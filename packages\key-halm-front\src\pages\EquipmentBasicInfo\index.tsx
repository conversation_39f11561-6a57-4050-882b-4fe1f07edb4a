/**
 * @Description: 销售发运平台 - 入口页面
 * @Author: <EMAIL>
 * @Date: 2022/2/10 15:08
 */
import React from 'react';
import { Button, DataSet, Table, Switch, Modal } from 'choerodon-ui/pro';
import { Badge } from "choerodon-ui";
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import { Content, Header } from 'components/Page';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { lineTableDS, tableDS } from "./stores";

const modelPrompt = 'alm.equipmentBasicInfo';

const EquipmentBasicInfo = observer(props => {
  const { tableDs, lineTableDs } = props;

  const handleCancel = (dataSet, record) => {
    if(record.status === 'add') {
      dataSet.remove([record])
    } else {
      record.reset();
      record.setState('editing', false);
    }
  };

  const handleSubmit = async () => {
    const flag = await tableDs.validate();
    if(flag) {
      await tableDs.submit();
      tableDs.query()
    } 
  };

  const renderAction = (dataSet, record) => {
    if(!record?.getState('editing')){
      return (
        <a
          onClick={() => {
            record.setState('editing', true);
          }}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </a>
      )
    }
    return (
      <>
        <a onClick={() => handleCancel(dataSet, record)} style={{ marginRight: '0.1rem' }}>
          {intl.get('hzero.common.button.cancel').d('取消')}
        </a>
      </>
    )
  };


  const columns: ColumnProps[] = [
    {
      name: 'assetLov',
      editor: (record) => {
        return record.getState('editing') && record.status === 'add';
      },
      renderer: ({ record }) => (<a onClick={() => handleOpenStopTime(record)}>{ record.get('assetNum') }</a>),
    },
    { name: 'assetName' },
    { name: 'locationName' },
    { name: 'maintSiteDescription' },
    { name: 'standardCapacity', editor: (record) => record.getState('editing') },
    {
      name: 'enabledFlag',
      editor: (record) => record.getState('editing') && <Switch />,
      renderer: ({ record }) => (
        <Badge
          status={record.get('enabledFlag') !== 'N' ? 'success' : 'error'}
          text={
            record.get('enabledFlag') !== 'N'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      lock: ColumnLock.right,
      width: 180,
      align: ColumnAlign.center,
      title: intl.get('hzero.common.button.action').d('操作'),
      renderer: ({ record }) => renderAction(tableDs, record),
    },
  ];

  const lineColumns: ColumnProps[] = [
    { name: 'platHaltTime', editor: (record) => record.getState('editing') },
    { name: 'effectiveDate', editor: (record) => record.getState('editing') },
    { name: 'input', editor: (record) => record.getState('editing') },
    { name: 'output', editor: (record) => record.getState('editing') },
    { name: 'okQty', editor: (record) => record.getState('editing') },
    {
      lock: ColumnLock.right,
      width: 180,
      align: ColumnAlign.center,
      title: intl.get('hzero.common.button.action').d('操作'),
      renderer: ({ record }) => renderAction(lineTableDs, record),
    },
  ];

  const handleOpenStopTime = (record) => {
    lineTableDs.setQueryParameter("assetDataIds", [record.get('assetDataId')]);
    lineTableDs.query();
    Modal.open({
      title: (
        <div>
          <span style={{ fontSize: '14px' }}>
            {intl.get(`${modelPrompt}.title.materialLotDetail`).d('停机时长维护')}
          </span>
          <div
            style={{
              float: 'right',
              display: 'flex',
              flexDirection: 'row-reverse',
              alignItems: 'center',
              marginRight: '0.3rem',
            }}
          >
            <Button
              color={ButtonColor.primary}
              onClick={handleCreateLine}
              icon="add"
            >
              {intl.get(`${modelPrompt}.button.create`).d('新建')}
            </Button>
            <Button
              color={ButtonColor.primary}
              onClick={handleSaveLine}
              icon="save"
              style={{ marginRight: '12px' }}
            >
              {intl.get(`${modelPrompt}.button.save`).d('保存')}
            </Button>
          </div>
        </div>
      ),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <Table
          dataSet={lineTableDs}
          columns={lineColumns}
        />
      ),
      footer: null,
    });
  };

  const handleSaveLine = async () => {
    const flag = await lineTableDs.validate();
    if(flag) {
      await lineTableDs.submit()
      lineTableDs.query();
    }
  };

  const handleImport = () => {
    openTab({
      key: `/aafm/equipment-basic-info/import/AATN.ASSET_DATA`,
      title: intl.get(`${modelPrompt}.title.equipmentBasicInfoImport`).d('设备基础数据维护导入'),
      search: queryString.stringify({
        action: intl.get(`${modelPrompt}.title.equipmentBasicInfoImport`).d('设备基础数据维护导入'),
      }),
    });
  };

  const handleCreate = () => {
    const record = tableDs.create({}, 0);
    record.setState('editing', true);
  };

  const handleCreateLine = () => {
    const record = lineTableDs.create({
      assetDataId: tableDs.current?.get('assetDataId'),
    }, 0);
    record.setState('editing', true);
  };


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.equipmentBasicInfo`).d('设备基础数据维护')}>
        <Button
          color={ButtonColor.default}
          onClick={handleImport}
          icon="file_upload"
        >
          {intl.get(`${modelPrompt}.button.import`).d('导入')}
        </Button>
        <Button
          color={ButtonColor.primary}
          onClick={handleCreate}
          icon="add"
        >
          {intl.get(`${modelPrompt}.button.create`).d('新建')}
        </Button>
        <Button
          color={ButtonColor.primary}
          onClick={handleSubmit}
          icon="save"
        >
          {intl.get(`${modelPrompt}.button.save`).d('保存')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDs}
          columns={columns}
          highLightRow
          showCachedSelection={false}
          queryBar={TableQueryBarType.filterBar}
        />
      </Content>
    </div>
  );
});

export default flow(
  formatterCollections({ code: ['hmes.processYield', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({ ...tableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        tableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(EquipmentBasicInfo);
