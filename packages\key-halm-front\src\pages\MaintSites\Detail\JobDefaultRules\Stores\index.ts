/**
 * 默认维护规则-ds
 * @date 2021-12-29
 * <AUTHOR> <<EMAIL>>
 * @version 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import { isUndefined, isNull } from 'lodash';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { AxiosRequestConfig } from 'axios';
import { HALM_ORI } from 'alm/utils/config';
import getLang from '../../../Langs';

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ORI}/v1/${organizationId}`;

const sortDS: () => DataSetProps = () => ({
  selection: false,
  paging: false,
  fields: [
    {
      name: 'value',
      type: FieldType.string,
    },
    {
      name: 'meaning',
      type: FieldType.string,
    },
    {
      name: 'flag',
      type: FieldType.boolean,
      trueValue: 1,
      falseValue: 0,
      defaultValue: 0,
    },
  ],
});
const defaultMaintainerRuleDS: () => DataSetProps = () => ({
  selection: false,
  paging: false,
  fields: [
    {
      label: getLang('POSITION_TYPE'),
      name: 'positionType',
      type: FieldType.string,
      lookupCode: 'AORI.POSITION_TYPE',
      required: true,
    },
    {
      label: getLang('SERVICE_TYPE'),
      name: 'serviceTypes',
      type: FieldType.string,
      multiple: true,
      lookupCode: 'AORI.SERVICE_TYPE',
      required: true,
      dynamicProps: {
        disabled: ({ record }) =>
          isUndefined(record.get('positionType')) || isNull(record.get('positionType')),
      },
    },
    {
      label: getLang('STAFF_DEFAULT_RULE'),
      name: 'defaultStaffRules',
      type: FieldType.string,
      required: true,
      lookupCode: 'AORI.DEFAULT_STAFF_RULE',
      dynamicProps: {
        disabled: ({ record }) =>
          isUndefined(record.get('positionType')) || isNull(record.get('positionType')),
      },
    },
  ],
  transport: {
    read: ({ dataSet }): AxiosRequestConfig => {
      return {
        method: 'GET',
        url: `${apiPrefix}/default-staff-rules`,
        data: {
          maintSiteId: dataSet?.getState('moduleId'),
        },
      };
    },
    submit: ({ data }): AxiosRequestConfig => {
      return {
        method: 'POST',
        url: `${apiPrefix}/default-staff-rules`,
        data: data[0],
      };
    },
    update: ({ data }): AxiosRequestConfig => {
      return {
        data: data[0],
        method: 'PUT',
        url: `${apiPrefix}/default-staff-rules`,
      };
    },
    destroy: ({ data }): AxiosRequestConfig => {
      const url = `${apiPrefix}/default-staff-rules`;
      return {
        url,
        method: 'DELETE',
        data: data[0],
      };
    },
  },
});

export { sortDS, defaultMaintainerRuleDS };
