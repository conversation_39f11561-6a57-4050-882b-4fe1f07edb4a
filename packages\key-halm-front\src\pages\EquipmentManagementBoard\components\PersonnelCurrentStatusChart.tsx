import React, { useEffect ,useRef} from 'react';
import { Row, Col } from "choerodon-ui/pro";
import classNames from 'classnames';
import intl from 'utils/intl';
import DashboardCard from './DashboardCard.js';
import styles from '../index.module.less';

const modelPrompt = 'tarzan.wms.DeliverySignage';

const PersonnelCurrentStatusChart = ({ data }) => {
  const scrollContainerRef = useRef(null);

  const getClass = (count) => {
    if (count === 0) {
      return classNames(styles['person-status-content'], styles['person-status-content-normal'])
    }
    return classNames(styles['person-status-content'], styles['person-status-content-warning']);
  }
  useEffect(() => {  
    const scrollContainer = scrollContainerRef.current;  
    if (scrollContainer) {  
      const scrollStep = 20; // 每次滚动的像素数  
      const scrollInterval = 2000; // 滚动的间隔时间（毫秒）  
      let scrollPosition = 0;  
      const buffer = 50; // 在滚动到底部之前开始回滚的缓冲区（像素）  
    
      const scrollContent = () => {  
        scrollPosition += scrollStep;  
        if (scrollPosition >= scrollContainer.scrollHeight - scrollContainer.clientHeight - buffer) {  
          // 当滚动到接近底部时，开始从头滚动  
          scrollPosition = -scrollStep; // 设置为负值以确保下一次循环时从头开始  
        }  
        // 使用 requestAnimationFrame 来确保平滑滚动  
        window.requestAnimationFrame(() => {  
          scrollContainer.scrollTop = scrollPosition;  
        });  
      };  
    
      const scrollIntervalId = setInterval(scrollContent, scrollInterval);  
    
      // 清除定时器，防止内存泄漏  
      return () => clearInterval(scrollIntervalId);  
    }  
  }, []); // 空依赖数组表示这个effect只在组件挂载时运行一次
  return (
    <DashboardCard style={{ height: '100%' }} >
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-bottom-chart-title']}>
          {intl.get(`${modelPrompt}.title.personnelCurrentStatusChart`).d('人员当前状态')}
        </div>
        <div className={styles['workOrder-info']}
          ref={scrollContainerRef}
          style={{ overflowY: 'auto', height: 'calc(100% - 10%)' }}>
          <Row style={{ width: '100%' }}>
            {
              data.map(e => (
                <Col span={4}>
                  <div className={getClass(e.count)}>
                    {e.name}：{e.count}
                  </div>
                </Col>
              ))
            }
          </Row>
        </div>
      </div>
    </DashboardCard>
  );
};

export default PersonnelCurrentStatusChart;
