/**
 * 默认维护人员-组件
 * @date 2021/12/29
 * <AUTHOR>
 * @copyright Copyright (c) 2021, Hand
 */
import React, { FC, useEffect, useMemo } from 'react';
import { Button, DataSet, Table, Select, Lov } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { TableColumnTooltip } from 'choerodon-ui/pro/lib/table/enum';
import { useDataSet } from 'utils/hooks';
import { operatorRender } from 'utils/renderer';

import EmployeesLov from 'alm/components/EmployeesLov';
import getLang from './Langs';
import { defaultMaintainerDS } from './Stores';

interface Props {
  moduleId: number | string;
  moduleName: string;
  editFlag?: boolean;
}

const DefaultMaintainer: FC<Props> = props => {
  const { editFlag, moduleId, moduleName } = props;
  const tableDs = useDataSet(() => new DataSet(defaultMaintainerDS()));
  tableDs.setState('moduleId', moduleId);
  tableDs.setState('moduleName', moduleName);

  useEffect(() => {
    // 如果页面变成不可编辑,重置所有修改
    if (!editFlag) {
      tableDs.reset();
      handleCancelEdit();
    }
  }, [editFlag]);

  useEffect(() => {
    tableDs.query();
  }, []);

  const handleCreate = () => {
    if (shouldStop()) {
      return;
    }
    const initData: any = {
      sourceId: moduleId,
      sourceType: moduleName,
    };
    if (moduleName === 'MAINT_SITE') {
      initData.maintSiteId = moduleId;
    }
    tableDs.create(initData);
    if (tableDs.current) {
      tableDs.current.setState('editing', true);
    }
  };

  const handleEdit = record => {
    if (shouldStop()) {
      return;
    }
    // eslint-disable-next-line
    record.status = 'update';
    record.setState('editing', true);
  };

  const handleDelete = async record => {
    if (shouldStop()) {
      return;
    }
    try {
      await tableDs.delete(record);
      tableDs.loadData(tableDs.toData());
    } catch (error) {
      console.log(error);
    }
  };

  const handleSave = async record => {
    const validate = await record.validate();
    if (validate) {
      try {
        await tableDs.submit();
        tableDs.query();
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log(record.getState('editing'));
      record.setState('editing', false);
      record.setState('editing', true);
    }
  };

  const handleCancel = record => {
    if (record.status === 'add') {
      tableDs.remove(record);
    } else {
      record.reset();
      record.setState('editing', false);
    }
  };

  const handleWorkCenterChange = (e, record) => {
    if (record) {
      const positionType = record.get('positionType');
      record.set('workcenterId', e?.workCenterId);
      record.set('workcenterName', e?.workCenterName);
      record.set('staffId', e?.employeeId);
      record.set('staffName', e?.employeeName);
      if (e && positionType !== 'CHECKER') {
        record.set('maintSiteId', e?.maintSiteId);
        record.set('maintSiteName', e?.maintSiteName);
      }
    }
  };

  const handleWorkStaffChange = (e, record) => {
    if (record) {
      const positionType = record.get('positionType');
      if (e && positionType !== 'CHECKER') {
        record.set('workcenterId', e?.workCenterId);
        record.set('workcenterName', e?.workCenterName);
        record.set('maintSiteId', e?.maintSiteId);
        record.set('maintSiteName', e?.maintSiteName);
      }
      record.set('staffId', e?.employeeId);
      record.set('staffName', e?.employeeName);
    }
  };

  const handleMaintSiteChange = record => {
    const positionType = record.get('positionType');
    record.set('workcenterId');
    record.set('workcenterName');
    if (positionType !== 'CHECKER') {
      record.set('staffId');
      record.set('staffName');
    }
  };

  /**
   * 如果表格有正在编辑的行, 则不允许继续操作 新增/编辑
   * @returns boolean
   */
  const shouldStop = () => {
    return tableDs.some(i => i.getState('editing'));
  };

  /**
   * 取消所有在编辑的行
   */
  const handleCancelEdit = () => {
    tableDs.forEach(i => {
      if (i.getState('editing')) {
        i.setState('editing', false);
      }
    });
  };

  const handlePositionTypeChange = (value: String, oldValue: String, record) => {
    record.set('serviceTypes');
    if (value === 'CHECKER' || !value) {
      record.set('workcenterId');
      record.set('workcenterName');
      record.set('staffId');
      record.set('staffName');
    }
    if (oldValue === 'CHECKER') {
      record.set('staffId');
      record.set('staffName');
    }
  };

  const columns = useMemo(() => {
    const arr: ColumnProps[] = [
      {
        name: 'positionType',
        width: 100,
        editor: record =>
          record.getState('editing') && (
            <Select
              name="positionType"
              onChange={(value, oldValue) => handlePositionTypeChange(value, oldValue, record)}
            />
          ),
      },
      {
        name: 'serviceTypes',
        // 单据职务为负责人时, 不可选'委外申请单'和'服务申请单'
        tooltip: TableColumnTooltip.overflow,
        editor: record =>
          record.getState('editing') && (
            <Select
              optionsFilter={i => {
                const positionType = record.get('positionType');
                if (positionType === 'PRINCIPAL') {
                  return (
                    i.get('value') !== 'SUBCONTRACTING' && i.get('value') !== 'SERVICE_REQUEST'
                  );
                } else if (positionType === 'CHECKER') {
                  // 当单据职务为验收员时，业务单据限制为工作单（保养、技改大修）、工作单（故障维修类）
                  return ['FAULT_MAINTAIN_WO', 'FAILURE_WO'].includes(i.get('value'));
                } else {
                  return true;
                }
              }}
              name="serviceTypes"
            />
          ),
      },
      {
        name: 'maintSiteLov',
        width: 150,
        hidden: moduleName === 'MAINT_SITE',
        tooltip: TableColumnTooltip.overflow,
        editor: record =>
          record.getState('editing') && (
            <Lov onChange={() => handleMaintSiteChange(record)} name="maintSiteLov" />
          ),
      },
      {
        name: 'workcenterName',
        width: 150,
        tooltip: TableColumnTooltip.overflow,
        className: 'alm-table-cell',
        editor: record =>
          record.getState('editing') &&
          record.get('positionType') !== 'CHECKER' && (
            <EmployeesLov
              name="workcenterName"
              required
              queryParams={{
                maintSiteId: record.get('maintSiteId'),
              }}
              record={record}
              value={record.get('workcenterName')}
              onOk={e => handleWorkCenterChange(e, record)}
            />
          ),
      },
      {
        name: 'staffName',
        width: 100,
        tooltip: TableColumnTooltip.overflow,
        editor: record => {
          const positionType = record.get('positionType');
          return (
            record.getState('editing') &&
            (positionType === 'CHECKER' ? (
              <Lov name="staffName" onChange={e => handleWorkStaffChange(e, record)} />
            ) : (
              <EmployeesLov
                name="staffName"
                queryParams={{
                  maintSiteId: record.get('maintSiteId'),
                }}
                record={record}
                value={record.get('staffName')}
                onOk={e => handleWorkStaffChange(e, record)}
              />
            ))
          );
        },
      },
      {
        name: 'lastUpdateDate',
        width: 100,
      },
    ];
    if (editFlag) {
      arr.push({
        header: getLang('OPTION'),
        width: 120,
        renderer: rowDS => {
          const { record } = rowDS;
          const operators = !record!.getState('editing')
            ? [
                {
                  key: 'edit',
                  ele: <a onClick={() => handleEdit(record)}>{getLang('EDIT')}</a>,
                  len: 2,
                  title: getLang('EDIT'),
                },
                {
                  key: 'delete',
                  ele: <a onClick={() => handleDelete(record)}>{getLang('DELETE')}</a>,
                  len: 3,
                  title: getLang('DELETE'),
                },
              ]
            : [
                {
                  key: 'save',
                  ele: <a onClick={() => handleSave(record)}>{getLang('SAVE')}</a>,
                  len: 2,
                  title: getLang('Save'),
                },
                {
                  key: 'cancel',
                  ele: <a onClick={() => handleCancel(record)}>{getLang('CANCEL')}</a>,
                  len: 3,
                  title: getLang('CANCEL'),
                },
              ];
          return operatorRender(operators);
        },
      });
    }
    return arr;
  }, [editFlag]);

  return (
    <>
      <Button hidden={!editFlag} icon="add" color={ButtonColor.primary} onClick={handleCreate}>
        {getLang('CREATE')}
      </Button>
      <Table dataSet={tableDs} columns={columns} />
    </>
  );
};

export default DefaultMaintainer;
