import React, { Component } from 'react';
import {
  Form,
  TextField,
  Output,
  Lov,
  Select,
  DatePicker,
  TextArea,
  NumberField,
  IntlField,
  DataSet,
} from 'choerodon-ui/pro';
import { Collapse, Tabs, Icon } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';
import { isEmpty } from 'lodash';

import OrgPartnerLov from 'alm/components/OrgPartnerLov';

import getLang from '../Langs';
import ValueChangeTab from './ValueChangeTab';
import { faCategoryDS } from '../Stores/DetailDS';

class InfoExhibit extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() {
    this.handleSetTransferObject();
  }

  /**
   * 根据其他模块传递的对象设置默认值
   */
  @Bind()
  async handleSetTransferObject() {
    const { isNew, transferObject, detailDS } = this.props;
    const record = transferObject;
    if (isNew && record) {
      if (record.key === 'asset') {
        this.faCategoryDS = new DataSet(faCategoryDS());
        this.faCategoryDS.setQueryParameter('faCategoryId', record.faCategoryId);
        const faCategory = await this.faCategoryDS.query();
        detailDS.current.set('fixedAssetName', record.assetName); // 资产名称->名称
        detailDS.current.set('faOrgId', record.owningOrgId); // 所属组织->资产所属部门
        detailDS.current.set('faOrgName', record.owningOrgName);
        detailDS.current.set('assetId', record.assetId);
        detailDS.current.set('assetName', record.assetName);
        detailDS.current.set('assetDesc', record.assetDesc);
        detailDS.current.set('assetNum', record.assetNum);
        detailDS.current.set('costCenterCodeId', record.costCenterId); // 成本中心
        detailDS.current.set('costCenterCodeName', record.costCenterMeaning);
        detailDS.current.set('faCategoryId', record.faCategoryId);
        detailDS.current.set('faCategoryName', record.faCategoryName);
        detailDS.current.set('faDeprnRuleId', faCategory.faDeprnRuleId);
        detailDS.current.set('faDeprnRuleName', faCategory.faDeprnRuleName);
        detailDS.current.set('depreciationTypeCode', faCategory.depreciationTypeCode);
        detailDS.current.set('depreciationTypeCodeMeaning', faCategory.depreciationTypeCodeMeaning);
        detailDS.current.set('depreciationMouth', faCategory.depreciationMouth);
        detailDS.current.set('residualValueRate', faCategory.residualValueRate);
        detailDS.current.set('orgValDepreciationCode', faCategory.orgValDepreciationCode);
        detailDS.current.set('orgValDeptCodeMeaning', faCategory.orgValDeptCodeMeaning);
        detailDS.current.set('depreciationStartDate', faCategory.depreciationStartDate);
        // [TODO] 来源信息以后再加
        // detailDS.current.set('projectId', record.sourceProjectId); // 来源项目->项目
        // detailDS.current.set('projectName', record.sourceProjectName);
        // detailDS.current.set('contractId', record.sourceContractId); // 来源合同->合同
        // detailDS.current.set('contractName', record.sourceContractName);
        // detailDS.current.set('acceptanceSheetId', record.aosReceivingReportId); // 来源验收单->验收单
        // detailDS.current.set('acceptanceSheetName', record.aosReceivingReportName);
      }
    }
  }

  @Bind()
  handleFaCategoryChange(record) {
    const { detailDS } = this.props;
    if (!isEmpty(record)) {
      detailDS.current.set('faDeprnRuleId', record.faDeprnRuleId);
      detailDS.current.set('faDeprnRuleName', record.faDeprnRuleName);
      // 折旧类型
      detailDS.current.set('depreciationTypeCode', record.depreciationType);
      // 折旧月份
      detailDS.current.set('depreciationMouth', record.depreciationDate);
      // 残值率
      detailDS.current.set('residualValueRate', record.salvageRate);
      // 原值增减折旧规则
      detailDS.current.set('orgValDepreciationCode', record.depreciationRule);
      // 折旧起始日期
      detailDS.current.set('depreciationStartDate', record?.depreciationStartDate);
    }
  }

  // 改变资产所属部门-带出成本中心
  @Bind()
  handleOrgNameChange(record, type) {
    const { detailDS } = this.props;
    if (type === 'PLATFORM') {
      detailDS.current.set('faOrgId', record.unitId);
      detailDS.current.set('faOrgName', record.unitName);
    } else {
      detailDS.current.set('faOrgId', record.orgId);
      detailDS.current.set('faOrgName', record.orgName);
      if (record.costCenterId != null) {
        detailDS.current.set('costCenterCodeId', record.costCenterId);
        detailDS.current.set('costCenterCodeName', record.costCenterName);
      }
    }
    detailDS.current.set('faOrgType', type);
  }

  // 改变验收单，带出验收部门、人员，且可编辑
  @Bind()
  handleAcceptanceSheetChange(record) {
    const { detailDS } = this.props;
    if (!isEmpty(record)) {
      detailDS.current.set('acceptanceSheetId', record.orgId);
      detailDS.current.set('acceptanceSheetName', record.orgName);
      detailDS.current.set('acceptanceOrgId', 600021); // TODO 假数据
      detailDS.current.set('acceptanceOrgName', '盘点二组'); // TODO 假数据
      detailDS.current.set('acceptanceEmployeeId', 10); // TODO 假数据
      detailDS.current.set('acceptanceEmployeeName', '周一一'); // TODO 假数据
    }
  }

  // 改变折旧规则
  @Bind()
  handleChangeFaDeprnRule(record) {
    const { detailDS } = this.props;
    if (!isEmpty(record)) {
      const currentOriginalValue = detailDS.current.get('currentOriginalValue');
      const originalMonthValue = detailDS.current.get('originalMonthValue');
      const initialOriginalValue = detailDS.current.get('initialOriginalValue');
      const tmp =
        originalMonthValue || originalMonthValue === 0 ? originalMonthValue : currentOriginalValue;
      let residualValue = detailDS.current.get('residualValue'); // 残值
      let deprnAccountLastMonth = detailDS.current.get('deprnAccountLastMonth'); // 上月折旧金额
      if (
        (record.salvageRate || record.salvageRate === 0) &&
        (currentOriginalValue || currentOriginalValue === 0)
      ) {
        residualValue = ((initialOriginalValue * record.salvageRate) / 100).toFixed(2); // 残值
        if (record.depreciationRule === 'CURRENT_MONTH' && record.depreciationDate) {
          deprnAccountLastMonth = (
            ((1 - record.salvageRate / 100) / record.depreciationDate) *
            currentOriginalValue
          ).toFixed(2);
        }
      }
      if (record.depreciationRule === 'NEXT_MONTH' && record.depreciationDate) {
        deprnAccountLastMonth = (
          ((1 - record.salvageRate / 100) / record.depreciationDate) *
          tmp
        ).toFixed(2);
      }
      detailDS.current.set('faDeprnRuleId', record.faDeprnRuleId);
      detailDS.current.set('faDeprnRuleName', record.ruleName);
      detailDS.current.set('depreciationTypeCode', record.depreciationType);
      detailDS.current.set('depreciationMouth', record.depreciationDate);
      detailDS.current.set('residualValueRate', record.salvageRate);
      detailDS.current.set('orgValDepreciationCode', record.depreciationRule);
      detailDS.current.set('residualValue', residualValue);
      detailDS.current.set('deprnAccountLastMonth', deprnAccountLastMonth);
    }
  }

  render() {
    const { isNew, editFlag, currentId, history, detailDS, activeKey, onTabsChange } = this.props;
    const { solidStateCode } = detailDS && detailDS.current ? detailDS.current.toData() : {};
    const valueChangeProps = {
      history,
      currentId,
      detailDS,
    };
    return (
      <React.Fragment>
        {/* tab 面板 */}
        <Tabs activeKey={activeKey} onChange={onTabsChange} defaultActiveKey="basicTab">
          <Tabs.TabPane tab={getLang('BASIC')} key="basicTab">
            <Collapse bordered={false} defaultActiveKey={['A', 'B', 'C', 'D', 'E']}>
              <Collapse.Panel key="A" header={getLang('BASIC_INFO')}>
                {isNew || editFlag ? (
                  <>
                    <Form dataSet={detailDS} labelWidth={120} columns={3}>
                      <Lov name="faCategoryLov" onChange={this.handleFaCategoryChange} />
                      <IntlField
                        name="fixedAssetName"
                        suffix={<Icon type="language" style={{ color: '#bfbfbf' }} />}
                      />
                      <TextField name="financialNum" />
                      <Lov name="accountBookNameLov" />
                      <OrgPartnerLov name="faOrgName" handleOk={this.handleOrgNameChange} />
                      <Lov name="costCenterLov" />
                      <Lov name="assetLov" />
                      <TextField name="assetNum" />
                      <IntlField
                        name="description"
                        suffix={<Icon type="language" style={{ color: '#bfbfbf' }} />}
                        colSpan={3}
                        newLine
                      />
                    </Form>
                  </>
                ) : (
                  <Form dataSet={detailDS} labelWidth={120} columns={3}>
                    <Output name="faCategoryName" />
                    <Output name="fixedAssetName" />
                    <Output name="financialNum" />
                    <Output name="accountBookName" />
                    <Output name="faOrgName" />
                    <Output name="costCenterCodeName" />
                    <Output name="assetLov" />
                    <Output name="assetNum" />
                    <Output name="description" newLine colSpan={3} />
                  </Form>
                )}
              </Collapse.Panel>
              <Collapse.Panel key="B" header={getLang('TRANSFER_INFO')}>
                {isNew || editFlag ? (
                  <Form dataSet={detailDS} labelWidth={120} columns={3}>
                    <Select name="solidStateCode" />
                    <DatePicker name="transferDate" />
                    <Lov name="transferEmpLov" />
                    <Lov name="accountingEmpLov" />
                    <TextField name="postingAccountType" />
                    <TextField name="accountEntry" />
                    <TextField name="accountingVoucherNumber" />
                    <DatePicker name="accountEntryDate" />
                    <TextArea name="conversionRemarks" newLine colSpan={3} />
                  </Form>
                ) : (
                  <Form dataSet={detailDS} labelWidth={120} columns={3}>
                    <Output name="solidStateCode" />
                    <Output name="transferDate" />
                    <Output name="transferEmployeeName" />
                    <Output name="accountingeEmployeeName" />
                    <Output name="postingAccountType" />
                    <Output name="accountEntry" />
                    <Output name="accountingVoucherNumber" />
                    <Output name="accountEntryDate" />
                    <Output name="conversionRemarks" newLine colSpan={3} />
                  </Form>
                )}
              </Collapse.Panel>
              {/* 先隐藏来源信息，6月份版本先不上 */}
              {true ? (
                ''
              ) : (
                <Collapse.Panel key="C" header={getLang('SOURCE_INFO')}>
                  {isNew || editFlag ? (
                    <Form dataSet={detailDS} labelWidth={120} columns={3}>
                      <Lov name="projectLov" />
                      <Lov name="contractLov" />
                      <Lov name="acceptSheetLov" onChange={this.handleAcceptanceSheetChange} />
                      <Lov name="acceptOrgLov" />
                      <Lov name="acceptEmpLov" />
                    </Form>
                  ) : (
                    <Form dataSet={detailDS} labelWidth={120} columns={3}>
                      <Output name="projectName" />
                      <Output name="contractName" />
                      <Output name="acceptanceSheetName" />
                      <Output name="acceptanceOrgName" />
                      <Output name="acceptanceEmployeeName" />
                    </Form>
                  )}
                </Collapse.Panel>
              )}
              <Collapse.Panel key="D" header={getLang('FADEPRN_METHODS')}>
                {isNew || editFlag ? (
                  <Form dataSet={detailDS} labelWidth={120} columns={3}>
                    <Lov name="faDepreRuleLov" onChange={this.handleChangeFaDeprnRule} />
                    <Output name="depreciationTypeCode" />
                    <Output name="depreciationMouth" />
                    <Output name="residualValueRate" />
                    <Output name="orgValDepreciationCode" />
                    <DatePicker name="depreciationStartDate" />
                  </Form>
                ) : (
                  <Form dataSet={detailDS} labelWidth={120} columns={3}>
                    <Output name="faDeprnRuleName" />
                    <Output name="depreciationTypeCode" />
                    <Output name="depreciationMouth" />
                    <Output name="residualValueRate" />
                    <Output name="orgValDepreciationCode" />
                    <Output name="depreciationStartDate" />
                  </Form>
                )}
              </Collapse.Panel>
              <Collapse.Panel key="E" header={getLang('FIXED_ASSET_VALUES')}>
                {isNew || editFlag ? (
                  <Form dataSet={detailDS} labelWidth={120} columns={3}>
                    <Select name="currencyCode" />
                    <NumberField name="initialOriginalValue" />
                    <Output name="currentOriginalValue" />
                    <Output name="originalMonthValue" />
                    <NumberField name="accumulatedDepreciation" />
                    <NumberField name="ytdDepreciation" />
                    <NumberField name="netValue" />
                    <NumberField name="residualValue" />
                    {!isNew && <NumberField name="deprnAccountLastMonth" />}
                  </Form>
                ) : (
                  <Form dataSet={detailDS} labelWidth={120} columns={3}>
                    <Output name="currencyCode" />
                    <Output name="initialOriginalValue" />
                    <Output name="currentOriginalValue" />
                    <Output name="originalMonthValue" />
                    <Output name="accumulatedDepreciation" />
                    <Output name="ytdDepreciation" />
                    <Output name="netValue" />
                    <Output name="residualValue" />
                    <Output name="deprnAccountLastMonth" />
                  </Form>
                )}
              </Collapse.Panel>
            </Collapse>
          </Tabs.TabPane>
          {isNew || solidStateCode !== 'COMPLETED' ? null : (
            <Tabs.TabPane tab={getLang('VALUE_TAB')} key="valueTab">
              <Collapse bordered={false} defaultActiveKey={['F']}>
                <Collapse.Panel key="F" header={getLang('VALUE_TAB')}>
                  <ValueChangeTab {...valueChangeProps} />
                </Collapse.Panel>
              </Collapse>
            </Tabs.TabPane>
          )}
        </Tabs>
      </React.Fragment>
    );
  }
}

export default InfoExhibit;
