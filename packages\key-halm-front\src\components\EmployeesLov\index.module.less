@TREEBOXWIDTH: 220px;
@TREEBOXHEIGHT: 400px;
@BLUECOLOR: #3889ff;

:global {
  .c7n-pro-input-suffix {
    height: 100%;
    border-left: 1px solid #e6e6e6;

    .icon-search {
      &::before {
        content: '\e769';
        font-family: iconfont-c7n-font, sans-serif;
      }
    }
  }

  .ant-input-search {
    input {
      height: 26px !important;
    }

    i {
      margin-top: -1px;
      font-size: 16px !important;
    }
  }

  // 技能气泡框
  .c7n-popover-inner-content {
    padding: 5px;

    .card-popover-more {
      span {
        margin-right: 10px;
        padding: 1px 5px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .c7n-spin {
    color: @BLUECOLOR;
  }

  // 在表格中的编辑样式
  .c7n-pro-table-editor .alm-table-cell {
    top: -1px;
    left: -1px;
    width: 1.43rem !important;

    .employee-field.c7n-pro-input-required {
      top: 1px;
      left: 1px;
      width: calc(100% - 2px) !important;

      label input {
        height: 34px !important;
      }
    }
  }
  // .employee-field {
  //   height: max-content;
  // }
}

.tree-box {
  width: @TREEBOXWIDTH;
  height: @TREEBOXHEIGHT;

  :global {
    .c7n-tabs-bar {
      margin: 0;
      background-color: #f3f3f3;
      border: 1px solid rgba(217, 217, 217, 1);
      border-top-right-radius: 2px;
      border-top-left-radius: 2px;
      border-bottom: none;

      .c7n-tabs-nav-container {
        padding: 0 !important;

        .c7n-tabs-nav {
          width: 100% !important;

          .c7n-tabs-tab {
            justify-content: center;
            width: 50% !important;
            margin-right: 0 !important;
            margin-top: 0 !important;

            &:not(:last-of-type)::after {
              position: absolute;
              right: 0;
              bottom: auto !important;
              display: block !important;
              content: ' ';
              width: 1px;
              height: 20px !important;
              border-right: 1px solid rgba(217, 217, 217, 1);
            }

            .c7n-badge {
              font-weight: 600;
            }

            .c7n-badge[aria-selected='false'] {
              color: #262626;
            }
          }

          .c7n-tabs-ink-bar {
            // width: 50% !important;
            // transform: translate3d(0, 0,0);
          }
        }
      }
    }

    .c7n-pro-table {
      border-top: none !important;

      // 处理表格无数据图片不居中显示
      .c7n-pro-table-empty-row {
        position: relative;
        left: 105px;
      }
    }
  }

  .halm-tree-table {
    width: 100%;
    height: inherit;

    :global {
      .c7n-pro-table .c7n-pro-table-content .c7n-pro-table-cell {
        .c7n-pro-table-expand-icon,
        .c7n-pro-table-expand-icon-expanded {
          border-color: transparent;
          border: none !important;
        }

        .c7n-pro-table-expand-icon::before {
          content: '\E315' !important;
          font-size: 0.16rem;
          /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
          font-family: 'icomoon';
        }

        .c7n-pro-table-expand-icon-expanded::after {
          content: '\E315' !important;
          font-size: 0.16rem;
          color: #595959;
          /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
          font-family: 'icomoon';
          position: static !important;
          border-top: none !important;
          top: 0 !important;
          left: 0 !important;
          height: auto !important;
        }

        .c7n-pro-table-expand-icon-expanded {
          transform: rotate(90deg) !important;
        }
      }

      .c7n-pro-table-toolbar {
        display: none;
      }

      .c7n-pro-table-thead {
        display: none;
      }

      .c7n-pro-table-body {
        height: 350px !important;
        max-height: 350px !important;

        table {
          width: max-content !important; // 工作中心树字段不省略
          min-width: 100%;
        }
      }

      .c7n-pro-table-expanded-row {
        visibility: collapse !important;
      }

      .c7n-pro-table-cell-prefix {
        margin-right: 0 !important;
        padding-right: 0 !important;
      }

      .c7n-pro-table-cell-inner {
        padding-left: 8px !important;
      }
    }
  }
}

// employee card start
.employee-cards,
.employee-cards-cover {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  min-height: @TREEBOXHEIGHT;
  margin-right: 10px;
  margin-left: 10px;

  :global {
    .c7n-pro-pagination {
      width: 100%;
      text-align: right;
    }
  }
}

// 员工卡片样式提取到外层，Card组件复用时可获取到样式
.card-selected {
  position: relative;
  border: 1px solid @BLUECOLOR !important;
  box-shadow: none;

  &::after {
    position: absolute;
    top: 0;
    right: -1px;
    width: 23px;
    height: 23px;
    background-image: url('./assets/selected.png');
    background-repeat: space;
    background-position: 1px -1px;
    background-size: 23px;
    content: '';
  }
}

.employee-card {
  box-sizing: border-box;
  background: #fff;
  border: 1px solid #fff;
  border-radius: 2px;
  margin: 6px;
  padding: 12px;
  overflow: hidden;
  color: #333;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);

  .employee-name {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    span {
      width: 50%;
      margin-bottom: 5px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #262626;
      font-weight: 500;
      font-size: 14px;

      &:last-child {
        text-align: right;
      }
    }
  }

  .employee-line {
    display: inline-flex;
    width: 100%;
    min-height: 18px;
    font-size: 12px;

    span {
      font-size: 12px;
      color: #4d4d4d;
      font-weight: 400;

      &:last-child {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .employee-skills {
    min-height: 18px;
    margin-top: 5px;

    .skills-normal {
      width: calc(100% - 20px);
      height: 18px;
      overflow: hidden;

      span {
        margin-right: 10px;
        padding: 1px 5px;

        &:nth-child(5n) {
          margin-right: 0;
        }
      }
    }

    .card-more {
      position: absolute;
      right: 0;
      bottom: 0;

      &::after {
        width: 20px;
        height: 20px;
        content: '...';
      }
    }
  }
}

// 设为全局样式，方便重写卡片的宽度
:global {
  .employee-card-50 {
    width: calc(50% - 12px);
  }

  .employee-card-33 {
    width: calc(33% - 8px);
  }
}

.employee-cards {
  width: calc(100% - @TREEBOXWIDTH - 20px);
}

.employee-cards-cover {
  width: calc(100% - 20px);
}

// employee card end
