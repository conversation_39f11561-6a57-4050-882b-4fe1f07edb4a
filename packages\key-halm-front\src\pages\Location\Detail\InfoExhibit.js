import React from 'react';
import { Lov, Form, Cascader, Switch, Output, TextField, IntlField } from 'choerodon-ui/pro';
import { Tabs, Spin, Collapse } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';
import { isUndefined, isObject, isFunction } from 'lodash';

import intl from 'utils/intl';
import { yesOrNoRender } from 'utils/renderer';

import { DefaultMaintainer } from 'alm/components/DefaultMaintainer';
import { EDIT_DEFAULT_CLASSNAME, DETAIL_DEFAULT_CLASSNAME } from 'alm/utils/constants';

// import LocationSearch from 'alm/components/LocationSearch';
// import GeoMap from 'alm/components/GeoMap';

import MeterTable from './MeterTable';

class InfoExhibit extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      regionValue: [], // 省份/城市/区县 的值
    };
  }

  // 改变 省份/城市/区县 的显示
  @Bind()
  handleChangeRegionValue(data) {
    this.setState({
      regionValue: data,
    });
  }

  @Bind
  handleCollapseChange(keys) {
    this.props.onSetState({
      collapseActiveKey: keys,
    });
  }

  render() {
    const {
      id,
      infoDS,
      options,
      editFlag,
      meterTableDS,
      regionLoading,
      onLinkToMeter,
      onCountryChange,
      disabledMaintSite,
      onDisabledMaintSite,
      collapseActiveKey,
      directMaintainFlag,
    } = this.props;
    const viewPrompt = 'amdm.location.view';
    const meterProps = {
      meterTableDS,
      locationId: id,
      onLinkToMeter,
    };
    const isNew = isUndefined(id); // 当前是否是新增

    const searchProps = {
      isNew,
      isEdit: editFlag,
      ds: infoDS,
      isString: options.length > 0 && typeof options[0].regionId === 'string', // 判断是否开启雪花ID
      fieldName: 'address',
      onChangeRegionValue: this.handleChangeRegionValue,
    };

    const defaultMaintainerProps = {
      editFlag,
      moduleId: id,
      moduleName: 'LOCATION',
    };

    return (
      <div className={isNew || editFlag ? EDIT_DEFAULT_CLASSNAME : DETAIL_DEFAULT_CLASSNAME}>
        <Tabs defaultActiveKey="basicTab">
          <Tabs.TabPane tab={intl.get(`${viewPrompt}.tab.basicTab`).d('基本')} key="basicTab">
            <Collapse
              bordered={false}
              activeKey={collapseActiveKey}
              onChange={this.handleCollapseChange}
              className="form-collapse"
            >
              <Collapse.Panel
                key="key-1"
                header={intl.get('alm.common.view.baseInfo').d('基本信息')}
              >
                {isNew || editFlag ? (
                  <Form dataSet={infoDS} labelLayout="horizontal" columns={3} className="edit-row">
                    <Lov name="maintSiteLov" disabled={disabledMaintSite} />
                    <Lov name="locationTypeLov" />
                    <IntlField name="locationName" />
                    <TextField name="locationCode" disabled={!isNew} />
                    <Lov
                      name="locationLov"
                      onChange={value => {
                        // 选择父位置节点后，将当前位置的服务区域置为父位置的服务区域 且服务区域不可更改，移除父位置后，服务区域可更改。
                        if (isObject(value)) {
                          infoDS.current.set('maintSiteId', value && value.maintSiteId);
                          infoDS.current.set('maintSiteName', value && value.maintSiteName);
                        } else {
                          infoDS.current.set('maintSiteLov', null);
                        }
                        if (isFunction(onDisabledMaintSite)) {
                          onDisabledMaintSite(isObject(value));
                        }
                      }}
                    />
                    <Switch name="enabledFlag" />
                    <IntlField newLine colSpan={3} name="description" />
                  </Form>
                ) : (
                  <Form dataSet={infoDS} labelLayout="horizontal" columns={3} className="read-row">
                    <Output name="maintSiteName" />
                    <Output name="locationTypeName" />
                    <Output name="locationName" />
                    <Output name="locationCode" />
                    <Output name="parentLocationName" />
                    <Output name="enabledFlag" renderer={({ value }) => yesOrNoRender(value)} />
                    <Output newLine colSpan={3} name="description" />
                  </Form>
                )}
              </Collapse.Panel>
              <Collapse.Panel
                key="key-2"
                header={intl.get('alm.common.view.functionProperty').d('功能属性')}
              >
                {isNew || editFlag ? (
                  <Form dataSet={infoDS} labelLayout="horizontal" columns={3} className="edit-row">
                    <Switch name="assetLocationFlag" />
                    <Switch name="directMaintainFlag" />
                  </Form>
                ) : (
                  <Form dataSet={infoDS} labelLayout="horizontal" columns={3} className="read-row">
                    <Output
                      name="assetLocationFlag"
                      renderer={({ value }) => yesOrNoRender(value)}
                    />
                    <Output
                      name="directMaintainFlag"
                      renderer={({ value }) => yesOrNoRender(value)}
                    />
                  </Form>
                )}
              </Collapse.Panel>
              {directMaintainFlag && (
                <Collapse.Panel
                  key="key-3"
                  header={intl.get(`amdm.location.panel.trackAndManage`).d('成本对象')}
                >
                  {isNew || editFlag ? (
                    <Form dataSet={infoDS} labelWidth={120} columns={3}>
                      <Lov name="costCenterLov" />
                      <Lov name="internalOrderLov" />
                      <Lov name="wbsElementLov" />
                    </Form>
                  ) : (
                    <Form dataSet={infoDS} labelWidth={120} columns={3}>
                      <Output name="costCenterLov" />
                      <Output name="internalOrderLov" />
                      <Output name="wbsElementLov" />
                    </Form>
                  )}
                </Collapse.Panel>
              )}
              <Collapse.Panel
                key="key-4"
                header={intl.get('alm.common.view.addressAndContact').d('地址与联系方式')}
              >
                {isNew || editFlag ? (
                  <Spin spinning={regionLoading}>
                    <Form
                      dataSet={infoDS}
                      labelLayout="horizontal"
                      columns={3}
                      className="edit-row"
                    >
                      <Lov name="countryLov" onChange={onCountryChange} />
                      <Cascader
                        name="region"
                        expandTrigger="hover"
                        // defaultValue={text.split(',').map(Number)}
                        options={options}
                        // onChange={data => infoDS.current.set('region', data)}
                        onChange={(data, record) => {
                          infoDS.current.set('region', data);
                          infoDS.current.set(
                            'regionName',
                            record.map(item => item.regionName).join('/')
                          );
                          this.setState({
                            regionValue: data,
                          });
                        }}
                        value={this.state.regionValue}
                      />
                      <TextField name="addressContact1" />
                    </Form>
                  </Spin>
                ) : (
                  <Form dataSet={infoDS} labelLayout="horizontal" columns={3} className="read-row">
                    <Output name="countryName" />
                    <Output name="regionName" />
                    <Output name="addressContact1" />
                  </Form>
                )}
                {/* <LocationSearch {...searchProps} /> */}
              </Collapse.Panel>
            </Collapse>
          </Tabs.TabPane>
          <Tabs.TabPane
            key="meters"
            disabled={isNew}
            tab={intl.get(`${viewPrompt}.title.meters`).d('仪表点')}
          >
            <MeterTable {...meterProps} />
          </Tabs.TabPane>
          {isNew ? null : (
            <Tabs.TabPane
              tab={intl.get(`alm.component.tab.defaultMaintainer`).d('默认职务人员')}
              key="defaultMaintainer"
            >
              <DefaultMaintainer {...defaultMaintainerProps} />
            </Tabs.TabPane>
          )}
        </Tabs>
      </div>
    );
  }
}

export default InfoExhibit;
