import React, { useRef, useEffect, useMemo } from 'react';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import intl from 'utils/intl';
import DashboardCard from './DashboardCard';
import styles from '../index.module.less';

const modelPrompt = 'hmes.productionInfoBoard';


const ElectricInfo = ({ data }) => {

  const chartRef:any = useRef(null);
  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.clear()
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [data]);

  const option = useMemo(() => {
    const timeList = data.length > 0 ? data[0].subList.map(e => e.localTime) : [];
    console.log('data',data,'timeList',timeList)
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        textStyle: {
          color: '#fff',
        },
      },
      grid: {
        right: '4%',
        bottom: '8%',
      },
      xAxis: {
        type: 'category',
        splitLine: {
          show: false,
        },
        data: timeList,
        axisLabel: {
          color: '#fff',
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#fff',
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
      },
      series: data.map(e => {
        return {
          name: e.organizationName,
          type: 'line',
          data: e.subList.map(i => i.consumption),
          label: {
            show: true,
            position: 'top',
          },
        };
      }),
    }
  }, [data])
    return (
    <DashboardCard>
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-top-chart']}> 
          <div className={styles['my-scroll-board-title']} style={{ paddingBottom: '12px' }}>
            {intl.get(`${modelPrompt}.title.electricInfo`).d('电量变化/（天）')}
          </div>
          <div className={styles['my-chart']}>
            <div style={{ width: '100%', height: '100%' }}>
              <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
            </div>
          </div>
        </div>
      </div>
    </DashboardCard>
  );
};

export default ElectricInfo;