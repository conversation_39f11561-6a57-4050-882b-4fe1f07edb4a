/**
 * 资产模板-列表
 * @date 2023/4/18
 * <AUTHOR>
 * @copyright Copyright (c) 2023,Hand
 */
import React, { ReactNode, useCallback, useMemo } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { DataSet, Table, Button, Modal } from 'choerodon-ui/pro/lib';
import { ColumnProps, TableQueryBarType } from 'choerodon-ui/pro/lib/table/interface';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import { useDataSet } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { yesOrNoRender } from 'utils/renderer';
import getLang from '../Langs';
import { listDS } from '../Stores/listDS';
import { restoreDefault } from '../api';

const List: React.FC<RouteComponentProps> = ({ history }) => {
  const listDs = useDataSet(() => new DataSet(listDS()));

  const jumpToDetail = useCallback((code: string | null, editFlag: boolean = false) => {
    const pathname =
      code === null ? '/aori/asset-template/create' : `/aori/asset-template/detail/${code}`;
    history.push({
      pathname,
      state: {
        isEdit: editFlag,
      },
    });
  }, []);

  const handleRestore = async (code: string) => {
    const buttonClicked = await Modal.confirm({
      children: <span>{getLang('RESTORE_CONFIRM')}</span>,
    });
    if (buttonClicked === 'ok') {
      await restoreDefault(code);
      listDs.query();
    }
  };

  const columns = useMemo(
    (): ColumnProps[] => [
      {
        name: 'templateCode',
        renderer: ({ value }): ReactNode => {
          return <a onClick={() => jumpToDetail(value)}>{value}</a>;
        },
      },
      {
        name: 'templateName',
      },
      {
        name: 'objectTypeMeaning',
      },
      {
        name: 'scopeObjectSpliceName',
      },
      {
        name: 'enableFlag',
        renderer: ({ value }) => yesOrNoRender(Number(value)),
      },
      {
        header: getLang('OPTION'),
        renderer: ({ record }) => [
          <a onClick={() => jumpToDetail(record?.get('templateCode'), true)}>{getLang('EDIT')}</a>,
          <Button
            funcType={FuncType.link}
            style={{ margin: '0 0 0 16px' }}
            onClick={() => handleRestore(record?.get('templateCode'))}
          >
            {getLang('RESTORE_DEFAULT')}
          </Button>,
        ],
      },
    ],
    []
  );
  return (
    <PageHeaderWrapper
      title={getLang('TITLE')}
      header={
        <Button icon="add" color={ButtonColor.primary} onClick={() => jumpToDetail(null, true)}>
          {getLang('CREATE')}
        </Button>
      }
    >
      <Table
        key="assetTemplateList"
        queryBar={TableQueryBarType.filterBar}
        customizedCode="AORI.ASSET_TEMPLATE_LIST"
        searchCode="AORI.ASSET_TEMPLATE_LIST"
        dataSet={listDs}
        columns={columns}
        queryBarProps={{
          fuzzyQueryPlaceholder: getLang('KEYWORD_PLACEHOLDER'),
          queryFieldsLimit: 4,
        }}
      />
    </PageHeaderWrapper>
  );
};

export default formatterCollections({
  code: ['aori.assetTemplate', 'alm.common', 'alm.component'],
})(List);
