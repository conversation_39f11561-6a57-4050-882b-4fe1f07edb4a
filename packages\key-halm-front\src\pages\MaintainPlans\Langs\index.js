import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLangs = key => {
  const PREFIX = 'aori.maintainPlans';
  const MODELPREFIX = `${PREFIX}.model.maintainPlans`;
  const LANGS = {
    ...getCommonLangs(),
    // message
    HEADER: intl.get(`${PREFIX}.view.message.title`).d('维保计划'),
    CREATE_PLAN: intl.get(`${PREFIX}.view.message.createPlan`).d('新建维保计划'),
    DEL_CONFIRM: intl.get(`${PREFIX}.view.message.delConfirm`).d('确认删除'),
    CREATE_FROM_ACT: intl.get(`${PREFIX}.view.message.button.createFromAct`).d('引用标准作业'),
    SAVE_CONFIRM: intl
      .get(`${PREFIX}.view.message.saveConfirm`)
      .d('当前维保计划缺少维保步骤，保存后不可用于维护维保计划预测，是否保存？'),
    DEL_LAST_STEP: intl
      .get(`${PREFIX}.view.message.delLastStep`)
      .d('维保计划需至少具备一条维保步骤才可进行后续维保计划预测，确认删除？'),
    DEL_STEP: intl
      .get(`${PREFIX}.view.message.delStep`)
      .d('删除维保步骤行时会同步删除所关联物料及检查项信息'),
    NO_DATA: intl.get(`${PREFIX}.view.message.noData`).d('暂无内容'),
    QUERY_PLANS: intl.get(`${PREFIX}.view.message.queryPlans`).d('查询已生效维保'),
    IMPORT_MP: intl.get(`${PREFIX}.view.message.importMp`).d('维保计划导入'),
    PLAN_SCHED_BASE_TIME: intl
      .get(`${PREFIX}.view.message.planSchedBaseTime`)
      .d(
        '表示该维保计划的后续计划日期的基准是哪个时间，当选择最近标准时间时，无论实际执行延迟或提前，下次执行依旧按计划中标准时间按周期进行。选择最近作业时间，由于可配置“作业时间依据”，所以回写的最近作业时间有可能会因此提前或滞后，下次执行时间也会因此调整。选择最近作业时间后生成的工单在完工回写最近作业时间时，系统会自动运行一次当前维保计划所对应计划预测。'
      ),
    PLAN_SCHED_BASE_VALUE: intl
      .get(`${PREFIX}.view.message.planSchedBaseValue`)
      .d(
        '表示该维保计划的后续计划的基准读数来自哪个读数，当选择最近标准读数时，无论实际最近作业读数是多少，下次执行依旧按计划中最近标准读数按维保间隔进行。选择最近作业读数，由于读数回写规则存在，回写的最近作业读数有可能会因此提前或滞后，下次执行读数也会因此调整。选择最近作业读数后生成的工单在完工时，系统会自动运行一次当前维保计划所对应计划预测。'
      ),
    DAY: intl.get(`${PREFIX}.view.message.day`).d('天'),
    HOUR: intl.get(`${PREFIX}.view.message.hour`).d('小时'),
    LENGTH_RULE: intl.get(`${PREFIX}.view.message.lengthRule`).d('限制输入1~99整数'),
    KEYWORD_PLACEHOLDER: intl.get(`${PREFIX}.view.message.keywordPlaceholder`).d('请搜索关键字'),
    // model
    ASSET: intl.get(`${MODELPREFIX}.asset`).d('设备'),
    LOCATION: intl.get(`${MODELPREFIX}.location`).d('位置'),
    PLAN_NAME: intl.get(`${MODELPREFIX}.maintainPlanName`).d('维保计划名称'),
    CYCLE_TYPE: intl.get(`${MODELPREFIX}.cycleType`).d('周期类型'),
    MAINTAIN_WAY: intl.get(`${MODELPREFIX}.maintainWayCode`).d('维保方式'),
    CYCLE_INTERVAL: intl.get(`${MODELPREFIX}.cycleInterval`).d('周期间隔'),
    CYCLE_UOM: intl.get(`${MODELPREFIX}.cycleUom`).d('周期单位'),
    PLAN_CODE: intl.get(`${MODELPREFIX}.maintainPlanCode`).d('维保计划编号'),
    START_DATE: intl.get(`${MODELPREFIX}.startDate`).d('生效日期'),
    END_DATE: intl.get(`${MODELPREFIX}.endDate`).d('失效日期'),
    MAINTSITE: intl.get(`${MODELPREFIX}.maintSite`).d('服务区域'),
    LAST_WORK_TIME: intl.get(`${MODELPREFIX}.lastWorkTime`).d('最近作业时间'),
    CYCLE: intl.get(`${MODELPREFIX}.cycle`).d('周期'),
    LEAD_TIME: intl.get(`${MODELPREFIX}.leadTime`).d('提醒前置天数'),
    FORECAST_TIME_BASIS: intl.get(`${MODELPREFIX}.forecastTimeBasis`).d('作业时间依据'),
    DEFAULT_COMMIT: intl.get(`${MODELPREFIX}.defaultCommit`).d('默认下达'),
    // WO_CREATE_RULE: intl.get(`${MODELPREFIX}.woCreateRule`).d('工单生成规则'),
    WO_TYPE: intl.get(`${MODELPREFIX}.woType`).d('工单类型'),
    OWNER_GROUP: intl.get(`${MODELPREFIX}.ownerGroup`).d('负责人组'),
    OWNER: intl.get(`${MODELPREFIX}.owner`).d('负责人'),
    STANDARD_TIME: intl.get(`${MODELPREFIX}.standardHour`).d('标准时长'),
    DURATION_UOM_CODE: intl.get(`${MODELPREFIX}.durationUomCode`).d('单位'),
    ACTIVITY_OP_NUM: intl.get(`${MODELPREFIX}.activityOpNumber`).d('序号'),
    ACT_OP_NAME: intl.get(`${MODELPREFIX}.actOpName`).d('任务名称'),
    PLANNER_GROUP: intl.get(`${MODELPREFIX}.plannerGroup`).d('计划员组'),
    PLANNER: intl.get(`${MODELPREFIX}.planner`).d('计划员'),
    DEFAULT_JOB: intl.get(`${MODELPREFIX}.defaultJobFlag`).d('优先职务默认'),
    JOB_SPECIFIED: intl.get(`${MODELPREFIX}.jobDefault`).d('职责默认方式'),
    PLAN_SCHED_BASE: intl.get(`${MODELPREFIX}.planSchedBase`).d('计划预测基于'),
    CYCLE_STANDARD_TIME: intl.get(`${MODELPREFIX}.cycleStandardTime`).d('最近标准时间'),
    DURATION_SCHED: intl.get(`${MODELPREFIX}.durationScheduled`).d('计划工期'),
    DURATION_UOM: intl.get(`${MODELPREFIX}.durationUom`).d('工期单位'),
    METER: intl.get(`${MODELPREFIX}.meter`).d('仪表点'),
    CYCLE_WHEN_METER: intl.get(`${MODELPREFIX}.cycleWhenMeter`).d('维保间隔'),
    LAST_WORK_VALUE: intl.get(`${MODELPREFIX}.lastWorkValue`).d('最近作业读数'),
    STANDARD_VALUE: intl.get(`${MODELPREFIX}.standardValue`).d('最近标准读数'),
    NEXT_WORK_VALUE: intl.get(`${MODELPREFIX}.nextWorkValue`).d('预计下次读数'),
    LAST_READING_VALUE: intl.get(`${MODELPREFIX}.lastReadingValue`).d('最近仪表读数'),
    PLAN_TIME_RULE: intl.get(`${MODELPREFIX}.planTimeRule`).d('计划时间规则'),
    DELAY_TIME: intl.get(`${MODELPREFIX}.delayTime`).d('延迟时间'),
    PLAN_START_TIME: intl.get(`${MODELPREFIX}.planStartTime`).d('计划开始时间'),
    LEAD_VALUE: intl.get(`${MODELPREFIX}.leadValue`).d('提醒前置读数'),
    WRITE_BACK_RULE: intl.get(`${MODELPREFIX}.writeBackRule`).d('读数回写规则'),
    CYCLE_INTERVAL_COMMON: intl.get(`${MODELPREFIX}.cycleIntervalCommon`).d('周期/维保间隔'),
    UOM_COMMON: intl.get(`${MODELPREFIX}.uomCommon`).d('单位'),
    // tabs
    TAB_STEP: intl.get(`${PREFIX}.view.message.tab.step`).d('维保步骤'),
    TAB_ADV: intl.get(`${PREFIX}.view.message.tab.adv`).d('高级配置'),
    TAB_MATERIAL: intl.get(`${PREFIX}.view.message.tab.material`).d('物料信息'),
    TAB_CHECK: intl.get(`${PREFIX}.view.message.tab.check`).d('检查项'),
  };
  return LANGS[key];
};

export default getLangs;
