import React, { useMemo } from 'react';
import {
  DataSet,
  Table,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { HALM_MTC } from 'alm/utils/config';
import { isNil } from 'lodash';
import { observer } from 'mobx-react';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';

import {
  tableDS,
} from './stores/WorkshopEnergyQuery';


const modelPrompt = 'tarzan.hmes.EquipmentMaintenanceManagementReport';

const EquipmentMaintenanceManagementReport = observer(() => {

  const tableDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  const columns = [
    {
      name: 'maintSiteDescription',
    },
    {
      name: 'sr<PERSON><PERSON><PERSON>',
    },
    {
      name: 'woNum',
    },
    {
      name: 'woName',
    },
    {
      name: 'woTypeName',
    },
    {
      name: 'assetNum',
    },
    {
      name: 'faultReason',
    },
    {
      name: 'faultPoint',
    },
    {
      name: 'assetDesc',
    },
    {
      name: 'remark',
    },
    {
      name: 'ownerName',
    },
    {
      name: 'reporterName',
    },
    {
      name: 'scoreMeaning',
    },
    {
      name: 'scoreDescription',
    },
    {
      name: 'plannerName',
    },
    {
      name: 'durationScheduled',
    },
    {
      name: 'actualTime',
    },
    {
      name: 'woopShortName',
    },
    {
      name: 'itemName',
    },
    {
      name: 'needQuantity',
    },
    {
      name: 'uomName',
    },
    {
      name: 'actualStartDate',
    },
    {
      name: 'actualFinishDate',
    },
    {
      name: 'creationDate',
    },
  ];

  const getExportDetailQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  }


  return (
    <div className='hmes-style'>
      <Header title={intl.get(`${modelPrompt}.title`).d('设备维保管理报表')}>
        <ExcelExport
          method="GET"
          // otherButtonProps={{
          //   disabled: tableDs.records.length === 0,
          // }}
          requestUrl={`${HALM_MTC}/v1/${getCurrentOrganizationId()}/device-maintain-report/export`}
          queryParams={getExportDetailQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="EquipmentMaintenanceManagementReport"
          customizedCode="EquipmentMaintenanceManagementReport"
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          style={{maxHeight: 400}}
          queryFieldsLimit={10}
          dataSet={tableDs}
          columns={columns}
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.EquipmentMaintenanceManagementReport', 'tarzan.common'],
})(EquipmentMaintenanceManagementReport);
