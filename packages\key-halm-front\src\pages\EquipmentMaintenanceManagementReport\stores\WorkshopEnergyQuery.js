import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

const tenantId = getCurrentOrganizationId();
const organizationId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.EquipmentMaintenanceManagementReport';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'woId',
    paging: true,
    autoQuery: true,
    selection: false,
    fields: [
      {
        name: 'maintSiteDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.maintSiteDescription`).d('服务区域'),
      },
      {
        name: 'srNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.srNumber`).d('服务申请单号'),
      },
      {
        name: 'woNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.woNum`).d('工单号'),
      },
      {
        name: 'woName',
        type: 'string',
        label: intl.get(`${modelPrompt}.woName`).d('工单概述'),
      },
      {
        name: 'woTypeName',
        type: 'string',
        label: intl.get(`${modelPrompt}.woTypeName`).d('工单类型'),
      },
      {
        name: 'assetDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.assetDesc`).d('设备'),
      },
      {
        name: 'remark',
        type: 'string',
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
      {
        name: 'ownerName',
        type: 'string',
        label: intl.get(`${modelPrompt}.ownerName`).d('负责人'),
      },
      {
        name: 'reporterName',
        type: 'string',
        label: intl.get(`${modelPrompt}.reporterName`).d('提起人'),
      },
      {
        name: 'plannerName',
        type: 'string',
        label: intl.get(`${modelPrompt}.plannerName`).d('计划员'),
      },
      {
        name: 'durationScheduled',
        type: 'string',
        label: intl.get(`${modelPrompt}.durationScheduled`).d('计划时长'),
      },
      {
        name: 'actualTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.actualTime`).d('实际时长'),
      },
      {
        name: 'woopShortName',
        type: 'string',
        label: intl.get(`${modelPrompt}.woopShortName`).d('任务项'),
      },
      {
        name: 'itemName',
        type: 'string',
        label: intl.get(`${modelPrompt}.itemName`).d('领用物料'),
      },
      {
        name: 'needQuantity',
        type: 'string',
        label: intl.get(`${modelPrompt}.needQuantity`).d('物料数量'),
      },
      {
        name: 'actualStartDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.actualStartDate`).d('任务开始时间'),
      },
      {
        name: 'actualFinishDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.actualFinishDate`).d('任务结束时间'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'assetNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.assetNum`).d('设备编号'),
      },
      {
        name: 'faultReason',
        type: 'string',
        label: intl.get(`${modelPrompt}.faultReason`).d('故障原因'),
      },
      {
        name: 'faultPoint',
        type: 'string',
        label: intl.get(`${modelPrompt}.faultPoint`).d('故障点'),
      },
      {
        name: 'uomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomName`).d('单位'),
      },
      {
        name: 'scoreMeaning',
        type: 'string',
        label: '评分', 
      },
      {
        name: 'scoreDescription',
        type: 'string',
        label: '评分描述', 
      },
    ],
    queryFields: [
      {
        name: 'ServiceareaLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.maintSiteId`).d('服务区域'),
        ignore: 'always',
        textField: 'maintSiteName',
        valueField: 'maintSiteId',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
      },
      {
        name: 'maintSiteId',
        bind: 'ServiceareaLov.maintSiteId',
      },
      {
        name: 'assetLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.assetLov`).d('设备'),
        ignore: 'always',
        textField: 'assetDesc',
        valueField: 'assetId',
        lovCode: 'AAFM.ASSET_LOV',
      },
      {
        name: 'assetId',
        bind: 'assetLov.assetId',
      },
      {
        name: 'assetDesc',
        bind: 'assetLov.assetDesc',
      },
      {
        name: 'employeeObjLov',
        label: intl.get(`${modelPrompt}.employeeObjLov`).d('负责人'),
        lovCode: 'LOV_ACT_EMPLOYEE',
        type: 'object',
        ignore: 'always',
        textField: 'name',
        valueField: 'employeeId',
      },
      {
        name: 'ownerId',
        type: 'string',
        bind: 'employeeObjLov.employeeId',
      },
      {
        name: 'employeePlanLov',
        label: intl.get(`${modelPrompt}.employeePlanLov`).d('计划员'),
        lovCode: 'LOV_ACT_EMPLOYEE',
        type: 'object',
        ignore: 'always',
        textField: 'name',
        valueField: 'employeeId',
      },
      {
        name: 'plannerId',
        type: 'string',
        bind: 'employeePlanLov.employeeId',
      },
      {
        name: 'woTypeCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.woType`).d('工单类型'),
        textField: 'woTypeName',
        valueField: 'woTypeCode',
        lookupUrl: `${HALM_MTC}/v1/${tenantId}/workorder-type/lov?enabledFlag=1`,
        lookupAxiosConfig: {
          transformResponse(data) {
            // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
            if (data instanceof Array) {
              return data;
            }
            const { content } = JSON.parse(data);
            return content;
          },
        },
      },
      {
        name: 'woopShortName',
        type: 'string',
        label: intl.get(`${modelPrompt}.taskitem`).d('任务项'),
      },
      {
        name: 'materialLov',
        label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
        lovCode: 'AORI.ITEM',
        type: 'object',
        ignore: 'always',
        textField: 'itemName',
        valueField: 'itemId',
      },
      {
        name: 'itemId',
        type: 'string',
        bind: 'materialLov.itemId',
      },
      {
        name: 'actualStartDateFrom',
        max: 'actualStartDateTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.startTime`).d('开始时间从'),
      },
      {
        name: 'actualStartDateTo',
        min: 'actualStartDateFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.startTime`).d('开始时间至'),
      },
      {
        name: 'actualFinishDateFrom',
        max: 'actualFinishDateTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.endTime`).d('结束时间从'),
      },
      {
        name: 'actualFinishDateTo',
        min: 'actualFinishDateFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.endTime`).d('结束时间至'),
      },
      {
        name: 'woNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.woNum`).d('工单号'),
      },
      {
        name: 'creationDateFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
        max: 'creationDateTo',
      },
      {
        name: 'creationDateTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
        min: 'creationDateFrom',
      },
      {
        name: 'employeesLov',
        label: intl.get(`${modelPrompt}.employeeObjLov`).d('提起人'),
        lovCode: 'HALM.EMPLOYEE',
        type: 'object',
        lovPara: { tenantId:organizationId },
        noCache: true,
        ignore: 'always',
      },
      {
        name: 'reporterId',
        type: 'string',
        bind: 'employeesLov.employeeId',
      },
      {
        name: 'faultPoint',
        type: 'string',
        label: intl.get(`${modelPrompt}.faultPoint`).d('故障点'),
        lookupCode: 'ALM.FAULT_POINT',
        textField: 'meaning',
        valueField: 'value',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${HALM_MTC}/v1/${tenantId}/device-maintain-report/select`,
          method: 'GET',
        };
      },
    },
  };
};



export { tableDS };
