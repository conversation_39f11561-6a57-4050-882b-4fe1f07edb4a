import request from 'utils/request';
import { HALM_MTC, HALM_MDM, HALM_ORI } from 'alm/utils/config';
import { getCurrentUserId, getCurrentOrganizationId } from 'utils/utils';

const userId = getCurrentUserId();
const tenantId = getCurrentOrganizationId();

// 查询服务区域明细
const getMaintSiteDetail = maintSiteId => {
  const queryMaintSiteUrl = `${HALM_MDM}/v1/${tenantId}/maint-sites`;
  return request(`${queryMaintSiteUrl}/${maintSiteId}`, {
    method: 'GET',
    query: {
      maintSiteId,
      tenantId,
    },
  });
};

// 查询工作中心人员
const getCurrentWorkcenterStaff = ({ maintSiteId }) => {
  return request(`${HALM_MTC}/v1/${tenantId}/work-orders/current`, {
    method: 'GET',
    query: {
      userId,
      tenantId,
      maintSiteId,
    },
  });
};

// 查询位置信息
const getAssetLocationInfo = locationId => {
  return request(`${HALM_MDM}/v1/${tenantId}/asset-locations/${locationId}`, {
    method: 'GET',
  });
};

// 查询缺陷
const getEvalItem = assetId => {
  return request(`${HALM_MTC}/v1/${tenantId}/eval-item`, {
    method: 'GET',
    query: {
      assetId,
      tenantId,
    },
  });
};

/**
 * 查询默认职责
 * @param {*} {}
 */
const getDefaultStaff = ({
  serviceType,
  assetId,
  assetLocationId,
  maintSiteId,
  isRoute,
  point,
}) => {
  return request(`${HALM_ORI}/v1/${tenantId}/default-staff`, {
    method: 'GET',
    query: {
      point,
      userId,
      assetId,
      isRoute,
      tenantId,
      serviceType,
      maintSiteId,
      assetLocationId,
    },
  });
};

export {
  getEvalItem,
  getMaintSiteDetail,
  getAssetLocationInfo,
  getCurrentWorkcenterStaff,
  getDefaultStaff,
};
