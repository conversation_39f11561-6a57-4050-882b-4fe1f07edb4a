/**
 * 资产分类
 * @since 2021-02-23
 * <AUTHOR>
 * @copyright Copyright (c) 2021, Hand
 */
import { isUndefined, isNull } from 'lodash';
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';

import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();

// 查询列表数据
const apiPrefix = `${HALM_ATN}/v1`;

// 列表ds
function tableDS() {
  return {
    autoQuery: true,
    selection: 'multiple',
    cacheSelection: true,
    paging: 'server',
    pageSize: 10,
    primaryKey: 'assetSetId',
    idField: 'assetSetId',
    parentField: 'parentClassId',
    queryFields: [
      {
        label: getLang('ASSET_CLASS_CODE'),
        name: 'assetSetNum',
        type: 'string',
      },
      {
        label: getLang('ASSET_CLASS_NAME'),
        name: 'assetSetName',
        type: 'string',
        maxLength: 40,
      },
      {
        label: getLang('ASSET_CLASS_LEVEL'),
        name: 'classLevelName',
        type: 'string',
        maxLength: 20,
      },
      {
        label: getLang('FA_CATEGORY'),
        name: 'faCategoryName',
        type: 'string',
      },
      {
        name: 'enabledFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        label: getLang('ENABLED_FLAG'),
      },
    ],
    fields: [
      {
        label: getLang('ASSET_CLASS_CODE'),
        name: 'assetSetNum',
        type: 'string',
      },
      {
        label: getLang('ASSET_CLASS_NAME'),
        name: 'assetSetName',
        type: 'string',
      },
      {
        label: getLang('ASSET_CLASS_LEVEL'),
        name: 'classLevelName',
        type: 'string',
      },
      {
        label: getLang('FA_CATEGORY'),
        name: 'faCategoryName',
        type: 'string',
      },
      {
        name: 'enabledFlag',
        type: 'boolean',
        lookupCode: 'HPFM.FLAG',
        label: getLang('ENABLED_FLAG'),
        defaultValue: 1,
        trueValue: 1,
        falseValue: 0,
      },
    ],
    transport: {
      read: ({ data, params, dataSet }) => {
        const url = `${apiPrefix}/${organizationId}/asset-class`;
        return {
          url,
          // 查询条件不作用在查询子节点上
          // data: data?.parentClassId ? { parentClassId: data?.parentClassId } : data,
          data: data?.parentClassId
            ? { parentClassId: data?.parentClassId }
            : { ...data, treeFlag: 1 },
          dataSet,
          params,
          method: 'GET',
        };
      },
      destroy: ({ data }) => {
        const url = `${apiPrefix}/${organizationId}/asset-class`;
        return {
          url,
          method: 'DELETE',
          data: data[0],
        };
      },
    },
  };
}

function detailDS() {
  return {
    transport: {
      read: ({ data, params, dataSet }) => {
        const url = `${apiPrefix}/${organizationId}/asset-class/${data.assetSetId}`;
        return {
          url,
          data,
          dataSet,
          params,
          method: 'GET',
        };
      },
    },
    lang: intl.options.currentLocale,
    autoQuery: false,
    selection: false,
    pageSize: 10,
    primaryKey: 'assetSetId',
    fields: [
      {
        label: getLang('ASSET_CLASS_CODE'),
        name: 'assetSetNum',
        type: 'string',
        required: true,
        dynamicProps: {
          disabled: ({ record }) => {
            return !isUndefined(record.get('assetSetId'));
          },
        },
        maxLength: 40,
      },
      {
        label: getLang('ASSET_CLASS_NAME'),
        name: 'assetSetName',
        type: 'intl',
        required: true,
        maxLength: 40,
      },
      {
        label: getLang('PARENT_ASSET_CLASS'),
        name: 'parentClassLov',
        type: 'object',
        lovCode: 'AAFM.ASSET_SET',
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ record }) => {
            const orderNum = record?.get('orderNum');
            return {
              organizationId,
              effectiveUpFlag: 1,
              orderNum,
              enabledFlag: 1,
            };
          },
        },
      },
      {
        label: getLang('PARENT_ASSET_CLASS'),
        name: 'parentClassId',
        type: 'number',
        bind: 'parentClassLov.assetSetId',
      },
      {
        label: getLang('PARENT_ASSET_CLASS'),
        name: 'parentClassName',
        type: 'string',
        bind: 'parentClassLov.assetSetName',
      },
      {
        label: getLang('ASSET_CLASS_RANK'),
        name: 'classLevelLov',
        type: 'object',
        required: true,
        lovCode: 'AAFM.ASSET_CLASS_LEVEL',
        ignore: 'always',
        lovPara: { organizationId },
        dynamicProps: {
          disabled: ({ record }) => {
            return !isUndefined(record.get('assetSetId'));
          },
        },
      },
      {
        name: 'classLevelCode',
        type: 'string',
        bind: 'classLevelLov.classLevelCode',
      },
      {
        label: getLang('ASSET_CLASS_RANK'),
        name: 'classLevelName',
        type: 'string',
        bind: 'classLevelLov.classLevelName',
      },
      {
        name: 'orderNum',
        type: 'string',
        bind: 'classLevelLov.orderNum',
      },

      {
        label: getLang('ATTRIBUTE_SET'),
        name: 'attributeSetMeaning',
        type: 'string',
      },
      {
        name: 'enabledFlag',
        type: 'boolean',
        label: getLang('ENABLED_FLAG'),
        defaultValue: 1,
        trueValue: 1,
        falseValue: 0,
      },

      {
        name: 'description',
        label: getLang('DESCRIPTION'),
        type: 'intl',
        maxLength: 240,
      },
      // {
      //   label: getLang('VISUAL_LABEL_RULE'),
      //   name: 'visualLabelRuleCode',
      //   type: 'string',
      //   lookupCode: 'AAFM.VISUAL_LABEL_RULE_CODE',
      //   dynamicProps: {
      //     required: ({ record }) => record.get('classLevelCode') === 'ASC',
      //   },
      // },
      {
        label: getLang('ASSET_SPECIALTY'),
        name: 'assetSpecialtyLov',
        type: 'object',
        lovCode: 'AAFM.SPECIAL_ASSET_CLASS',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'assetSpecialtyId',
        type: 'number',
        bind: 'assetSpecialtyLov.assetSpecialtyId',
      },
      {
        label: getLang('ASSET_SPECIALTY'),
        name: 'assetSpecialtyName',
        type: 'string',
        bind: 'assetSpecialtyLov.assetSpecialtyName',
      },
      {
        label: getLang('FA_CATEGORY'),
        name: 'faCategoryLov',
        type: 'object',
        lovCode: 'AFAM.FIXED_ASSET_CATEGORY',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'faCategoryId',
        type: 'string',
        bind: 'faCategoryLov.faCategoryId',
      },
      {
        label: getLang('FA_CATEGORY'),
        name: 'faCategoryName',
        type: 'string',
        bind: 'faCategoryLov.categoryName',
      },
      {
        name: 'codeRuleFlag',
        type: 'boolean',
        defaultValue: 0,
        trueValue: 1,
        falseValue: 0,
      },
      {
        label: getLang('CODE_RULE_FLAG'),
        name: 'codeRuleLov',
        type: 'object',
        lovCode: 'HALM.CODE_RULE_URL',
        lovPara: { organizationId },
        ignore: 'always',
        dynamicProps: {
          // disabled: ({ record }) => {
          //   return record.get('codeRuleFlag') !== 1;
          // },
          required: ({ record }) => record.get('codeRuleFlag') === 1,
        },
      },
      {
        name: 'codeRule',
        type: 'string',
        bind: 'codeRuleLov.ruleCode',
      },
      {
        label: getLang('CODE_RULE'),
        name: 'codeRuleMeaning',
        type: 'string',
        bind: 'codeRuleLov.ruleName',
      },
      {
        label: getLang('STANDARD_ASSET_CODE_RULE'),
        name: 'standarAssetCodeRuleLov',
        type: 'object',
        lovCode: 'HALM.CODE_RULE_URL',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'standardAssetCodeRule',
        type: 'string',
        bind: 'standarAssetCodeRuleLov.ruleCode',
      },
      {
        label: getLang('STANDARD_ASSET_CODE_RULE'),
        name: 'standardAssetCodeRuleMeaning',
        type: 'string',
        bind: 'standarAssetCodeRuleLov.ruleName',
      },
      // {
      //   name: 'maintainFlag',
      //   type: 'boolean',
      //   label: getLang('MAINTAIN_FLAG'),
      //   defaultValue: 1,
      //   trueValue: 1,
      //   falseValue: 0,
      // },
      {
        label: getLang('ASSET_IMPORTANCE'),
        name: 'assetImportance',
        type: 'string',
        lookupCode: 'AAFM.ASSET_IMPORTANCE',
      },
      {
        name: 'relationIotFlag',
        type: 'boolean',
        label: getLang('RELATION_IOT_FLAG'),
        defaultValue: 0,
        trueValue: 1,
        falseValue: 0,
      },
      {
        label: getLang('CONFIG'),
        name: 'configLov',
        type: 'object',
        lovCode: 'HIOT.LOV.CLOUD_ACCOUNT',
        textField: 'platformName',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
        dynamicProps: {
          disabled: ({ record }) => {
            return record.get('relationIotFlag') !== 1;
          },
        },
      },
      {
        name: 'configId',
        type: 'string',
        bind: 'configLov.configId',
      },
      {
        label: getLang('CONFIG'),
        name: 'platformName',
        type: 'string',
        bind: 'configLov.platformName',
      },
      {
        label: getLang('CONFIG_NAME'),
        name: 'configName',
        type: 'string',
      },
      {
        name: 'meterFlag',
        type: 'boolean',
        label: getLang('METER_FLAG'),
        defaultValue: 0,
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'regularCheckFlag',
        type: 'boolean',
        label: getLang('REGULAR_CHECK_FLAG'),
        defaultValue: 0,
        trueValue: 1,
        falseValue: 0,
      },
    ],
  };
}

function queryAttributeSetDS() {
  return {
    transport: {
      read: ({ data, params, dataSet }) => {
        const url = `${apiPrefix}/${organizationId}/attribute-sets/filter`;
        return {
          url,
          data,
          dataSet,
          params: {
            ...params,
            attributeSetTypeCode: 'ASSET_ATTRIBUTE_SET',
            headerId: data.assetSetId,
            tenantId: organizationId,
          },
          method: 'GET',
        };
      },
    },
    autoQuery: true,
    selection: 'multiple',
    pageSize: 10,
    primaryKey: 'attributeSetId',
    queryFields: [
      {
        label: getLang('ATTRIBUTE_SET_NAME'),
        name: 'attributeSetName',
        type: 'string',
      },
    ],
    fields: [
      {
        label: getLang('ATTRIBUTE_SET_NAME'),
        name: 'attributeSetName',
        type: 'string',
      },
      {
        label: getLang('ATTRIBUTE_SET_TYPE'),
        name: 'attributeSetTypeMeaning',
        type: 'string',
      },
      {
        label: getLang('ATTRIBUTE_SET_CODE'),
        name: 'attributeSetCode',
        type: 'string',
      },
    ],
  };
}

function attributeSetListDS() {
  return {
    transport: {
      read: ({ data, params, dataSet }) => {
        const url = `${apiPrefix}/${organizationId}/attribute-set-relations/ASSET_ATTRIBUTE_SET/${data.assetSetId}`;
        return {
          url,
          data,
          dataSet,
          params: {
            ...params,
            headerId: data.assetSetId,
            relationType: 'ASSET_ATTRIBUTE_SET',
            tenantId: organizationId,
          },
          method: 'GET',
        };
      },
      destroy: ({ data, params }) => {
        const temp = data[0];
        return {
          url: `${HALM_ATN}/v1/${organizationId}/attribute-set-relations/remove/ASSET_ATTRIBUTE_SET/${temp.assetSetId}/${temp.attributeSetId}`,
          data,
          params,
          method: 'DELETE',
        };
      },
    },
    autoQuery: false,
    selection: false,
    pageSize: 10,
    primaryKey: 'attributeSetId',
    fields: [
      {
        label: getLang('ATTRIBUTE_SET_NAME'),
        name: 'attributeSetName',
        type: 'string',
      },
      {
        label: getLang('ATTRIBUTE_SET_TYPE'),
        name: 'attributeSetTypeMeaning',
        type: 'string',
      },
      {
        label: getLang('ATTRIBUTE_SET_CODE'),
        name: 'attributeSetCode',
        type: 'string',
      },
      {
        label: getLang('DESCRIPTION'),
        name: 'description',
        type: 'string',
      },
      {
        label: getLang('ENABLED_FLAG'),
        name: 'enabledFlag',
        type: 'string',
      },
    ],
  };
}

function assetListDS() {
  return {
    transport: {
      read: ({ data, params, dataSet }) => {
        const url = `${apiPrefix}/${organizationId}/asset-info`;
        const assetFilter = {
          assetSetId: dataSet.filter.assetSetId,
          assetDesc: dataSet.filter.assetDesc,
          assetLocationId: dataSet.filter.assetLocationId,
          assetSpecialtyId: dataSet.filter.assetSpecialtyId,
          assetStatusId: dataSet.filter.assetStatusId,
          owningOrgId: dataSet.filter.owningOrgId,
          owningOrgType: dataSet.filter.owningOrgType,
          userPersonId: dataSet.filter.userPersonId,
          usingOrgId: dataSet.filter.usingOrgId,
          usingOrgType: dataSet.filter.usingOrgType,
          visualLabelLike: dataSet.filter.visualLabel,
        };
        return {
          url,
          data,
          dataSet,
          params: {
            ...params,
            assetSetId: data.assetSetId,
            tenantId: organizationId,
            ...assetFilter,
          },
          method: 'GET',
        };
      },
    },
    autoQuery: false,
    selection: false,
    pageSize: 10,
    primaryKey: 'assetId',
    fields: [
      {
        label: getLang('ASSET_DESC'),
        name: 'assetDesc',
        type: 'string',
      },
      {
        label: getLang('VISUAL_LABEL'),
        name: 'visualLabel',
        type: 'string',
      },
      {
        label: getLang('ASSET_CLASS'),
        name: 'assetSetName',
        type: 'string',
      },
      {
        label: getLang('ASSET_STATUS'),
        name: 'assetStatusName',
        type: 'string',
      },
      {
        label: getLang('ASSET_SPECIALTY'),
        name: 'assetSpecialtyName',
        type: 'string',
      },
      {
        label: getLang('ASSET_LOCATION'),
        name: 'assetLocationName',
        type: 'string',
      },
      {
        label: getLang('OWNING_ORG'),
        name: 'owningOrgName',
        type: 'string',
      },
      {
        label: getLang('USING_ORG'),
        name: 'usingOrgName',
        type: 'string',
      },
      {
        label: getLang('USER_PERSON'),
        name: 'userPersonName',
        type: 'string',
      },
    ],
  };
}

function filterDS() {
  return {
    autoCreate: true,
    fields: [
      {
        label: getLang('ASSET_DESC'),
        name: 'assetDesc',
        type: 'string',
      },
      {
        label: getLang('VISUAL_LABEL'),
        name: 'visualLabel',
        type: 'string',
      },
      {
        label: getLang('ASSET_LOCATION'),
        name: 'assetLocationLov',
        type: 'object',
        lovCode: 'AMDM.LOCATIONS',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'assetLocationId',
        type: 'string',
        bind: 'assetLocationLov.assetLocationId',
      },
      {
        label: getLang('ASSET_LOCATION'),
        name: 'assetLocationName',
        type: 'string',
        bind: 'assetLocationLov.locationName',
      },
      {
        label: getLang('OWNING_ORG'),
        name: 'owningOrgName',
        type: 'string',
      },
      {
        label: getLang('USING_ORG'),
        name: 'usingOrgName',
        type: 'string',
      },
      {
        name: 'usingOrgId',
        type: 'string',
        bind: 'usingOrgLov.orgId',
      },
      {
        label: getLang('USER_PERSON'),
        name: 'userPersonLov',
        type: 'object',
        lovCode: 'HALM.EMPLOYEE',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
      },
      {
        name: 'userPersonId',
        type: 'string',
        bind: 'userPersonLov.employeeId',
      },
      {
        label: getLang('ASSET_STATUS'),
        name: 'assetStatusLov',
        type: 'object',
        lovCode: 'AAFM.ASSET_STATUS',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'assetStatusId',
        type: 'string',
        bind: 'assetStatusLov.assetStatusId',
      },
      {
        label: getLang('ASSET_CLASS'),
        name: 'assetClassLov',
        type: 'object',
        lovCode: 'AAFM.ASSET_SET',
        lovPara: { organizationId, effectiveAssetsClassFlag: 1, enabledFlag: 1 },
        ignore: 'always',
      },
      {
        name: 'assetSetId',
        type: 'string',
        bind: 'assetClassLov.assetSetId',
      },
      {
        label: getLang('ASSET_SPECIALTY'),
        name: 'assetSpecialtyLov',
        type: 'object',
        lovCode: 'AAFM.SPECIAL_ASSET_CLASS',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'assetSpecialtyId',
        type: 'string',
        bind: 'assetSpecialtyLov.assetSpecialtyId',
      },
    ],
  };
}

function assetStatusListDS() {
  return {
    transport: {
      read: ({ data, params, dataSet }) => {
        const url =
          dataSet.isNew || dataSet.oriClassLevelCode !== 'ASC'
            ? `${apiPrefix}/${organizationId}/asset-status/list`
            : `${apiPrefix}/${organizationId}/asset-class/asset-status/${dataSet.assetSetId}`;
        return {
          url,
          data,
          dataSet,
          params: {
            ...params,
            enabledFlag: 1,
            tenantId: organizationId,
          },
          method: dataSet.isNew || dataSet.oriClassLevelCode !== 'ASC' ? 'GET' : 'POST',
        };
      },
      submit: ({ dataSet, data, params }) => {
        return {
          url: `${HALM_ATN}/v1/${organizationId}/asset-status-relations`,
          data: {
            relationsType: 'ASSET_SET',
            mouduleId: dataSet.assetSetId,
            assetStatusIds: data.map(i => i.assetStatusId).join(','),
          },
          params,
          method: 'POST',
        };
      },
      destroy: ({ dataSet, params }) => {
        const deleteArray = dataSet.deleteArr;
        return {
          url: `${HALM_ATN}/v1/${organizationId}/asset-status-relations`,
          data: {
            relationsType: 'ASSET_SET',
            mouduleId: dataSet.assetSetId,
            assetStatusIds: deleteArray.join(','),
          },
          params,
          method: 'DELETE',
        };
      },
    },
    events: {
      read: ({ dataSet }) => {
        dataSet.forEach(i => {
          if (isNull(i.get('code'))) {
            i.set('code', i.get('assetStatusCode'));
          }
        });
      },
    },
    autoQuery: false,
    selection: false,
    paging: false,
    primaryKey: 'assetStatusId',
    idField: 'code',
    parentField: 'parentCode',
    fields: [
      {
        label: getLang('ASSET_STATUS_NAME'),
        name: 'assetStatusName',
        type: 'string',
      },
      {
        label: getLang('ASSET_STATUS_CODE'),
        name: 'assetStatusCode',
        type: 'string',
      },
      {
        name: 'parentCode',
        type: 'string',
      },
      {
        label: getLang('DESCRIPTION'),
        name: 'description',
        type: 'string',
      },
    ],
  };
}

function queryStatusDS() {
  return {
    transport: {
      read: ({ data, params, dataSet }) => {
        const url = `${apiPrefix}/${organizationId}/asset-status`;
        return {
          url,
          data,
          dataSet,
          params: {
            ...params,
            enabledFlag: 1,
            tenantId: organizationId,
            insertFlag: !dataSet.isNew, // 后端写反了
          },
          method: 'GET',
        };
      },
    },
    autoQuery: false,
    selection: 'multiple',
    pageSize: 10,
    primaryKey: 'assetStatusId',
    queryFields: [
      {
        name: 'assetStatusName',
        label: getLang('ASSET_STATUS_NAME'),
        type: 'string',
        labelWidth: 50,
      },
      {
        name: 'assetStatusCodeLike',
        label: getLang('ASSET_STATUS_CODE'),
        type: 'string',
      },
    ],
    fields: [
      {
        label: getLang('ASSET_STATUS_NAME'),
        name: 'assetStatusName',
        type: 'string',
      },
      {
        label: getLang('ASSET_STATUS_CODE'),
        name: 'assetStatusCode',
        type: 'string',
      },
      {
        name: 'parentCode',
        type: 'string',
      },
      {
        label: getLang('DESCRIPTION'),
        name: 'description',
        type: 'string',
      },
    ],
  };
}

// 获取默认规则数据
function frameWorkRulesDS(that) {
  return {
    autoQuery: true,
    fields: [
      {
        name: 'enabledAcFlag',
        type: 'number',
      },
    ],
    events: {
      load: ({ dataSet }) => {
        const enabledAcFlag = dataSet.current.get('enabledAcFlag');
        that.setState({ enabledFieldFlag: enabledAcFlag === 1 });
      },
    },
    transport: {
      read: () => {
        const url = `${apiPrefix}/${organizationId}/frameworks`;
        return {
          url,
          method: 'GET',
        };
      },
    },
  };
}

export {
  tableDS,
  detailDS,
  queryAttributeSetDS,
  attributeSetListDS,
  assetListDS,
  filterDS,
  assetStatusListDS,
  queryStatusDS,
  frameWorkRulesDS,
};
