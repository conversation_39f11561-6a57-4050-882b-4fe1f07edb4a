/**
 * @since 2020-06-10
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { HZERO_PLATFORM } from 'utils/config';
import { HALM_MDM, HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { expandOrShrinkAll } from 'alm/utils/utils';

const organizationId = getCurrentOrganizationId();
const promptCode = 'amdm.location.model.location';

const queryListUrl = `${HALM_MDM}/v1/${organizationId}/asset-locations/lov/tree`;

function tableDS() {
  return {
    autoQuery: true,
    selection: 'multiple',
    cacheSelection: true,
    paging: 'server',
    pageSize: 10,
    modifiedCheck: false,
    primaryKey: 'assetLocationId',
    dataKey: 'content',
    idField: 'assetLocationId',
    parentField: 'parentLocationId',
    expandField: 'expand',
    queryFields: [
      {
        name: 'locationName',
        type: 'string',
        label: intl.get('amdm.location.model.location.locationName').d('位置名称'),
      },
      {
        name: 'locationCode',
        type: 'string',
        label: intl.get('amdm.location.model.location.locationCode').d('位置编码'),
      },
      {
        name: 'locationTypeLov',
        type: 'object',
        lovCode: 'AMDM.LOCATION_TYPE',
        label: intl.get('amdm.location.model.location.locationTypeId').d('位置类型'),
        noCache: true,
        ignore: 'always',
      },
      {
        name: 'locationTypeId',
        type: 'string',
        bind: 'locationTypeLov.assetLocationTypeId',
      },
      {
        name: 'maintSiteLov',
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        label: intl.get('amdm.location.model.location.maintSites').d('服务区域'),
        noCache: true,
        ignore: 'always',
      },
      {
        name: 'maintSiteId',
        type: 'number',
        bind: 'maintSiteLov.maintSiteId',
      },
      {
        name: 'enabledFlag',
        // type: 'number',
        // lookupCode: 'HPFM.FLAG',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.enabledFlag').d('是否启用'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      {
        name: 'assetLocationFlag',
        // type: 'number',
        // lookupCode: 'HPFM.FLAG',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.assetLocationFlag').d('可放置资产'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      {
        name: 'directMaintainFlag',
        // type: 'number',
        // lookupCode: 'HPFM.FLAG',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.directMaintainFlag').d('可维修'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
    ],
    fields: [
      {
        name: 'expand',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.expandedOrNot').d('是否展开'),
      },
      {
        name: 'locationName',
        type: 'string',
        label: intl.get('amdm.location.model.location.locationName').d('位置名称'),
      },
      {
        name: 'locationTypeName',
        type: 'string',
        label: intl.get('amdm.location.model.location.locationTypeId').d('位置类型'),
      },
      {
        name: 'locationCode',
        type: 'string',
        label: intl.get('amdm.location.model.location.locationCode').d('位置编码'),
      },
      {
        name: 'maintSiteName',
        type: 'string',
        label: intl.get('amdm.location.model.location.maintSites').d('服务区域'),
      },
      {
        name: 'assetLocationFlag',
        type: 'boolean',
        trueValue: 1,
        falseValue: 0,
        label: intl.get('amdm.location.model.location.assetLocationFlag').d('可放置资产'),
      },
      {
        name: 'directMaintainFlag',
        type: 'boolean',
        trueValue: 1,
        falseValue: 0,
        label: intl.get('amdm.location.model.location.directMaintainFlag').d('可维修'),
      },
      // {
      //   name: 'manageOrgName',
      //   type: 'string',
      //   label: intl.get('amdm.location.model.location.manageOrgName').d('管理部门'),
      // },
      // {
      //   name: 'stockFlag',
      //   type: 'boolean',
      //   trueValue: 1,
      //   falseValue: 0,
      //   label: intl.get('amdm.location.model.location.stockFlag').d('是否库存'),
      // },
      {
        name: 'description',
        type: 'string',
        label: intl.get('amdm.location.model.location.description').d('描述'),
      },
      {
        name: 'enabledFlag',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.enabledFlag').d('是否启用'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
    ],
    transport: {
      read: ({ params, data }) => {
        // 如果查询条件中有 locationName， 那么接口就会将父子都返回，前端需要把树形展开
        // 如果没有，则接口只返回父，子数据通过点击异步加载,故不用做特殊处理
        if (data.locationName || data.locationCode) {
          return {
            params,
            url: queryListUrl,
            method: 'GET',
            transformResponse: res => {
              const originData = JSON.parse(res);
              const content = originData.content || [];
              const newContent = expandOrShrinkAll({
                type: 'expand',
                data: content,
                parentField: 'parentLocationId',
                idField: 'assetLocationId',
              });
              return {
                ...originData,
                content: newContent,
              };
            },
          };
        } else {
          return {
            params,
            url: queryListUrl,
            method: 'GET',
          };
        }
      },
      submit: ({ dataSet }) => {
        const { currentObj, enabledFlag } = dataSet;
        const { assetLocationId, objectVersionNumber, tenantId, _token } = currentObj;
        const data = {
          assetLocationId,
          objectVersionNumber,
          tenantId,
          _token,
        };
        const url = enabledFlag ? 'enable' : 'disable';
        return {
          url: `${HALM_MDM}/v1/${organizationId}/asset-locations/${url}`,
          data,
          method: 'PUT',
        };
      },
    },
  };
}

function detailDS(that) {
  return {
    autoCreate: true,
    dataKey: 'content',
    lang: intl.options.currentLocale,
    fields: [
      {
        name: 'maintSiteLov',
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        label: intl.get('amdm.location.model.location.maintSites').d('服务区域'),
        required: true,
        noCache: true,
        ignore: 'always',
      },
      {
        name: 'maintSiteId',
        type: 'number',
        bind: 'maintSiteLov.maintSiteId',
      },
      {
        name: 'maintSiteName',
        label: intl.get('amdm.location.model.location.maintSites').d('服务区域'),
        type: 'string',
        bind: 'maintSiteLov.maintSiteName',
      },
      {
        name: 'locationTypeLov',
        type: 'object',
        lovCode: 'AMDM.LOCATION_TYPE',
        label: intl.get('amdm.location.model.location.locationTypeId').d('位置类型'),
        required: true,
        noCache: true,
        ignore: 'always',
      },
      {
        name: 'locationTypeId',
        type: 'string',
        bind: 'locationTypeLov.assetLocationTypeId',
      },
      {
        name: 'locationTypeName',
        label: intl.get('amdm.location.model.location.locationTypeId').d('位置类型'),
        type: 'string',
        bind: 'locationTypeLov.locationTypeName',
      },
      {
        name: 'locationLov',
        type: 'object',
        lovCode: 'AMDM.LOCATIONS',
        label: intl.get('amdm.location.model.location.parentLocation').d('父位置节点'),
        noCache: true,
        ignore: 'always',
        cascadeMap: {
          maintSiteId: 'maintSiteId',
        },
      },
      {
        name: 'parentLocationId',
        type: 'string',
        bind: 'locationLov.assetLocationId',
      },
      {
        name: 'parentLocationName',
        label: intl.get('amdm.location.model.location.parentLocation').d('父位置节点'),
        type: 'string',
        bind: 'locationLov.locationName',
      },
      {
        name: 'iconSelect',
        label: intl.get('amdm.location.model.location.icon').d('图标'),
      },
      {
        name: 'iconTypeCode',
        type: 'string',
        label: intl.get('amdm.location.model.location.icon').d('图标'),
        lookupCode: 'AMMT.ICONTYPE',
        defaultValue: 'ICON',
      },
      {
        name: 'icon',
        type: 'string',
        label: intl.get('amdm.location.model.location.icon').d('图标'),
        maxLength: 150,
      },
      {
        name: 'locationName',
        type: 'intl',
        label: intl.get('amdm.location.model.location.locationName').d('位置名称'),
        required: true,
        maxLength: 40,
      },
      {
        name: 'locationCode',
        type: 'intl',
        label: intl.get('amdm.location.model.location.locationCode').d('位置编码'),
        required: true,
        maxLength: 40,
      },
      {
        name: 'description',
        type: 'intl',
        label: intl.get('amdm.location.model.location.description').d('描述'),
        maxLength: 240,
      },
      {
        name: 'enabledFlag',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.enabledFlag').d('是否启用'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      {
        name: 'assetLocationFlag',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.assetLocationFlag').d('可放置资产'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      // {
      //   name: 'rackLocationFlag',
      //   type: 'boolean',
      //   label: intl.get('amdm.location.model.location.rackLocationFlag').d('可放置机柜'),
      //   trueValue: 1,
      //   falseValue: 0,
      //   defaultValue: 0,
      // },
      {
        name: 'directMaintainFlag',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.directMaintainFlag').d('可维修'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      // {
      //   name: 'inventoryModeCode',
      //   type: 'string',
      //   label: intl.get('amdm.location.model.location.inventoryModeCode').d('库存属性'),
      //   lookupCode: 'AMDM.INVENTORY_ATTRIBUTE',
      //   defaultValue: 'NONE',
      //   required: true,
      // },
      {
        name: 'costCenterLov',
        type: 'object',
        lovCode: 'AATN.COST_CENTER',
        label: intl.get('amdm.location.model.location.costCenter').d('成本中心'),
        noCache: true,
        ignore: 'always',
        lovPara: {
          type: 'COST_CENTER',
          tenantId: organizationId,
        },
      },
      {
        name: 'costCenterId',
        type: 'number',
        bind: 'costCenterLov.codeId',
      },
      {
        name: 'costCenterMeaning',
        type: 'string',
        bind: 'costCenterLov.name',
      },
      {
        name: 'internalOrderLov',
        label: intl.get(`${promptCode}.internalOrderId`).d('内部订单'),
        type: 'object',
        lovCode: 'HPFM.FINANCE_CODE',
        lovPara: { tenantId: organizationId, type: 'INTERNAL_ORDER' },
        ignore: 'always',
      },
      {
        name: 'internalOrderId',
        type: 'string',
        bind: 'internalOrderLov.codeId',
      },
      {
        name: 'internalOrderMeaning',
        type: 'string',
        bind: 'internalOrderLov.name',
      },
      {
        name: 'wbsElementLov',
        label: intl.get(`${promptCode}.wbsElementId`).d('WBS要素'),
        type: 'object',
        lovCode: 'HPFM.FINANCE_CODE',
        lovPara: { tenantId: organizationId, type: 'WBS_ELEMENT' },
        ignore: 'always',
      },
      {
        name: 'wbsElementId',
        type: 'string',
        bind: 'wbsElementLov.codeId',
      },
      {
        name: 'wbsElementMeaning',
        type: 'string',
        bind: 'wbsElementLov.name',
      },
      {
        name: 'negativeBalancesFlag',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.negativeBalancesFlag').d('允许负库存余量'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'movableLocationFlag',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.movableLocationFlag').d('是否为移动位置'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'pickingFlag',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.pickingFlag').d('是否为挑库来源'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'countryLov',
        type: 'object',
        lovCode: 'HALM.COUNTRY',
        label: intl.get('amdm.location.model.location.country').d('国家/地区'),
        noCache: true,
        ignore: 'always',
      },
      {
        name: 'countryId',
        type: 'number',
        bind: 'countryLov.countryId',
      },
      {
        name: 'countryName',
        label: intl.get('amdm.location.model.location.country').d('国家/地区'),
        type: 'string',
        bind: 'countryLov.countryName',
      },
      {
        name: 'region',
        type: 'string',
        label: intl.get('amdm.location.model.location.region').d('省份/城市/区县'),
        textField: 'regionName',
      },
      {
        name: 'regionName',
        type: 'string',
        label: intl.get('amdm.location.model.location.region').d('省份/城市/区县'),
      },
      {
        name: 'address',
        type: 'intl',
        label: intl.get('amdm.location.model.location.address').d('地址'),
      },
      {
        name: 'addressContact1',
        type: 'string',
        label: intl.get('amdm.location.model.location.addressContact').d('主要联系方式'),
      },
      {
        name: 'mapEnabledFlag',
        type: 'boolean',
        label: intl.get('amdm.location.model.location.mapEnabledFlag').d('开启地图/GIS模式'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'lng', // 经度
        type: 'string',
      },
      {
        name: 'lat', // 纬度
        type: 'string',
      },
      {
        name: 'zoom', // 地图缩放比例
        type: 'number',
      },
      {
        name: 'mapType',
        type: 'string',
        defaultValue: 'BaiduMap',
      },
    ],
    events: {
      load: ({ dataSet }) => {
        const regionIds = dataSet.current.get('regionIds') || '';
        dataSet.current.set('region', regionIds.split(','));
        dataSet.current.set('startStatus', dataSet.current.get('enabledFlag'));
        dataSet.current.set('statusChanged', false);
      },
      update: ({ dataSet, name, value }) => {
        if (name === 'enabledFlag') {
          const startStatus = dataSet.current.get('startStatus');
          const enabledFlag = value;
          const statusChanged = startStatus !== enabledFlag;
          dataSet.current.set('statusChanged', statusChanged);
        } else if (name === 'directMaintainFlag') {
          that.setState({
            directMaintainFlag: value,
          });
        }
      },
    },
    transport: {
      read: ({ data }) => {
        const url = `${HALM_MDM}/v1/${organizationId}/asset-locations/${data.id}`;
        return {
          url,
          method: 'GET',
        };
      },
      submit: ({ data, params }) => {
        let newData = data[0];
        let newParams = params;
        const { region: regionArr, statusChanged } = newData;
        newData.tenantId = organizationId;
        newParams = { ...newParams, statusChanged };
        const regionId = regionArr && regionArr.length && regionArr[regionArr.length - 1];
        const regionIds = regionArr && regionArr.length && regionArr.join(',');
        newData = {
          ...newData,
          regionId,
          regionIds,
        };
        const method = newData.assetLocationId ? 'PUT' : 'POST';
        return {
          url: `${HALM_MDM}/v1/${organizationId}/asset-locations`,
          data: newData,
          params: newParams,
          method,
        };
      },
    },
  };
}

function regionDS() {
  return {
    selection: 'multiple',
    idField: 'regionId',
    parentField: 'parentRegionId',
    fields: [
      { name: 'regionId', type: 'string' },
      { name: 'expand', type: 'boolean' },
      { name: 'parentRegionId', type: 'string' },
    ],
    transport: {
      read: ({ data }) => {
        const { id } = data;
        const url = `${HZERO_PLATFORM}/v1/${organizationId}/countries/${id}/regions?enabledFlag=1`;
        return {
          url,
          method: 'GET',
        };
      },
    },
  };
}

function meterTableDS() {
  return {
    autoQuery: true,
    selection: false,
    primaryKey: 'meterId',
    dataKey: 'content',
    fields: [
      {
        name: 'meterName',
        type: 'string',
        label: intl.get('amdm.location.model.location.meterName').d('仪表点名称'),
      },
      {
        name: 'lastestReadingValue',
        type: 'string',
        label: intl.get('amdm.location.model.location.lastestReadingValue').d('最近读数'),
      },
      {
        name: 'meterUom',
        type: 'string',
        label: intl.get('amdm.location.model.location.meterUom').d('仪表单位'),
      },
      {
        name: 'readingTime',
        type: 'date',
        label: intl.get('amdm.location.model.location.readingTime').d('仪表读数日期'),
      },
      {
        name: 'keyMeterFlag',
        type: 'boolean',
        trueValue: 1,
        falseValue: 0,
        label: intl.get('amdm.location.model.location.keyMeterFlag').d('是否关键仪表'),
      },
    ],
    transport: {
      read: ({ params }) => {
        let newParams = params;
        newParams = {
          ...params,
          enabledFlag: 1,
          orderByKeyMeterFlag: 1,
        };
        const url = `${HALM_MTC}/v1/${organizationId}/meters`;
        return {
          params: newParams,
          url,
          method: 'GET',
        };
      },
    },
  };
}

export { tableDS, detailDS, regionDS, meterTableDS };
