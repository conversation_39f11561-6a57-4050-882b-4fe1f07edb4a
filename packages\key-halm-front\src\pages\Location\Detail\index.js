/**
 * Detail - 位置明细页面
 * @since 2020-06-14
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2020, Hand
 */
import React from 'react';
import { Button, DataSet } from 'choerodon-ui/pro';
import { Spin } from 'choerodon-ui';

import { Header, Content } from 'components/Page';
import { isUndefined, isNull } from 'lodash';
import { Bind } from 'lodash-decorators';

import intl from 'utils/intl';
import notification from 'utils/notification';

import FileUpload from 'alm/components/FileUpload';
import { mapTree } from 'alm/utils/treeUtils';
import formatterCollections from 'utils/intl/formatterCollections';

import InfoExhibit from './InfoExhibit';

import { detailDS, regionDS, meterTableDS } from '../Stores/';

@formatterCollections({
  code: ['amdm.location', 'alm.common', 'alm.component'],
})
export default class MissPermissionRecord extends React.Component {
  constructor(props) {
    super(props);
    const { url } = props.match;
    this.state = {
      editFlag: false, // 区分是编辑还是查看
      detailLoading: false, // info loading
      iconTypeCode: 'ICON', // 图标类型
      mapEnabledFlag: 0, // 是否展示地图
      options: [],
      disabledMaintSite: false, // 置灰 服务区域控件
      regionLoading: false, // 省市区 loading
      isNew: url.includes('create') || url.includes('create-sub'), // 新建有两种情况 ：新建父 新建子
      collapseActiveKey: ['key-1', 'key-2', 'key-3', 'key-4'], // 折叠面板展开的keys
      directMaintainFlag: 1, // 可维修，影响成本对象展示
    };
    this.regionDS = new DataSet(regionDS()); // 省市级联
    this.meterTableDS = new DataSet(meterTableDS());
    this.detailFormDS = new DataSet(detailDS(this));
    this.infoExhibitRef = React.createRef();
  }

  componentDidMount() {
    const {
      location: { state },
      match: { url, params },
    } = this.props;
    const { id, parentId, flag } = params;
    if (url.includes('detail')) {
      // 查看 或 编辑
      this.handleSearch(id);
      if (flag === 'true') {
        this.handleEdit(true);
      }
    } else if (!isUndefined(parentId)) {
      // 新建下级的情况
      // 设置子的父节点 服务区域
      this.detailFormDS.current.set('parentLocationId', parentId);
      this.detailFormDS.current.set('parentLocationName', state && state.parentName);
      this.detailFormDS.current.set('maintSiteId', state && state.maintSiteId);
      this.detailFormDS.current.set('maintSiteName', state && state.maintSiteName);
      this.setState({
        disabledMaintSite: true,
      });
    } else {
      // 新建自身的情况
      this.handleEdit(true);
    }
  }

  componentDidUpdate(preProps) {
    if (preProps.match.params.id !== this.props.match.params.id) {
      this.handleSearch(this.props.match.params.id);
    }
  }

  /**
   * 查询
   * @param {*} id
   */
  async handleSearch(id) {
    this.setState({ detailLoading: true });
    this.detailFormDS.setQueryParameter('id', id);
    const result = await this.detailFormDS.query();

    if (result.countryId) {
      this.regionDS.setQueryParameter('id', result.countryId);
      let options = await this.regionDS.query();
      options = mapTree(options, item => {
        return { ...item, value: item.regionId, label: item.regionName };
      });
      this.setState({ options });
    }
    const flag =
      result.regionIds &&
      this.infoExhibitRef &&
      this.infoExhibitRef.current &&
      this.infoExhibitRef.current.handleChangeRegionValue;
    if (flag) {
      this.infoExhibitRef.current.handleChangeRegionValue(result.regionIds.split(',').map(Number));
    }

    this.meterTableDS.setQueryParameter('onlyLocationId', id);
    this.meterTableDS.query();

    this.setState({
      detailLoading: false,
      iconTypeCode: result.iconTypeCode,
      mapEnabledFlag: result.mapEnabledFlag,
      directMaintainFlag: result.directMaintainFlag,
      disabledMaintSite: Boolean(result.parentLocationId),
    });
  }

  /**
   * 处理保存操作
   */
  @Bind()
  async handleSave() {
    const {
      match: { url },
      history,
    } = this.props;
    const { isNew } = this.state;
    if (!this.detailFormDS.validate()) {
      notification.error({
        message: intl.get('amdm.location.view.message.validate').d('请检查数据！'),
      });
      return;
    }
    if (this.detailFormDS.isModified()) {
      this.detailFormDS.locationUrl = url;
      const res = await this.detailFormDS.submit();
      if (res) {
        if (isNew) {
          // 如果是新建 则进入明细页面
          history.push(`/amdm/location/detail/${res.content[0].assetLocationId}/false`);
        } else {
          const { assetLocationId } = res.content[0];
          this.detailFormDS.setQueryParameter('id', assetLocationId);
          this.detailFormDS.query().then(_res => {
            if (_res) {
              this.setState({
                directMaintainFlag: _res.directMaintainFlag,
              });
            }
          });
          this.handleEdit(false);
        }
      }
    } else {
      notification.warning({
        message: intl.get('amdm.location.view.message.noChange').d('数据未更改！'),
      });
    }
  }

  /**
   * 展开/收缩数据检索
   */
  @Bind()
  setShowSearchFlag() {
    const { showSearchFlag } = this.state;
    this.setState({ showSearchFlag: !showSearchFlag });
  }

  /**
   * 编辑
   * @param {*} flag
   */
  @Bind()
  handleEdit(flag) {
    const { editFlag } = this.state;
    if (editFlag) {
      this.detailFormDS.current.reset(); // 如果编辑
      this.setState({
        iconTypeCode: this.detailFormDS.current.get('iconTypeCode'),
        directMaintainFlag: 1,
        mapEnabledFlag: this.detailFormDS.current.get('mapEnabledFlag'),
      });
    }
    this.setState({ editFlag: flag || !editFlag });
  }

  /**
   * 跳转到详情页
   * @param {string} id 位置行id
   */
  @Bind()
  handleLinkToDetail(id) {
    const { history } = this.props;
    const linkUrl = `detail/${id}`;
    const path = `/amdm/location/${linkUrl}/false`;
    history.push(path);
  }

  @Bind()
  handleLinkToMeter(id) {
    const { history } = this.props;
    const linkUrl = `detail/${id}`;
    const path = `/amtr/meters/${linkUrl}`;
    history.push(path);
  }

  /**
   * 图标修改
   */
  @Bind()
  handleIconTypeCodeChange() {
    const iconTypeCode = this.detailFormDS.current.get('iconTypeCode');
    this.setState({ iconTypeCode });
  }

  /**
   * 地图变更
   * @param {*} val
   */
  @Bind()
  handleMapChange(val) {
    this.setState({ mapEnabledFlag: val });
  }

  @Bind()
  async handleChangeCountry(record) {
    if (!isNull(record)) {
      this.setState({ regionLoading: true });
      const { countryId } = record;
      this.regionDS.setQueryParameter('id', countryId);
      let options = await this.regionDS.query();
      this.setState({ regionLoading: false });
      options = mapTree(options, item => {
        return { ...item, value: item.regionId, label: item.regionName };
      });
      this.setState({ options });
    }
  }

  @Bind()
  handleDisabledMaintSite(value) {
    this.setState({
      disabledMaintSite: value,
    });
  }

  /**
   * 设置折叠面板的keys
   * key使用"key-数字"格式 目前页面已有三个固定的 还会有动态的：位置类型关联的属性组渲染而成
   * 对于三个固定的pane, 位置类型改变时保持其当前折叠/收缩状态不变，而动态的则全部展开
   * @param {number} data 位置类型关联的属性组数据
   */
  @Bind
  handleCollapseActiveKey(data) {
    const { collapseActiveKey } = this.state;
    const keys = collapseActiveKey.filter(i => ['key-1', 'key-2', 'key-3', 'key-4'].includes(i));
    if (data && data.length > 0) {
      for (let num = 1; num <= data.length; num++) {
        keys.push(`key-${3 + num}`);
      }
    }
    return keys;
  }

  @Bind
  handleSetState(records) {
    this.setState(records);
  }

  render() {
    const {
      options,
      isNew,
      editFlag,
      regionLoading,
      iconTypeCode,
      mapEnabledFlag,
      detailLoading,
      disabledMaintSite,
      directMaintainFlag,
      collapseActiveKey,
    } = this.state;
    const { match } = this.props;
    const { id } = match.params;
    const infoProps = {
      id,
      options,
      editFlag,
      iconTypeCode,
      mapEnabledFlag,
      regionLoading,
      disabledMaintSite,
      collapseActiveKey,
      directMaintainFlag,
      infoDS: this.detailFormDS,
      regionDS: this.regionDS,
      meterTableDS: this.meterTableDS,
      onMapChange: this.handleMapChange,
      onIconChange: this.handleIconTypeCodeChange,
      onLinkToMeter: this.handleLinkToMeter,
      onCountryChange: this.handleChangeCountry,
      onDisabledMaintSite: this.handleDisabledMaintSite,
      onSetState: this.handleSetState,
    };
    return (
      <>
        <Header
          title={intl.get('amdm.location.view.title.location').d('位置')}
          backPath="/amdm/location/list"
        >
          <Button
            style={{ display: !editFlag && !isNew ? 'block' : 'none' }}
            icon="border_color"
            color="primary"
            onClick={() => this.handleEdit()}
          >
            {intl.get('hzero.common.button.edit').d('编辑')}
          </Button>
          <Button
            style={{ display: isNew || editFlag ? 'block' : 'none' }}
            icon="save"
            color="primary"
            onClick={this.handleSave}
          >
            {intl.get('hzero.common.button.save').d('保存')}
          </Button>
          <Button
            style={{ display: editFlag && !isNew ? 'block' : 'none' }}
            icon="cancel"
            onClick={() => this.handleEdit()}
          >
            {intl.get('hzero.common.button.close').d('关闭')}
          </Button>
          <FileUpload moduleName="amdm-location" moduleId={id} />
        </Header>
        <Content>
          <Spin spinning={detailLoading}>
            <InfoExhibit {...infoProps} className="info-exhibit" ref={this.infoExhibitRef} />
          </Spin>
        </Content>
      </>
    );
  }
}
