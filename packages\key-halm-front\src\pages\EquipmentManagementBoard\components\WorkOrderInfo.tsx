import React, { useEffect, useState ,useRef } from 'react';
import intl from 'utils/intl';
import { Tooltip } from 'choerodon-ui/pro';
import DashboardCard from './DashboardCard.js';
import styles from '../index.module.less';
import { Badge} from 'choerodon-ui';
const modelPrompt = 'tarzan.wms.DeliverySignage';

const NgReport = ({ data }) => {
  // const [page, setPage] = useState(0);
  const scrollContainerRef = useRef<any>(null);  
  
  useEffect(() => {  
    if (!scrollContainerRef.current || data.length <= 0) return;  
  let timer:any;  
    const scrollToNext = () => {  
      if (scrollContainerRef.current.scrollHeight - scrollContainerRef.current.scrollTop <= scrollContainerRef.current.clientHeight) {  
        scrollContainerRef.current.scrollTop = 0;  
      } else {   
        scrollContainerRef.current.scrollTop += 50; 
      }  
    };  
    timer = setInterval(scrollToNext, 30000); // 每30秒滚动一次  
    return () => {  
      clearInterval(timer);  
    };  
  }, [data, scrollContainerRef]);  

  // useEffect(() => {
  //   let timer;
  //   if(data.length > 1) {
  //     timer = setInterval(() => {
  //       if((page + 1) * 10 < data.length) {
  //         setPage(page + 1);
  //       } else {
  //         setPage(0);
  //       }
  //     }, 100 * 30);
  //   }
    
  //   return () => {
  //     clearInterval(timer);
  //   };
  // }, [data, page]);

  return (
    <DashboardCard style={{ height: '100%' }} >
      <div style={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
        <div className={styles['my-bottom-chart-title']}>
          {intl.get(`${modelPrompt}.title.waitingWorkOrder`).d('待办工作维修单')}
          <Badge count={data.length} showZero className={styles['tab-super-count']} />
        </div>
        <div className={styles['workOrder-content']}>
          <div>
            <div className={styles["workOrder-table"]}>
              <table>
                <thead>
                  <tr>
                    <th style={{ width: '15%' }}>
                      {intl.get(`${modelPrompt}.field.workOrderNum`).d('工单编号')}
                    </th>
                    <th style={{ width: '20%' }}>
                      {intl.get(`${modelPrompt}.field.workOrderName`).d('工单概述')}
                    </th>
                    <th style={{ width: '15%' }}>
                      {intl.get(`${modelPrompt}.field.assetDesc`).d('设备名称')}
                    </th>
                    <th style={{ width: '20%' }}>
                      {intl.get(`${modelPrompt}.field.locationName`).d('位置')}
                    </th>
                    <th style={{ width: '15%' }}>
                      {intl.get(`${modelPrompt}.field.priorityName`).d('优先级')}
                    </th>
                    <th style={{ width: '15%' }}>
                      {intl.get(`${modelPrompt}.field.reportDate`).d('报告时间')}
                    </th>
                  </tr>
                </thead>
                <tbody ref={scrollContainerRef}>
                  {/* {(data || []).slice(page * 10, (page + 1) * 10).map(e => ( */}
                  {(data || []).map(e => (
                    <tr>
                      <td style={{ width: '15%' }}>
                      <Tooltip theme="dark" title={e.woNum}>{e.woNum}</Tooltip>
                      </td>
                      <td style={{ width: '20%' }}>
                        <Tooltip theme="dark" title={e.woName}>{e.woName}</Tooltip>
                      </td>
                      <td style={{ width: '15%' }}>
                        <Tooltip theme="dark" title={e.assetDesc}>{e.assetDesc}</Tooltip>
                      </td>
                      <td style={{ width: '20%' }}>
                        <Tooltip theme="dark" title={e.locationName}>{e.locationName}</Tooltip>
                      </td>
                      <td style={{ width: '15%' }}>
                        <Tooltip theme="dark" title={e.priorityName}>{e.priorityName}</Tooltip>
                      </td>
                      <td style={{ width: '15%' }}>
                        <Tooltip theme="dark" title={e.reportDate}>{e.reportDate}</Tooltip>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </DashboardCard>
  );
};

export default NgReport;
