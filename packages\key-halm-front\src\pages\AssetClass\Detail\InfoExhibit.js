import React, { Component } from 'react';
import { Form, TextField, Output, Lov, Switch, IntlField, DataSet } from 'choerodon-ui/pro';
import { Collapse, Tabs } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';
import { yesOrNoRender } from 'utils/renderer';
import { observer } from 'mobx-react';

import { DefaultMaintainer } from 'alm/components/DefaultMaintainer';
import { getSubAssetClassLevel } from '../api';

import { assetStatusListDS, queryStatusDS } from '../Stores';
import getLang from '../Langs';
import ApplicableStatus from './ApplicableStatus';
import EquipmentAsset from './EquipmentAsset';

@observer
class InfoExhibit extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeKey: 'basicTab', // 当前激活 tab 面板的 key
    };

    this.statusListDS = new DataSet(assetStatusListDS());
    this.queryStatusDS = new DataSet(queryStatusDS());
  }

  // 切换tab的key
  @Bind()
  handleTabsChange(val) {
    this.setState({
      activeKey: val,
    });
  }

  @Bind()
  async handleChangeParentAssetClass(record) {
    const { detailDS } = this.props;
    // 资产分类层级没值切换上级分类时
    if (!detailDS.current.get('classLevelCode') && record?.classLevelCode) {
      const res = await getSubAssetClassLevel({ classLevelCode: record?.classLevelCode });
      detailDS.current.set('classLevelCode', res?.childClassLevelCode);
      detailDS.current.set('classLevelName', res?.childClassLevelName);
      detailDS.current.set('orderNum', res?.childOrderNum);
    }
  }

  @Bind()
  handleChangeClassLevel() {
    const { detailDS } = this.props;
    detailDS.current.set('parentClassId', null);
    detailDS.current.set('parentClassName', null);
  }

  basicDataEditRender() {
    const { isNew, editFlag, detailDS, currentId } = this.props;
    const minClassLevelFlag = detailDS.current
      ? detailDS.current.get('classLevelCode') === 'ASC'
      : false;
    const statusProps = {
      isNew,
      editFlag,
      relationsType: 'ASSET_SET',
      mouduleId: currentId,
      listDS: this.statusListDS,
      queryDS: this.queryStatusDS,
    };

    // const relationIotFlag = detailDS.current.get('relationIotFlag');
    // 隐藏云平台及云账号字段
    const relationIotFlag = false;
    return (
      <>
        <Collapse bordered={false} defaultActiveKey={['OA']}>
          <Collapse.Panel key="OA" header={getLang('BASIC')}>
            <Form dataSet={detailDS} labelWidth={120} columns={3}>
              <TextField name="assetSetNum" restrict="a-zA-Z0-9" />
              <IntlField name="assetSetName" />
              <Lov name="parentClassLov" onChange={this.handleChangeParentAssetClass} />
              <Lov
                name="classLevelLov"
                tableProps={{ pagination: false }}
                onChange={this.handleChangeClassLevel}
              />
              <Lov name="faCategoryLov" />
              <Switch name="enabledFlag" />
              <IntlField name="description" type="multipleLine" colSpan={3} />
            </Form>
          </Collapse.Panel>
        </Collapse>
        {minClassLevelFlag ? (
          <Collapse bordered={false} defaultActiveKey={['OB', 'OC']}>
            <Collapse.Panel key="OB" header={getLang('CONTROL_RULE')}>
              <Form dataSet={detailDS} labelWidth={120} columns={3}>
                <Lov name="codeRuleLov" />
                {/* <Select name="visualLabelRuleCode" /> */}
                <Lov name="standarAssetCodeRuleLov" />
                <Switch name="regularCheckFlag" />
                {/* <Switch name="maintainFlag" /> */}
                <Switch
                  name="relationIotFlag"
                  onChange={() => {
                    detailDS.current.init('configId');
                    detailDS.current.init('configName');
                    detailDS.current.init('platformName');
                    // this.forceUpdate();
                  }}
                />
                {relationIotFlag && (
                  <Lov
                    name="configLov"
                    onChange={record => {
                      detailDS.current.set('configName', record.configName);
                    }}
                  />
                )}
                {relationIotFlag && <TextField disabled name="configName" />}
                <Switch name="meterFlag" />
              </Form>
            </Collapse.Panel>
            <Collapse.Panel key="OC" header={getLang('APPLICABLE_STATUS')}>
              <ApplicableStatus {...statusProps} />
            </Collapse.Panel>
          </Collapse>
        ) : null}
      </>
    );
  }

  basicDataViewRender() {
    const { isNew, editFlag, detailDS, currentId } = this.props;
    const minClassLevelFlag = detailDS.current
      ? detailDS.current.get('classLevelCode') === 'ASC'
      : false;
    const statusProps = {
      isNew,
      editFlag,
      relationsType: 'ASSET_SET',
      mouduleId: currentId,
      listDS: this.statusListDS,
      queryDS: this.queryStatusDS,
    };
    // const relationIotFlag = detailDS.current.get('relationIotFlag');
    // 隐藏云平台及云账号字段
    const relationIotFlag = false;
    return (
      <>
        <Collapse bordered={false} defaultActiveKey={['OA']}>
          <Collapse.Panel key="OA" header={getLang('BASIC')}>
            <Form dataSet={detailDS} labelWidth={120} columns={3}>
              <Output name="assetSetNum" />
              <Output name="assetSetName" />
              <Output name="parentClassLov" />
              <Output name="classLevelLov" />
              <Output name="faCategoryLov" />
              <Output name="enabledFlag" renderer={({ text }) => yesOrNoRender(Number(text))} />
              <Output name="description" type="multipleLine" colSpan={3} />
            </Form>
          </Collapse.Panel>
          {minClassLevelFlag ? (
            <Collapse bordered={false} defaultActiveKey={['OB', 'OC']}>
              <Collapse.Panel key="OB" header={getLang('CONTROL_RULE')}>
                <Form dataSet={detailDS} labelWidth={120} columns={3}>
                  <Output name="codeRuleLov" />
                  {/* <Output name="visualLabelRuleCode" /> */}
                  <Output name="standardAssetCodeRuleMeaning" />
                  <Output
                    name="regularCheckFlag"
                    renderer={({ text }) => yesOrNoRender(Number(text))}
                  />
                  {/* <Output
                    name="maintainFlag"
                    renderer={({ text }) => yesOrNoRender(Number(text))}
                  /> */}
                  <Output
                    name="relationIotFlag"
                    renderer={({ text }) => yesOrNoRender(Number(text))}
                  />
                  {relationIotFlag && <Output name="platformName" />}
                  {relationIotFlag && <Output name="configName" />}
                  <Output name="meterFlag" renderer={({ text }) => yesOrNoRender(Number(text))} />
                </Form>
              </Collapse.Panel>
              <Collapse.Panel key="OC" header={getLang('APPLICABLE_STATUS')}>
                <ApplicableStatus {...statusProps} />
              </Collapse.Panel>
            </Collapse>
          ) : null}
        </Collapse>
      </>
    );
  }

  render() {
    const { activeKey } = this.state;
    const { isNew, editFlag, currentId, history, detailDS } = this.props;
    const equipmentAssetProps = {
      history,
      currentId,
    };
    const defaultMaintainerProps = {
      editFlag,
      moduleId: currentId,
      moduleName: 'ASSET_SET',
    };
    const minClassLevelFlag = detailDS.current
      ? detailDS.current.get('classLevelCode') === 'ASC'
      : false;
    return isNew ? (
      this.basicDataEditRender()
    ) : minClassLevelFlag ? (
      <Tabs activeKey={activeKey} onChange={this.handleTabsChange} defaultActiveKey="basicTab">
        <Tabs.TabPane tab={getLang('BASIC')} key="basicTab">
          {editFlag ? this.basicDataEditRender() : this.basicDataViewRender()}
        </Tabs.TabPane>
        <Tabs.TabPane tab={getLang('ASSET_TAB')} key="asset">
          <EquipmentAsset {...equipmentAssetProps} />
        </Tabs.TabPane>
        <Tabs.TabPane tab={getLang('DEFAULT_MAINTAINER_TAB')} key="defaultMaintainer">
          <DefaultMaintainer {...defaultMaintainerProps} />
        </Tabs.TabPane>
      </Tabs>
    ) : editFlag ? (
      this.basicDataEditRender()
    ) : (
      this.basicDataViewRender()
    );
  }
}

export default InfoExhibit;
