import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';
import getLangs from './Langs';

const organizationId = getCurrentOrganizationId();

function tableDs({ multiSelect, queryParams }) {
  return {
    autoQuery: true,
    primaryKey: 'pointId',
    selection: multiSelect ? 'multiple' : 'single',
    cacheSelection: true,
    fields: [
      {
        name: 'pointName',
        type: 'string',
        label: getLangs('POINT_NAME'),
      },
      {
        name: 'pointCode',
        type: 'string',
        label: getLangs('POINT_CODE'),
      },
      {
        name: 'pointType',
        type: 'string',
        label: getLangs('POINT_TYPE'),
        lookupCode: 'AMTC.CHECKPOINT_TYPE',
      },
      {
        name: 'pointObjectName',
        type: 'string',
        label: getLangs('POINT_OBJ'),
      },
      {
        name:'assetLocationName',
        label:'资产位置'
      }
    ],
    transport: {
      read: ({ params }) => {
        return {
          params: {
            ...params,
            ...queryParams,
            enabledFlag: 1,
          },
          url: `${HALM_MTC}/v1/${organizationId}/points`,
          method: 'GET',
        };
      },
    },
  };
}

function queryDs(queryParams) {
  const { maintSiteIds, maintSiteId } = queryParams || {};
  return {
    fields: [
      {
        name: 'pointName',
        type: 'string',
        label: getLangs('POINT_NAME'),
      },
      {
        name: 'pointCode',
        type: 'string',
        label: getLangs('POINT_CODE'),
      },
      {
        name: 'pointType',
        type: 'string',
        label: getLangs('POINT_TYPE'),
        lookupCode: 'AMTC.CHECKPOINT_TYPE',
      },
      {
        name: 'pointObject',
        type: 'object',
        label: getLangs('POINT_OBJ'),
        disabled: true,
        dynamicProps: {
          lovCode: ({ record }) => {
            const pointType = record.get('pointType');
            if (pointType === 'ASSET') {
              return 'AAFM.ASSET';
            } else if (pointType === 'LOCATION') {
              return 'AMDM.LOCATIONS';
            }
          },
          lovPara: ({ record }) => {
            const pointType = record.get('pointType');
            const params = {
              organizationId,
              maintSiteIds,
              maintSiteId,
            };
            if (pointType === 'ASSET') {
              return { ...params, aclFlag: 1 };
            } else if (pointType === 'LOCATION') {
              return params;
            }
          },
          disabled: ({ record }) => {
            return !record.get('pointType');
          },
        },
        ignore: 'always',
      },
      {
        name: 'pointObjectId',
        type: 'number',
        label: getLangs('POINT_OBJECT'),
        dynamicProps: {
          bind: ({ record }) => {
            const pointType = record.get('pointType');
            if (pointType === 'ASSET') {
              return 'pointObject.assetId';
            } else if (pointType === 'LOCATION') {
              return 'pointObject.assetLocationId';
            }
          },
        },
      },
    ],
    events: {
      update: ({ name, value, record, oldValue }) => {
        if (name === 'pointType') {
          if (!value || value !== oldValue) {
            record.init('pointObject');
            record.init('pointObjectId');
          }
        }
      },
    },
  };
}

export { tableDs, queryDs };
