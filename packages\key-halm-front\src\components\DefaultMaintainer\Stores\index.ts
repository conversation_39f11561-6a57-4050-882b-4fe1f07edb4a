/**
 * 默认维护人员-ds
 * @date 2021-12-29
 * <AUTHOR> <<EMAIL>>
 * @version 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import { isUndefined, isNull } from 'lodash';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { AxiosRequestConfig } from 'axios';
import { HALM_ORI } from 'alm/utils/config';
import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ORI}/v1/${organizationId}`;

const defaultMaintainerDS: () => DataSetProps = () => ({
  selection: false,
  paging: false,
  fields: [
    {
      label: getLang('POSITION_TYPE'),
      name: 'positionType',
      type: FieldType.string,
      lookupCode: 'AORI.POSITION_TYPE',
      required: true,
    },
    {
      label: getLang('SERVICE_TYPE'),
      name: 'serviceTypes',
      type: FieldType.string,
      multiple: true,
      lookupCode: 'AORI.SERVICE_TYPE',
      required: true,
      dynamicProps: {
        disabled: ({ record }) =>
          isUndefined(record.get('positionType')) || isNull(record.get('positionType')),
      },
    },
    {
      label: getLang('MAINT_SITE'),
      type: FieldType.object,
      name: 'maintSiteLov',
      lovCode: 'AMDM.ASSET_MAINT_SITE',
      required: true,
      ignore: FieldIgnore.always,
      lovPara: { tenantId: organizationId, enabledFlag: 1 },
    },
    {
      type: FieldType.number,
      name: 'maintSiteId',
      bind: 'maintSiteLov.maintSiteId',
    },
    {
      label: getLang('MAINT_SITE'),
      type: FieldType.string,
      name: 'maintSiteName',
      bind: 'maintSiteLov.maintSiteName',
    },
    {
      label: getLang('WORK_CENTER'),
      name: 'workcenterName',
      type: FieldType.string,
      dynamicProps: {
        disabled: ({ record }) => {
          const positionType = record.get('positionType');
          return positionType === 'CHECKER';
        },
        required: ({ record }) => {
          const positionType = record.get('positionType');
          return positionType !== 'CHECKER';
        },
      },
    },
    {
      label: getLang('DEFAULT_STAFF'),
      name: 'staffName',
      lovCode: 'HALM.EMPLOYEE_ORG',
      lovPara: {
        tenantId: organizationId,
      },
      dynamicProps: {
        required: ({ record }) => {
          const positionType = record.get('positionType');
          return positionType === 'CHECKER';
        },
        type: ({ record }) => {
          const positionType = record.get('positionType');
          return positionType === 'CHECKER' ? FieldType.object : FieldType.string;
        },
      },
    },
    {
      label: getLang('UPDATE_DATE'),
      name: 'lastUpdateDate',
      type: FieldType.date,
    },
  ],
  transport: {
    read: ({ dataSet }): AxiosRequestConfig => {
      return {
        data: {
          sourceId: dataSet?.getState('moduleId'),
          sourceType: dataSet?.getState('moduleName'),
        },
        method: 'GET',
        url: `${apiPrefix}/default-staff-line`,
      };
    },
    submit: ({ data }): AxiosRequestConfig => {
      return {
        data: data[0],
        method: 'POST',
        url: `${apiPrefix}/default-staff-line`,
      };
    },
    update: ({ data }): AxiosRequestConfig => {
      return {
        data: data[0],
        method: 'PUT',
        url: `${apiPrefix}/default-staff-line`,
      };
    },
    destroy: ({ data }): AxiosRequestConfig => {
      const url = `${apiPrefix}/default-staff-line`;
      return {
        url,
        data: data[0],
        method: 'DELETE',
      };
    },
  },
});

export { defaultMaintainerDS };
