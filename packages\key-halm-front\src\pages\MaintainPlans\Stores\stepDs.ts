import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { HALM_ORI } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ORI}/v1/${organizationId}`;
// const apiPrefix = `https://mock.apifox.cn/m1/964148-0-4e810015/v1/${organizationId}`;

const tableDs = (): DataSetProps => ({
  autoQuery: false,
  primaryKey: 'actOpId',
  selection: false,
  fields: [
    {
      name: 'activityOpNumber',
      type: FieldType.number,
      label: getLang('ACTIVITY_OP_NUM'),
    },
    {
      name: 'actOpName',
      type: FieldType.string,
      label: getLang('ACT_OP_NAME'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: getLang('DESCRIPTION'),
    },
    {
      name: 'defaultJobCode',
      type: FieldType.string,
      label: getLang('JOB_SPECIFIED'),
      lookupCode: 'AMTC.ACTOPDEFJOBCODE',
    },
    {
      name: 'ownerGroupName',
      label: getLang('OWNER_GROUP'),
      type: FieldType.string,
    },
    {
      name: 'ownerName',
      label: getLang('OWNER'),
      type: FieldType.string,
    },
    {
      name: 'standardHour',
      type: FieldType.number,
      label: getLang('STANDARD_TIME'),
    },
    {
      name: 'durationUomCodeMeaning',
      type: FieldType.string,
      label: getLang('DURATION_UOM_CODE'),
    },
    {
      name: 'needSignFlag',
      type: FieldType.number,
      label: getLang('NEED_SIGN_FLAG'),
    },
    {
      name: 'importance',
      label: '重要程度',
      type: FieldType.boolean,
      trueValue: "Y",
      falseValue: "N",
      defaultValue: "Y",
    },
  ],
  transport: {
    read: ({ params }) => {
      return {
        url: `${apiPrefix}/actOp/byActId`,
        method: 'GET',
        params,
      };
    },
    destroy: ({ data }) => {
      const url = `${apiPrefix}/actOp`;
      return {
        url,
        method: 'DELETE',
        data: data[0],
      };
    },
  },
});

const lovBtnDS = ({ maintSiteId, assetId, assetLocationId }) => ({
  autoQuery: true,
  fields: [
    {
      name: 'actLov',
      type: FieldType.object,
      lovCode: 'ALM.MAINTAIN_ACT',
      lovPara: {
        maintSiteId,
        assetId,
        assetLocationId,
      },
    },
  ],
});
export { tableDs, lovBtnDS };
