/**
 * 页面配置-附加字段
 * @since：2021/4/26
 * @author：jxy <<EMAIL>>
 * @copyright Copyright (c) 2021,Hand
 */
import React, { Component } from 'react';
import { DataSet, Table, Button, Modal } from 'choerodon-ui/pro';
import { Row, Col, Icon, Divider } from 'choerodon-ui';
import { yesOrNoRender, dateRender } from 'utils/renderer';
import { Bind } from 'lodash-decorators';
import { isFunction } from 'lodash';
import axios from 'axios';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { HALM_ATN } from 'alm/utils/config';

import { extraTableDS } from './PageConfigurationDS';
import getLang from './Langs';
import RelateExtraFieldList from './RelateExtraFieldList';
import ExtraFieldDrawer from './ExtraFieldDrawer';

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ATN}/v1`;

class ExtraFieldList extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.extraTableDS = new DataSet(extraTableDS());
    this.extraModalRef = React.createRef();
    this.relateModalRef = React.createRef();
  }

  componentDidMount() {
    this.handleSearch();
  }

  @Bind()
  handleSearch() {
    const { scopeCode, currentId, onSearchLine } = this.props;
    if (scopeCode && currentId) {
      this.extraTableDS.setQueryParameter('scopeCode', scopeCode);
      this.extraTableDS.setQueryParameter('scopeId', currentId);
      this.extraTableDS.query();
      if (isFunction(onSearchLine)) {
        onSearchLine();
      }
    }
  }

  @Bind()
  async handleDelete(record) {
    const { onSearchLine } = this.props;
    await this.extraTableDS.delete(
      record,
      record.get('fieldUsedFlag') ? getLang('DELETE_PROMPT') : false
    );
    if (isFunction(onSearchLine)) {
      onSearchLine();
    }
  }

  @Bind()
  handleShowRelationModal() {
    const { scope, sourceCode, transactionTypeId } = this.props;
    const props = {
      scope,
      sourceCode,
      extraTableDS: this.extraTableDS,
      transactionTypeId,
    };
    Modal.open({
      key: Modal.key(),
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      closable: true,
      okText: getLang('SURE'),
      cancelText: getLang('CANCEL'),
      children: <RelateExtraFieldList {...props} ref={this.relateModalRef} />,
      onOk: async () => {
        if (this.relateModalRef.current.relateExtraTableDS.selected.length > 0) {
          await this.handleOk();
        } else {
          notification.error({ message: getLang('NO_SELECT') });
          return false;
        }
      },
    });
  }

  // 批量新增附加字段
  @Bind()
  async handleOk() {
    const { scopeCode, currentId } = this.props;
    const submitData = this.relateModalRef.current.relateExtraTableDS.selected.map(i => {
      i.set('width', 1);
      i.set('scopeCode', scopeCode);
      i.set('scopeId', currentId);
      i.set('creationDate', null);
      i.set('createdBy', null);
      i.set('lastUpdateDate', null);
      i.set('lastUpdatedBy', null);
      i.set('objectVersionNumber', null);
      i.set('_token', null);
      return i.toData();
    });
    await axios({
      url: `${apiPrefix}/${organizationId}/field-confs/batch`,
      method: 'POST',
      data: submitData,
    });
    notification.success();
    this.handleSearch();
  }

  /**
   * 查看/编辑 附加字段
   * @param record
   */
  @Bind()
  handleOpenModal(record) {
    const { lineSync, scope } = this.props;
    const editFlag = !record.fieldUsedFlag;
    const modalProps = {
      lineSync,
      editFlag,
      scope,
      currentData: {
        ...record,
        attrName: getLang('DEFAULT_VALUE'),
        attrFieldCode: 'defaultValue',
      },
    };
    Modal.open({
      key: Modal.key(),
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      closable: true,
      drawer: true,
      style: {
        width: 450,
      },
      title: getLang(record.fieldUsedFlag ? 'VIEW' : 'EDIT'),
      cancelText: getLang(record.fieldUsedFlag ? 'CLOSE' : 'CANCEL'),
      okText: getLang('SAVE'),
      children: <ExtraFieldDrawer {...modalProps} ref={this.extraModalRef} />,
      footer: (okBtn, cancelBtn) => (
        <div>
          {cancelBtn}
          {editFlag && okBtn}
        </div>
      ),
      onOk: async () => {
        if (this.extraModalRef && this.extraModalRef.current) {
          const result = await this.extraModalRef.current.extraFormDS.validate();
          if (result) {
            this.handleUpdateExtraField();
          } else {
            return false;
          }
        }
      },
    });
  }

  // 更新附加字段
  @Bind()
  async handleUpdateExtraField() {
    await this.extraModalRef.current.extraFormDS.submit();
    this.handleSearch();
  }

  @Bind()
  renderDragRow(props) {
    // eslint-disable-next-line no-param-reassign
    delete props.dragColumnAlign;
    return <Table.TableRow {...props} />;
  }

  @Bind()
  async handleDragEnd(dataSet) {
    dataSet.forEach((item, index) => {
      item.set('seqNum', index + 1);
    });
    if (dataSet.updated.length > 0) {
      await this.extraTableDS.submit();
      this.handleSearch();
    }
  }

  // 根据使用的范围区域配置的特殊列
  @Bind()
  handleSpecialColumnsByScope() {
    const { lineSync } = this.props;
    let specialCols = [];
    // 资产事务类型页面配置头配置需要添加行数据同步列
    if (lineSync) {
      specialCols = [
        {
          // TODO: 行数据同步
          name: 'lineDataSynFlag',
          width: 100,
          align: 'left',
          renderer: ({ value }) => yesOrNoRender(value),
        },
      ];
    }
    return specialCols;
  }

  get extraFieldColumns() {
    const { scope, scopeCode } = this.props;
    const commonCols = [
      {
        name: 'seqNum',
        width: 150,
        renderer: ({ value }) => {
          return (
            <div>
              <Icon type="baseline-drag_indicator" />
              {value}
            </div>
          );
        },
      },
      {
        name: 'fieldName',
        width: 150,
      },
      {
        name: 'fieldCode',
        width: 150,
      },
      {
        name: 'sourceCode',
        width: 150,
      },
      {
        name: 'typeCode',
        width: 150,
      },
      {
        name: 'defaultValue',
        renderer: ({ value, record }) => {
          const typeCode = record.get('typeCode');
          let defaultValueMeaning = record.get('defaultValueMeaning') || '';
          if (['MULTI_SELECT', 'MULTI_LOV'].includes(typeCode) && !!defaultValueMeaning) {
            defaultValueMeaning = JSON.parse(defaultValueMeaning).join(',');
          }
          if (typeCode === 'SWITCH') {
            return yesOrNoRender(Number(value));
          } else if (
            [
              'SELECT',
              'LOV',
              'EMPLOYEE',
              'ORGANIZATION',
              'PARTNER',
              'PLATFORM_ORG',
              'MULTI_SELECT',
              'MULTI_LOV',
            ].includes(typeCode)
          ) {
            return <span>{defaultValueMeaning}</span>;
          } else if (typeCode === 'DATE_PICKER') {
            return dateRender(value);
          } else {
            return <span>{value}</span>;
          }
        },
      },
      {
        name: 'width',
        width: 120,
      },
      {
        name: 'requiredFlag',
        width: 100,
        align: 'left',
        renderer: ({ value }) => yesOrNoRender(value),
      },
      scope !== 'PO' && {
        name: 'showOriginalFlag',
        width: 100,
        align: 'left',
        renderer: ({ value }) => yesOrNoRender(value),
      },
      {
        name: 'readonlyFlag',
        width: 100,
        align: 'left',
        renderer: ({ value }) => yesOrNoRender(value),
      },
      scope !== 'PO' && {
        name: 'filterFieldFlag',
        width: 100,
        align: 'left',
        renderer: ({ value }) => yesOrNoRender(value),
      },
      scope !== 'PO' && {
        name: 'appStyleList',
        width: 120,
        align: 'left',
      },
      {
        header: getLang('OPTION'),
        width: 120,
        align: 'center',
        lock: 'right',
        renderer: ({ record }) => {
          const fieldUsedFlag = record.get('fieldUsedFlag');
          // 开启行数据同步的字段，行的操作显示编辑，删除且置灰
          const lineDataSynFlag = record.get('lineDataSynFlag');
          const recordScopeCode = record.get('scopeCode');
          return lineDataSynFlag === 1 && recordScopeCode === 'ASSET_TRANSACTION_LINE' ? (
            <>
              {fieldUsedFlag ? (
                <a onClick={() => this.handleOpenModal(record.toData())}>{getLang('VIEW')}</a>
              ) : (
                <a style={{ color: 'rgba(0, 0, 0, 0.25)' }}>{getLang('EDIT')}</a>
              )}
              <Divider type="vertical" />
              <a style={{ color: 'rgba(0, 0, 0, 0.25)' }}>{getLang('DELETE')}</a>
            </>
          ) : (
            <>
              <a onClick={() => this.handleOpenModal(record.toData())}>
                {getLang(fieldUsedFlag ? 'VIEW' : 'EDIT')}
              </a>
              <Divider type="vertical" />
              <a style={{ color: 'red' }} onClick={() => this.handleDelete(record)}>
                {getLang('DELETE')}
              </a>
            </>
          );
        },
      },
    ];
    // 资产事务类型页面配置头配置需要添加行数据同步列
    if (scope === 'ASSET_TRANSACTION' && scopeCode === 'ASSET_TRANSACTION_HEAD') {
      commonCols.splice(5, 0, {
        name: 'lineDataSynFlag',
        width: 100,
        align: 'left',
        renderer: ({ value = 0 }) => yesOrNoRender(value),
      });
    }

    return commonCols;
  }

  render() {
    const { canAdd } = this.props;
    return (
      <>
        {canAdd && (
          <Row style={{ marginTop: 10 }}>
            <Col span={6} style={{ marginBottom: 10 }}>
              <Button icon="add" color="primary" onClick={this.handleShowRelationModal}>
                {getLang('ADD')}
              </Button>
            </Col>
          </Row>
        )}
        <Row>
          <Table
            key="extraFieldLine"
            columns={this.extraFieldColumns}
            dataSet={this.extraTableDS}
            dragRow
            dragColumnAlign={undefined}
            rowDragRender={{ renderClone: this.renderDragRow }}
            onDragEnd={this.handleDragEnd}
          />
        </Row>
      </>
    );
  }
}

export default ExtraFieldList;
