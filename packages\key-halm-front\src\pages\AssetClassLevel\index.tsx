import React, { FC, useMemo } from 'react';
import formatterCollections from 'utils/intl/formatterCollections';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import { Button, Table, DataSet, TextField } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useDataSet } from 'utils/hooks';
import { ColumnAlign, TableMode } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { operatorRender } from 'utils/renderer';
import uuid from 'uuid/v4';
import { tableDS } from './Stores';
import getLang from './Langs';

const AssetClassLevel: FC<any> = () => {
  const tableDs = useDataSet(() => new DataSet(tableDS()));

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'classLevelName',
        editor: record => record.status === 'add' || record.getState('editing'),
      },
      {
        name: 'classLevelCode',
        editor: record =>
          (record.status === 'add' || record.getState('editing')) && (
            <TextField name="classLevelCode" restrict="a-zA-Z0-9" />
          ),
      },
      {
        header: getLang('OPTION'),
        align: ColumnAlign.center,
        renderer: ({ record }) => {
          const minLevelFlag = record?.get('classLevelCode') === 'ASC';
          const disProps = { disabled: minLevelFlag };
          const operators =
            record?.status === 'add'
              ? [
                  // 新增状态
                  {
                    key: 'save',
                    ele: <a onClick={() => handleSave()}>{getLang('SAVE')}</a>,
                    len: 2,
                    title: getLang('SAVE'),
                  },
                  {
                    key: 'clean',
                    ele: <a onClick={() => handleRemove(record)}>{getLang('CLEAN')}</a>,
                    len: 2,
                    title: getLang('CLEAN'),
                  },
                ]
              : record?.getState('editing')
              ? [
                  // 编辑状态
                  {
                    key: 'save',
                    ele: <a onClick={() => handleSave()}>{getLang('SAVE')}</a>,
                    len: 2,
                    title: getLang('SAVE'),
                  },
                  {
                    key: 'cancel',
                    ele: <a onClick={() => handleCancel(record)}>{getLang('CANCEL')}</a>,
                    len: 2,
                    title: getLang('CANCEL'),
                  },
                ]
              : [
                  // 非编辑状态
                  {
                    key: 'edit',
                    ele: <a onClick={() => handleEdit(record)}>{getLang('EDIT')}</a>,
                    len: 2,
                    title: getLang('EDIT'),
                  },
                  {
                    key: 'addChildren',
                    ele: (
                      <a {...disProps} onClick={() => handleAddChildren(record)}>
                        {getLang('ADD_CHILDREN')}
                      </a>
                    ),
                    len: 4,
                    title: getLang('ADD_CHILDREN'),
                  },
                  {
                    key: 'delete',
                    ele: (
                      <Popconfirm
                        placement="topRight"
                        title={getLang('CONFIRM_DELETE')}
                        onConfirm={() => handleDelete(record)}
                        okText={getLang('SURE')}
                        cancelText={getLang('CANCEL')}
                      >
                        <a {...disProps} style={{ color: 'red' }}>
                          {getLang('DELETE')}
                        </a>
                      </Popconfirm>
                    ),
                    len: 2,
                    title: getLang('DELETE'),
                  },
                ];
          return operatorRender(operators);
        },
      },
    ];
  }, []);

  const handleSave = async () => {
    if (tableDs?.current?.status === 'add' || tableDs?.current?.dirty) {
      if (await tableDs?.current?.validate()) {
        // 新增或者数据变更后保存
        // tableDs.submit(); // 会校验所有变更数据
        await tableDs.forceSubmit(); // 强制提交
        tableDs.query();
      }
    } else {
      // 数据未发生修改，直接改变状态
      tableDs?.current?.setState('editing', false);
    }
  };

  /**
   * @description: 新增顶层节点，页面原根节点下移做为新增的节点的子节点
   * @return {*}
   */
  const handleCreate = () => {
    const oldRoot = tableDs.get(0);
    const orderNum = uuid();
    tableDs.create({ orderNum, levelPath: 0, expand: true }, 0);
    oldRoot?.set('parentOrderNum', orderNum);
  };

  /**
   * @description: 删除新增节点
   * @param {*} record
   * @return {*}
   */
  const handleRemove = record => {
    const parentNode = tableDs.find(
      r => `${r.get('orderNum')}` === `${record.get('parentOrderNum')}`
    );
    const subNode = tableDs.find(r => `${r.get('parentOrderNum')}` === `${record.get('orderNum')}`);
    subNode?.set('parentOrderNum', parentNode?.get('orderNum'));
    tableDs.remove(record);
  };

  const handleEdit = record => {
    record.setState('editing', true);
  };

  const handleCancel = record => {
    record.reset();
    record.setState('editing', false);
  };

  const handleAddChildren = record => {
    // 修改子节点的父节点字段
    const subNode = tableDs.find(r => `${r.get('parentOrderNum')}` === `${record.get('orderNum')}`);
    const orderNum = uuid();
    // 单行保存的关系，多次点击新建下级oriParentOrderNum记录原始上级
    tableDs.create({
      orderNum,
      expand: true,
      parentOrderNum: record.get('orderNum'),
      oriParentOrderNum: record.get('orderNum'),
    });
    subNode?.set('parentOrderNum', orderNum);
  };

  // 删除操作
  const handleDelete = async record => {
    await tableDs.delete(record, false);
    tableDs.query();
  };

  return (
    <PageHeaderWrapper
      title={getLang('HEADER')}
      header={
        <>
          <Button icon="add" color={ButtonColor.primary} onClick={handleCreate}>
            {getLang('CREATE')}
          </Button>
        </>
      }
    >
      <Table
        key="assetClassLevelList"
        customizedCode="AORI.ASSET_CLASS_LEVEL.LIST"
        dataSet={tableDs}
        columns={columns}
        mode={TableMode.tree}
      />
    </PageHeaderWrapper>
  );
};

export default formatterCollections({
  code: ['alm.common', 'alm.component', 'aori.assetClassLevel'],
})(AssetClassLevel);
