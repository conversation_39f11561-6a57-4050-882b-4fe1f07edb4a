import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Icon, Table, Modal, Button, TextField } from 'choerodon-ui/pro';
import notification from 'utils/notification';
import { Bind } from 'lodash-decorators';
import intl from 'utils/intl';
import { HALM_MMT } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';

import { queryValueStatusFlag } from 'alm/services/materialsService';

import InventoryModalContent from '../../MaterialTsMulti/InventoryModalContent';

const organizationId = getCurrentOrganizationId();

const commonViewPrompt = 'ammt.itemRequisition.view';

// 删除
const deleteUrl = `${HALM_MMT}/v1/${organizationId}/item-requisition/line/delete`;
@connect(({ frameWork }) => ({
  frameWork,
}))
export default class ListTable extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      maxLineNum: 0, // 最大行号
    };
  }

  /**
   * 新增
   * 1、先检查有没有服务区域和申请类型；
   * 2、（由工单传入的不可新增）；
   */
  @Bind()
  handleAddLine() {
    const { ds, formDs } = this.props;
    const { maxLineNum } = this.state;
    const { maintSiteId, transTypeId } = formDs.current.toData();
    const lineList = ds.toData();
    let _maxLineNum = 0; // 最大行号

    if (!maintSiteId || !transTypeId) {
      notification.warning({
        message: intl
          .get(`${commonViewPrompt}.message.isEmptyMaintSiteOrTransType`)
          .d('请先维护服务区域和申请类型，再添加行信息！'),
      });
      return;
    }

    if (maxLineNum === 0 && lineList.length > 0) {
      _maxLineNum = Number(lineList[0].maxLineNum) || 0;
    } else if (lineList.length > 0 && ds.created.length === 0) {
      _maxLineNum = Number(lineList[0].maxLineNum) || 0;
    } else {
      _maxLineNum = maxLineNum;
    }
    ds.create({
      lineNum: _maxLineNum + 1,
    });
    this.setState({
      maxLineNum: _maxLineNum + 1,
    });
  }

  /**
   * 清除行数据
   */
  @Bind()
  handleClear(record) {
    const { ds } = this.props;
    const { maxLineNum } = this.state;
    if (Number(record.get('lineNum')) === maxLineNum) {
      this.setState({
        maxLineNum: maxLineNum - 1,
      });
    }
    ds.remove(record);
  }

  /**
   * 编辑行数据
   */
  @Bind()
  async handleEditLine(record) {
    if (record.getState('editing')) {
      record.reset();

      const fromMaintSiteId = record.get('fromMaintSiteId');
      const toMaintSiteId = record.get('toMaintSiteId');
      const itemId = record.get('itemId');
      if (fromMaintSiteId && itemId) {
        const flag = await queryValueStatusFlag({
          itemId,
          maintSiteId: fromMaintSiteId,
        });
        record.set('isEditFromValueStatusFlag', flag);
      } else {
        record.set('isEditFromValueStatusFlag', 0);
      }

      if (toMaintSiteId && itemId) {
        const flag = await queryValueStatusFlag({
          itemId,
          maintSiteId: toMaintSiteId,
        });
        record.set('isEditToValueStatusFlag', flag);
      } else {
        record.set('isEditToValueStatusFlag', 0);
      }
    }
    record.setState({ editing: !record.getState('editing') });
  }

  /**
   * 删除行数据 -
   * 判断是不是数据库的 是则直接调接口删除
   */
  @Bind()
  async handleDeleteLine(record) {
    const { ds } = this.props;
    const deleteMethod = () => {
      request(deleteUrl, {
        method: 'DELETE',
        body: {
          ...record.toData(),
        },
      }).then(res => {
        if (res && !res.failed) {
          notification.success({
            message: intl.get(`${commonViewPrompt}.message.successfulOperation`).d('操作成功'),
          });
          // 删除后刷新最大行号
          ds.query();
        }
      });
    };

    if (ds.created.length > 0) {
      Modal.confirm({
        title: intl.get(`${commonViewPrompt}.message.confirmDeletion`).d('确认删除？'),
        children: intl
          .get(`${commonViewPrompt}.message.willLose`)
          .d('您有行数据未保存，继续操作将失去。'),
        onOk() {
          deleteMethod();
        },
      });
    } else {
      Modal.confirm({
        title: '确认删除',
        onOk: () => deleteMethod(),
      });
    }
  }

  get columns() {
    const { view, isNew, isEdit, pageSource, basicTypeCode } = this.props;
    const fields = {
      basicFields: [
        {
          name: 'lineNum',
          align: 'left',
          width: 60,
        },
        {
          name: 'itemLov',
          align: 'left',
          width: 150,
          // 新增都可以编辑，从工单来的行只可修改数量
          editor: record =>
            record.status === 'add' || (record.getState('editing') && pageSource !== 'WO'),
        },
        {
          name: 'brand',
          align: 'left',
        },
        {
          name: 'model',
          align: 'left',
        },
        {
          name: 'uomName',
          align: 'left',
        },
        {
          name: 'demandQuantity',
          align: 'left',
          editor: record => record.status === 'add' || record.getState('editing'),
        },
        {
          name: 'processedQuantity',
          align: 'left',
        },
        {
          name: 'reqLineStatus',
          align: 'left',
        },
      ],
      fromFields: [
        {
          name: 'fromMaintSiteLov',
          align: 'left',
          width: 150,
          editor: record =>
            (record.status === 'add' || record.getState('editing')) && !!record.get('itemId'),
        },
        {
          name: 'fromStoreroomName',
          align: 'left',
          width: 150,
          editor: (record, name) =>
            (record.status === 'add' || record.getState('editing')) &&
            !!record.get('fromMaintSiteId') &&
            this.renderInventoryField(name, record),
        },
        {
          name: 'fromLocatorName',
          align: 'left',
          width: 150,
          editor: (record, name) =>
            (record.status === 'add' || record.getState('editing')) &&
            !!record.get('fromStoreroomId') &&
            record.get('locatorFlagFrom') === 1 &&
            this.renderInventoryField(name, record),
        },
      ],
      fromValueStatus: {
        name: 'fromValueStatusMeaning',
        editor: (record, name) =>
          (record.status === 'add' || record.getState('editing')) &&
          !!record.get('itemId') &&
          !!record.get('fromMaintSiteId') &&
          !!record.get('fromStoreroomId') &&
          !!record.get('isEditFromValueStatusFlag') &&
          this.renderInventoryField(name, record),
      },
      toFields: [
        {
          name: 'toMaintSiteLov',
          align: 'left',
          width: 150,
          editor: record =>
            (record.status === 'add' || record.getState('editing')) && !!record.get('itemId'),
        },
        {
          name: 'toStoreroomLov',
          align: 'left',
          width: 150,
          editor: record =>
            (record.status === 'add' || record.getState('editing')) &&
            !!record.get('toMaintSiteId'),
        },
        {
          name: 'toLocatorLov',
          align: 'left',
          width: 150,
          editor: record =>
            (record.status === 'add' || record.getState('editing')) &&
            !!record.get('toStoreroomId') &&
            record.get('locatorFlagTo') === 1,
        },
      ],
      toValueStatus: {
        name: 'toValueStatus',
        editor: record =>
          (record.status === 'add' || record.getState('editing')) &&
          !!record.get('itemId') &&
          !!record.get('toMaintSiteId') &&
          !!record.get('isEditToValueStatusFlag'),
      },
      lotNumber: {
        name: 'lotNumber',
        align: 'left',
        width: 100,
        editor: (record, name) => {
          if (['TO'].includes(view)) {
            return (
              (record.status === 'add' || record.getState('editing')) &&
              record.get('lotControlFlag') === 1
            );
          } else {
            return (
              (record.status === 'add' || record.getState('editing')) &&
              record.get('lotControlFlag') === 1 &&
              !!record.get('fromStoreroomId') &&
              this.renderInventoryField(name, record)
            );
          }
        },
      },
      woop: {
        name: 'woopLov',
        align: 'left',
        width: 150,
        editor: record =>
          (record.status === 'add' || record.getState('editing')) && !record.get('woMaterialId'),
      },
      desc: {
        name: 'description',
        align: 'left',
        width: 150,
        editor: record => record.status === 'add' || record.getState('editing'),
      },
      operation: {
        header: intl.get('hzero.common.table.column.option').d('操作'),
        width: 120,
        align: 'left',
        lock: 'right',
        renderer: ({ record }) => {
          return record.status === 'add' ? (
            <>
              <a onClick={() => this.handleClear(record)}>
                {intl.get('hzero.common.button.clear').d('清除')}
              </a>
            </>
          ) : record.getState('editing') ? (
            <>
              <a onClick={() => this.handleEditLine(record)}>
                {intl.get('hzero.common.button.cancel').d('取消')}
              </a>
            </>
          ) : (
            <>
              <a onClick={() => this.handleEditLine(record)} style={{ marginRight: 8 }}>
                {intl.get('hzero.common.button.edit').d('编辑')}
              </a>
              {pageSource !== 'WO' && (
                <a onClick={() => this.handleDeleteLine(record)}>
                  {intl.get('hzero.common.button.delete').d('删除')}
                </a>
              )}
            </>
          );
        },
      },
    };
    let _columns = fields.basicFields;
    switch (basicTypeCode) {
      case 'INV_WO':
        _columns = _columns.concat(fields.fromFields);
        _columns.push(fields.lotNumber, fields.fromValueStatus, fields.woop, fields.desc);
        break;
      case 'INV_OUT':
        _columns = _columns.concat(fields.fromFields);
        _columns.push(fields.lotNumber, fields.fromValueStatus, fields.desc);
        break;
      case 'INV_IN': // 物料申请单实际不存在
        _columns = _columns.concat(fields.toFields);
        _columns.push(fields.lotNumber, fields.toValueStatus, fields.desc);
        break;
      case 'WO_RETURNED_INV_IN':
        _columns = _columns.concat(fields.toFields);
        _columns.push(fields.lotNumber, fields.toValueStatus, fields.woop, fields.desc);
        break;
      case 'INV_TRANSFER':
      case 'INV_IN_CARRY':
      case 'INV_CARRY_TO_STOCK':
        _columns = _columns.concat(fields.fromFields);
        _columns.push(fields.lotNumber, fields.fromValueStatus);
        _columns = _columns.concat(fields.toFields);
        _columns.push(fields.toValueStatus, fields.desc);
        break;
      default:
        break;
    }

    if (isNew || isEdit) {
      _columns.push(fields.operation);
    }
    return _columns;
  }

  @Bind
  handleClearField(name, record) {
    const strategy = {
      clearFromStoreroom: () => {
        record.set('fromStoreroomId', null);
        record.set('fromStoreroomName', null);
      },
      clearFromLocator: () => {
        record.set('fromLocatorId', null);
        record.set('fromLocatorName', null);
      },
      clearLotNumber: () => {
        record.set('lotNumber', null);
      },
      clearFromVS: () => {
        record.set('fromValueStatus', null);
        record.set('fromValueStatusMeaning', null);
      },
    };
    switch (name) {
      case 'fromStoreroomName':
        strategy.clearFromStoreroom();
        strategy.clearFromLocator();
        strategy.clearLotNumber();
        strategy.clearFromVS();
        break;
      case 'fromLocatorName':
        strategy.clearFromLocator();
        strategy.clearLotNumber();
        strategy.clearFromVS();
        break;
      case 'lotNumber':
        strategy.clearFromLocator();
        strategy.clearLotNumber();
        strategy.clearFromVS();
        break;
      case 'fromValueStatusMeaning':
        strategy.clearFromLocator();
        strategy.clearLotNumber();
        strategy.clearFromVS();
        break;
      default:
        break;
    }
  }

  @Bind
  openInventoryModal(props) {
    // 将已存在的库存、货位、批次、新旧件存储
    const {
      fromStoreroomId,
      fromStoreroomName,
      fromLocatorId,
      fromLocatorName,
      locatorFlagFrom,
      lotNumber,
      fromValueStatus,
      fromValueStatusMeaning,
    } = props.dataSource;

    this.selectedInventory = {
      storeroomId: fromStoreroomId,
      storeroomName: fromStoreroomName,
      locatorId: fromLocatorId,
      locatorName: fromLocatorName,
      locatorFlag: locatorFlagFrom,
      lotNumber,
      valueStatus: fromValueStatus,
      valueStatusMeaning: fromValueStatusMeaning,
    };

    this.inventoryModal = Modal.open({
      destroyOnClose: true,
      maskClosable: true,
      keyboardClosable: true,
      closable: true,
      drawer: true,
      style: {
        width: 700,
      },
      key: 'inventoryModal',
      title: intl.get(`ammt.materialTsMulti.title.drawer.sourceInventory`).d('来源库存'),
      children: <InventoryModalContent {...props} />,
      footer: (
        <>
          <Button key="cancel" onClick={this.handleCancelInventory}>
            {intl.get('hzero.common.button.cancel').d('取消')}
          </Button>
          <Button
            key="submit"
            color="primary"
            onClick={() => this.handleSaveInventory(props.record)}
          >
            {intl.get('hzero.common.button.save').d('保存')}
          </Button>
        </>
      ),
    });
  }

  @Bind
  handleCancelInventory() {
    this.inventoryModal.close();
    this.selectedInventory = null;
  }

  @Bind
  handleSaveInventory(record) {
    const {
      storeroomId,
      storeroomName,
      locatorId,
      locatorName,
      locatorFlag,
      lotNumber,
      valueStatus,
      valueStatusMeaning,
    } = this.selectedInventory;
    record.set('fromStoreroomId', storeroomId);
    record.set('fromStoreroomName', storeroomName);
    record.set('fromLocatorId', locatorId);
    record.set('fromLocatorName', locatorName);
    record.set('locatorFlagFrom', locatorFlag);
    record.set('lotNumber', lotNumber);
    record.set('fromValueStatus', valueStatus);
    record.set('fromValueStatusMeaning', valueStatusMeaning);

    this.handleCancelInventory();
  }

  @Bind
  handleSelectInventory(data) {
    const {
      storeroomId,
      storeroomName,
      locatorId,
      locatorName,
      locatorFlag,
      lotNumber,
      valueStatus,
      valueStatusMeaning,
    } = data;

    this.selectedInventory = {
      storeroomId,
      storeroomName,
      locatorId,
      locatorName,
      locatorFlag,
      lotNumber,
      valueStatus,
      valueStatusMeaning,
    };
  }

  /**
   *
   * @param {String} name 所在字段名称
   * @param {Object} i 所在行数据
   */
  @Bind
  renderInventoryField(name, record) {
    const props = {
      record,
      dataSource: record.toData(),
      onSelectInventory: this.handleSelectInventory,
    };
    const suffix = <Icon type="LOV-o" onClick={() => this.openInventoryModal(props)} />;
    return (
      <TextField
        name={name}
        className="c7n-pro-cus-lov"
        clearButton
        suffix={suffix}
        onClear={() => this.handleClearField(name, record)}
      />
    );
  }

  render() {
    const { ds, isNew, isEdit } = this.props;

    return (
      <React.Fragment>
        {isNew || isEdit ? (
          <div style={{ marginBottom: 10, marginTop: 8 }}>
            <Button icon="add" color="primary" onClick={this.handleAddLine}>
              {intl.get(`hzero.common.button.add`).d('新增')}
            </Button>
          </div>
        ) : null}
        <Table
          key="itemRequisitionLine"
          customizedCode="AORI.ITEM_REQ.LINE"
          dataSet={ds}
          columns={this.columns}
          queryFieldsLimit={3}
          style={{ marginTop: isNew || isEdit ? 0 : 16 }}
        />
      </React.Fragment>
    );
  }
}
