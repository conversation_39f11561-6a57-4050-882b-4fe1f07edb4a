import { isUndefined } from 'lodash';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_ATN } from 'alm/utils/config';

import { getDefaultStaff } from './commonApi';


const prefix = `${HALM_ATN}/v1`;
const tenantId = getCurrentOrganizationId();

// 设备或位置带出成本对象
const setCostObjectByAssetOrLoc = (record) => {

  const detail = record.toJSONData() || {};
  const {
    assetId,
    assetLocationId,
    costObjectType,
  } = detail;

  const setCostObjectByAsset = async () => {
    return new Promise(async resolve => {
      const res = await request(`${prefix}/${tenantId}/asset-info/${assetId}`, {
        method: 'GET',
      });
      if (!res?.failed) {
        handleChangeCostObjOfLocOrAsset(res, record);
      }
      resolve();
    });
  };

  const setCostObjectByLoc = async () => {
    return new Promise(async resolve => {
      const res = await request(`${prefix}/${tenantId}/asset-locations/${assetLocationId}`, {
        method: 'GET',
      });
      if (!res?.failed) {
        handleChangeCostObjOfLocOrAsset(res, record);
      }
      resolve();
    });
  };

  return new Promise(async resolve => {
    if (costObjectType) {
      if (assetId) {
        await setCostObjectByAsset();
      } else if (assetLocationId) {
        await setCostObjectByLoc();
      }
    } else {
      record.init('costObjectLov');
    }
    resolve();
  });
};

// 设备或位置与成本对象联动
const handleChangeCostObjOfLocOrAsset = (lovRecord, record) => {
  const costObjectType = record.get('costObjectType');
  if (costObjectType === 'COST_CENTER') {
    if (!lovRecord || !lovRecord.costCenterId) {
      record.init('costObjectLov');
      return;
    }
    record.set('costObjectLov', {
      codeId: lovRecord?.costCenterId,
      name: lovRecord?.costCenterMeaning,
    });
  } else if (costObjectType === 'INTERNAL_ORDER') {
    if (!lovRecord || !lovRecord.internalOrderId) {
      record.init('costObjectLov');
      return;
    }
    record.set('costObjectLov', {
      codeId: lovRecord?.internalOrderId,
      name: lovRecord?.internalOrderMeaning,
    });
  } else if (costObjectType === 'WBS_ELEMENT') {
    if (!lovRecord || !lovRecord.wbsElementId) {
      record.init('costObjectLov');
      return;
    }
    record.set('costObjectLov', {
      codeId: lovRecord?.wbsElementId,
      name: lovRecord?.wbsElementMeaning,
    });
  }
};

/**
 * 根据服务区域里的职务默认规则
 * @param type 'both':同时设置 计划员、负责人 'planner':仅设置计划员 'owner': 仅设置负责人
 * @param record 当前dataSet.current
 * @param serviceType 业务单据类型
 * @param handleRecord 页面手动选择的人员对象
 */
const handlePlannerAndOwner = async ({ queryData, type, record, handleRecord }) => {
  const data = record.toData();
  const newQueryData = {
    assetId: data.assetId,
    assetLocationId: data.assetLocationId,
    maintSiteId: data.maintSiteId,
    ...queryData,
  };
  const defaultStaff = await getDefaultStaff(newQueryData);
  console.log(
    '%c [ defaultStaff ]-53',
    'font-size:13px; background:pink; color:#bf2c9f;',
    defaultStaff
  );
  handleSetValueEmployee({ type, defaultStaff, record, handleRecord });
};

/**
 * 设置计划员和负责人的值
 * @param {*} type 'both':同时设置 计划员、负责人 'planner':仅设置计划员 'owner': 仅设置负责人
 * @param {*} defaultStaff 查询得到的默认职责
 * @param {*} record 当前dataSet.current
 * @param {*} handleRecord 页面手动选择的人员对象
 */
const handleSetValueEmployee = ({ type = '', defaultStaff, record, handleRecord = {} }) => {
  console.log(
    '%c [ record ]-64',
    'font-size:13px; background:pink; color:#bf2c9f;',
    record.toData()
  );
  /**
   * 1. 需要给对应页面设置一个handleRecord来代表手动操作过的工作中心和人员,清空人员则清空handleRecord, handleRecord放在moreProps里
   * 2. 所有的CommonComponent的moreProps多需要传serviceType(AORI.SERVICE_TYPE)
   * 3. employee类型的CommonComponent需要传callback过来用来设置1.的逻辑
   */
  const strategy = {
    planner: () => {
      if (isUndefined(handleRecord.plannerGroupId) || handleRecord.plannerGroupId === '') {
        record.set('plannerGroupId', defaultStaff.plannerGroupId);
        record.set('plannerGroupName', defaultStaff.plannerGroupName);
        record.set('plannerId', defaultStaff.plannerId);
        record.set('plannerName', defaultStaff.plannerName);
      }
    },
    owner: () => {
      if (isUndefined(handleRecord.ownerGroupId) || handleRecord.ownerGroupId === '') {
        record.set('ownerGroupId', defaultStaff.ownerGroupId);
        record.set('ownerGroupName', defaultStaff.ownerGroupName);
        record.set('ownerId', defaultStaff.ownerId);
        record.set('ownerName', defaultStaff.ownerName);
      }
    },
  };
  console.log(handleRecord);
  switch (type) {
    case 'both':
      strategy.planner();
      strategy.owner();
      break;
    case 'planner':
      strategy.planner();
      break;
    case 'owner':
      strategy.owner();
      break;
    default:
      strategy.planner();
      strategy.owner();
      break;
  }
  console.log(
    '%c [ record ]-64',
    'font-size:13px; background:pink; color:#bf2c9f;',
    record.toData()
  );
};

/**
 * 清空“缺陷评估项、故障部位、故障现象”
 */
const handleClearFault = record => {
  record.init('evalItemLov');
  record.init('partCode');
  record.init('riskCode');
  record.init('causeCode');
  record.init('remedyCode');
};

export {
  handleClearFault,
  handlePlannerAndOwner,
  handleChangeCostObjOfLocOrAsset,
  handleSetValueEmployee,
  setCostObjectByAssetOrLoc,
};
