/**
 * @Description: 销售发运平台 - 入口页面DS
 * @Author: <EMAIL>
 * @Date: 2022/2/9 18:02
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'hmes.processYield.field';
const tenantId = getCurrentOrganizationId();

const endUrl = '';

const tableDS = (): DataSetProps => ({
  selection: false,
  cacheSelection: true,
  primaryKey: 'recordId',
  autoLocateFirst: true,
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'AMDM.ASSET_MAINT_SITE',
      // textField: 'organizationName',
      ignore: FieldIgnore.always,
      lovPara: {
        organizationId: tenantId,
      },
      required: true,
    },
    {
      name: 'maintSiteIds',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      bind: 'siteLov.maintSiteId',
    },
    {
      label: intl.get(`${modelPrompt}.assetLocation`).d('资产位置'),
      name: 'assetLocationLov',
      type: 'object',
      lovCode: 'AMDM.LOCATIONS',
      lovPara: { organizationId: tenantId, assetLocationFlag: 1 },
      ignore: 'always',
      multiple: true,
    },
    {
      name: 'assetLocationIds',
      type: 'string',
      bind: 'assetLocationLov.assetLocationId',
    },
    {
      name: 'assetNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('设备编码'),
    },
    {
      name: 'assetName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('设备名称'),
    },
  ],
  fields: [
    {
      name: 'assetLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLov`).d('设备编码'),
      required: true,
      ignore: FieldIgnore.always,
      lovCode: 'AAFM.ASSET_RECEIPT',
      lovPara: { organizationId: tenantId },
    },
    {
      name: 'maintSiteId',
      bind: 'assetLov.maintSiteId',
    },
    {
      name: 'assetId',
      bind: 'assetLov.assetId',
    },
    {
      name: 'assetNum',
      bind: 'assetLov.assetNum',
    },
    {
      name: 'assetName',
      bind: 'assetLov.assetName',
      label: intl.get(`${modelPrompt}.model`).d('设备名称'),
    },
    {
      name: 'locationName',
      bind: 'assetLov.assetLocationName',
      label: intl.get(`${modelPrompt}.model`).d('位置'),
    },
    {
      name: 'maintSiteDescription',
      bind: 'assetLov.maintSiteName',
      label: intl.get(`${modelPrompt}.maintSiteDescription`).d('站点名称'),
    },
    {
      name: 'standardCapacity',
      type: FieldType.number,
      required: true,
      precision: 2,
      label: intl.get(`${modelPrompt}.standardCapacity`).d('标准产能'),
    },
    {
      name: 'enabledFlag',
      type: FieldType.boolean,
      falseValue: 'N',
      trueValue: 'Y',
      defaultValue: 'Y',
      label: intl.get(`${modelPrompt}.enabledFlag`).d('是否启用'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${HALM_ATN}${endUrl}/v1/${tenantId}/asset-data/page/ui`,
        method: 'POST',
        data: {
          ...data,
          maintSiteIds: data.maintSiteIds ? [data.maintSiteIds] : null,
        },
      };
    },
    submit: () => {
      return {
        url: `${HALM_ATN}${endUrl}/v1/${tenantId}/asset-data/batch/update`,
        method: 'POST',
      };
    },
  },
});

const lineTableDS = (): DataSetProps => ({
  selection: false,
  cacheSelection: true,
  primaryKey: 'recordId',
  autoLocateFirst: true,
  fields: [
    {
      name: 'platHaltTime',
      type: FieldType.number,
      precision: 2,
      label: intl.get(`${modelPrompt}.areaLov`).d('计划停机时长（H）'),
      required: true,
    },
    {
      name: 'effectiveDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.effectiveDate`).d('生效日期'),
    },
    {
      name: 'input',
      type: FieldType.number,
      precision: 2,
      label: intl.get(`${modelPrompt}.input`).d('投入量（KG）'),
    },
    {
      name: 'output',
      type: FieldType.number,
      precision: 2,
      label: intl.get(`${modelPrompt}.maintSiteDescription`).d('产出量（KG）'),
    },
    {
      name: 'okQty',
      type: FieldType.number,
      precision: 2,
      label: intl.get(`${modelPrompt}.standardCapacity`).d('合格量（KG）'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${HALM_ATN}${endUrl}/v1/${tenantId}/asset-data-line/page/ui`,
        method: 'POST',
      };
    },
    submit: () => {
      return {
        url: `${HALM_ATN}${endUrl}/v1/${tenantId}/asset-data-line/batch/update`,
        method: 'POST',
      };
    },
  },
});


export { tableDS, lineTableDS };
