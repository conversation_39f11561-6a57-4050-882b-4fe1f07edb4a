import React, { useRef, useEffect, useMemo } from 'react';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import { Radio } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import DashboardCard from './DashboardCard';
import styles from '../index.module.less';

const modelPrompt = 'hmes.productionInfoBoard';


const OEEChangeInfo = ({ data, onChangePeriodType }) => {

  const chartRef:any = useRef(null);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option, data]);

  const option = useMemo(() => {
    const timeList = data.length > 0 ? data[0].periodList.map(e => e.period) : [];
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        textStyle: {
          color: '#fff',
        },
      },
      grid: {
        // left: '15%',
        right: '4%',
        bottom: '8%',
      },
      xAxis: {
        type: 'category',
        splitLine: {
          show: false,
        },
        data: timeList,
        axisLabel: {
          color: '#fff',
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#fff',
          formatter: (val) => `${val * 100} %`,
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
          
        },
      },
      series: data.length > 0 ? data.map(e => {
        return {
          name: e.assetSpecialtyName,
          type: 'line',
          data: e.periodList.map(i => i ? i.oee : undefined),
          label: {
            show: true,
            position: 'top',
            formatter: (val) => `${val.value * 100} %`,
          },
        };
      }) : [],
    }
  }, [data]);

  const handleChange = (value) => {
    onChangePeriodType(value);
  };

  return (
    <DashboardCard>
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-top-chart']}> 
          <div className={styles['my-scroll-board-title']} style={{ paddingBottom: '12px' }}>
            {intl.get(`${modelPrompt}.title.OEEChangeInfo`).d('OEE变化趋势')}
          </div>
          <div className={styles['my-chart-filter']}>
            <div className={styles['container-inventory-select']}>
              <Radio
                mode="button"
                name="periodType"
                value="month"
                defaultChecked
                onChange={handleChange}
              >
                {intl.get(`${modelPrompt}.model.value.year`).d('月')}
              </Radio>
              <Radio mode="button" name="periodType" value="week" onChange={handleChange}>
                {intl.get(`${modelPrompt}.model.value.month`).d('周')}
              </Radio>
              <Radio mode="button" name="periodType" value="day" onChange={handleChange}>
                {intl.get(`${modelPrompt}.model.value.day`).d('日')}
              </Radio>
            </div>
          </div>

          <div className={styles['my-chart']}>
            <div style={{ width: '100%', height: '100%' }}>
              {data.length > 0 && (
                <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardCard>
  );
};

export default OEEChangeInfo;