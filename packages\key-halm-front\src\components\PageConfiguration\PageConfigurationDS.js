/**
 * 页面配置
 * @since：2021/4/26
 * @author：jxy <<EMAIL>>
 * @copyright Copyright (c) 2021,Hand
 */
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

import getLang from './Langs';

const organizationId = getCurrentOrganizationId();

// 查询列表数据
const apiPrefix = `${HALM_ATN}/v1`;

// 可添加的附加字段
function relateExtraTableDS() {
  return {
    cacheSelection: true,
    primaryKey: 'fieldId',
    queryFields: [
      {
        label: getLang('FIELD_NAME'),
        name: 'fieldName',
        type: 'string',
      },
      {
        label: getLang('FIELD_CODE'),
        name: 'fieldCode',
        type: 'string',
      },
    ],
    fields: [
      {
        label: getLang('FIELD_NAME'),
        name: 'fieldName',
        type: 'string',
      },
      {
        label: getLang('FIELD_CODE'),
        name: 'fieldCode',
        type: 'string',
      },
      {
        label: getLang('FIELD_TYPE'),
        name: 'typeCode',
        type: 'string',
        lookupCode: 'AORI.FIELD_TYPE',
      },
    ],
    transport: {
      read: ({ params, data }) => {
        const url = `${apiPrefix}/${organizationId}/field-defs`;
        return {
          url,
          data: {
            ...data,
          },
          params,
          method: 'GET',
        };
      },
    },
  };
}

// 附加字段table
function extraTableDS() {
  return {
    paging: false,
    selection: false,
    primaryKey: 'confId',
    fields: [
      {
        label: getLang('SEQ_NUM'),
        name: 'seqNum',
        type: 'string',
      },
      {
        label: getLang('FIELD_NAME'),
        name: 'fieldName',
        type: 'string',
      },
      {
        label: getLang('FIELD_CODE'),
        name: 'fieldCode',
        type: 'string',
      },
      // TODO:
      {
        label: getLang('SYNC_LINE'),
        name: 'lineDataSynFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        label: getLang('REQUIRED'),
        name: 'requiredFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        label: getLang('DEFAULT_VALUE'),
        name: 'defaultValue',
        type: 'string',
      },
      {
        label: getLang('WIDTH'),
        name: 'width',
        type: 'string',
        lookupCode: 'AORI.FIELD_WIDTH',
      },
      {
        label: getLang('FIELD_TYPE'),
        name: 'typeCode',
        type: 'string',
        lookupCode: 'AORI.FIELD_TYPE',
      },
      {
        label: getLang('SHOW_ORIGINAL_FLAG'),
        name: 'showOriginalFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        label: getLang('READONLY_FLAG'),
        name: 'readonlyFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        label: getLang('FILTER_FIELD_FLAG'),
        name: 'filterFieldFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        label: getLang('FIELD_SOURCE'),
        name: 'sourceCode',
        type: 'string',
        lookupCode: 'AORI.FIELD_SOURCE',
      },
      {
        label: getLang('APP_STYLE'),
        name: 'appStyleList',
        type: 'string',
        multiple: true,
        lookupCode: 'AORI.APP_STYLE',
      },
    ],
    transport: {
      read: ({ params, data }) => {
        const url = `${apiPrefix}/${organizationId}/field-confs/list`;
        return {
          url,
          data: {
            ...data,
          },
          params,
          method: 'GET',
        };
      },
      destroy: ({ data }) => {
        const url = `${apiPrefix}/${organizationId}/field-confs`;
        return {
          url,
          method: 'DELETE',
          data: data[0],
        };
      },
      submit: ({ data }) => {
        return {
          url: `${HALM_ATN}/v1/${organizationId}/field-confs/sort`,
          data,
          method: 'POST',
        };
      },
    },
  };
}

// 附加字段Form
function extraFormDS(dynamicList = []) {
  return {
    primaryKey: 'confId',
    fields: [
      {
        label: getLang('FIELD_CODE'),
        name: 'fieldCode',
        type: 'string',
      },
      {
        label: getLang('FIELD_NAME'),
        name: 'fieldName',
        type: 'string',
      },
      {
        label: getLang('REQUIRED'),
        name: 'requiredFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
        dynamicProps: {
          disabled: ({ record }) => {
            if (record.get('readonlyFlag')) {
              return true;
            }
          },
        },
      },
      {
        label: getLang('SYNC_LINE'),
        name: 'lineDataSynFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
      },
      {
        label: getLang('SHOW_ORIGINAL_FLAG'),
        name: 'showOriginalFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        label: getLang('READONLY_FLAG'),
        name: 'readonlyFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
        dynamicProps: {
          disabled: ({ record }) => {
            if (record.get('requiredFlag')) {
              return true;
            }
          },
        },
      },
      {
        label: getLang('FILTER_FIELD_FLAG'),
        name: 'filterFieldFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'defaultValueSelection',
        type: 'string',
        lookupCode: 'HALM.DEFAULT_VALUE_SELECTION',
        defaultValue: 'CUSTOM',
      },
      ...dynamicList,
      {
        label: getLang('WIDTH'),
        name: 'width',
        type: 'string',
        lookupCode: 'AORI.FIELD_WIDTH',
        defaultValue: '1',
        required: true,
        dynamicProps: {
          disabled: ({ record }) => record.get('typeCode') === 'SWITCH',
        },
      },
      {
        label: getLang('FIELD_TYPE'),
        name: 'typeCode',
        type: 'string',
        lookupCode: 'AORI.FIELD_TYPE',
      },
      {
        label: getLang('ATTR_FIELD_NAME'),
        name: 'attrFieldName',
        type: 'string',
      },
      {
        label: getLang('APP_STYLE'),
        name: 'appStyleList',
        type: 'string',
        multiple: true,
        lookupCode: 'AORI.APP_STYLE',
      },
      {
        label: getLang('FIELD_SOURCE'),
        name: 'sourceCode',
        type: 'string',
        lookupCode: 'AORI.FIELD_SOURCE',
      },
    ],
    transport: {
      submit: ({ data }) => {
        const submitData = data[0];
        if (['MULTI_LOV', 'MULTI_SELECT'].includes(submitData.typeCode)) {
          submitData.defaultValue = JSON.stringify(submitData.defaultValue);
          submitData.defaultValueMeaning = JSON.stringify(submitData.defaultValueMeaning);
        }
        return {
          url: `${HALM_ATN}/v1/${organizationId}/field-confs`,
          data: submitData,
          method: 'POST',
        };
      },
    },
    events: {
      update: ({ name, value, dataSet }) => {
        if (name === 'defaultValueSelection' && value === 'ORIGINAL_VALUE_OF_ASSETS') {
          dynamicList.forEach(field => {
            dataSet?.current?.set(field.name, null);
          });
        }
      },
    },
  };
}

export { relateExtraTableDS, extraTableDS, extraFormDS };
