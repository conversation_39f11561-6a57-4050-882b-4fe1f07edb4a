import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';

import getLangs from '../Langs';

const organizationId = getCurrentOrganizationId();
function tableDs() {
  return {
    autoQuery: false,
    primaryKey: 'actOpId',
    selection: false,
    fields: [
      {
        name: 'activityOpNumber',
        type: 'string',
        label: getLangs('ACTIVITY_OP_NUM'),
      },
      {
        name: 'actOpName',
        type: 'intl',
        label: getLangs('ACT_OP_NAME'),
      },
      {
        name: 'standardHour',
        type: 'string',
        label: getLangs('STANDARD_TIME'),
      },
      {
        name: 'durationUomCode',
        type: 'string',
        label: getLangs('UNIT'),
        lookupCode: 'AMTC.DURATION_UNIT',
      },
      {
        name: 'defaultJobCodeMeaning',
        type: 'string',
        label: getLangs('JOB_SPECIFIED'),
      },
      {
        name: 'needSignFlag',
        type: 'number',
        label: getLangs('NEED_SIGN_FLAG'),
      },
      {
        name: 'description',
        type: 'intl',
        label: getLangs('DESCRIPTION'),
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/actOp/byActId`,
          method: 'GET',
          params,
        };
      },
    },
  };
}

// 工作任务-明细
function woopDs({ relActId, maintSiteId, headerTypeCode = 'ACT' } = {}) {
  return {
    autoQuery: false,
    selection: false,
    autoCreate: false,
    primaryKey: 'actOpId',
    fields: [
      {
        name: 'actOpName',
        type: 'intl',
        label: getLangs('ACT_OP_NAME'),
        maxLength: 240,
        required: true,
      },
      {
        name: 'activityOpNumber',
        type: 'number',
        label: getLangs('ACTIVITY_OP_NUM'),
        required: true,
        precision: 0,
        min: 1,
        pattern: /^\d{1,15}$/,
      },
      {
        name: 'assetLocationLov',
        type: 'object',
        label: getLangs('LOCATION'),
        lovCode: 'AMDM.LOCATIONS',
        dynamicProps: {
          lovPara: () => {
            return {
              organizationId,
              relActId,
            };
          },
        },
      },
      {
        name: 'assetLocationId',
        type: 'number',
        bind: 'assetLocationLov.assetLocationId',
      },
      {
        name: 'assetLocationName',
        type: 'string',
        bind: 'assetLocationLov.locationName',
      },
      {
        name: 'assetLov',
        type: 'object',
        label: getLangs('ASSET'),
        lovCode: 'AAFM.ASSET_NUMBER',
        textField: 'descAndLabel',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              organizationId,
              assetLocationId: record.get('assetLocationId'),
              relActId,
              maintSiteId,
            };
          },
        },
      },
      {
        name: 'assetId',
        type: 'number',
        bind: 'assetLov.assetId',
      },
      {
        name: 'descAndLabel',
        type: 'string',
        bind: 'assetLov.descAndLabel',
      },
      {
        name: 'defaultJobCode',
        type: 'string',
        label: getLangs('JOB_SPECIFIED'),
        lookupCode: 'AMTC.ACTOPDEFJOBCODE',
        required: true,
      },
      {
        name: 'ownerGroupId',
        type: 'number',
      },
      {
        name: 'ownerGroupName',
        type: 'string',
        label: getLangs('OWNER_GROUP'),
        dynamicProps: {
          disabled: ({ record }) => record.get('defaultJobCode') !== 'CUSTOM',
        },
      },
      {
        name: 'ownerId',
        type: 'number',
      },
      {
        name: 'ownerName',
        type: 'string',
        label: getLangs('OWNER'),
        dynamicProps: {
          disabled: ({ record }) => record.get('defaultJobCode') !== 'CUSTOM',
        },
      },
      {
        name: 'standardHour',
        type: 'number',
        label: getLangs('STANDARD_TIME'),
        min: 0,
        dynamicProps: {
          pattern: ({ record }) => {
            const durationUomCode = record?.get('durationUomCode');
            let reg = /^\d{1,4}(\.\d{1,2})?$/g; // 默认按维保计划的 （4,2）
            if (headerTypeCode === 'ACT') {
              reg =
                durationUomCode === 'DAY'
                  ? /^(\d{1,2}(\.\d{1,2})?|[1-2]\d{2}(\.\d{1,2})?|3[0-5]\d(\.\d{1,2})?|36[0-5](\.\d{1,2})?)$/g
                  : /^\d{1,3}(\.\d{1,2})?$/g;
            }
            return reg;
          },
          defaultValidationMessages: ({ record }) => {
            const durationUomCode = record?.get('durationUomCode');
            let mes = {
              patternMismatch: '要求小数点前4位，后2位',
            };
            if (headerTypeCode === 'ACT') {
              mes =
                durationUomCode === 'DAY'
                  ? {
                    patternMismatch: '时间单位为天，要求总天数不超过366且最多两位小数',
                  }
                  : {
                    patternMismatch: '要求小数点前3位，后2位',
                  };
            }
            return mes;
          },
        },
      },
      {
        name: 'durationUomCode',
        type: 'string',
        label: getLangs('UNIT'),
        lookupCode: 'AMTC.DURATION_UNIT',
      },
      {
        name: 'needSignFlag',
        type: 'number',
        label: getLangs('NEED_SIGN_FLAG'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'description',
        type: 'string',
        label: getLangs('DESCRIPTION'),
        maxLength: 240,
      },
      {
        name: 'importance',
        label: '重要程度',
        type: 'boolean',
        trueValue: "Y",
        falseValue: "N",
        defaultValue: "Y",
      },
    ],
    transport: {
      create: ({ data, dataSet }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/actOp`,
          method: 'POST',
          data: {
            ...data[0],
            headerId: dataSet.headerId,
            headerTypeCode,
            tenantId: organizationId,
          },
        };
      },
      update: ({ data, dataSet }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/actOp`,
          method: 'PUT',
          data: {
            ...data[0],
            headerId: dataSet.headerId,
            headerTypeCode,
            tenantId: organizationId,
          },
        };
      },
    },
  };
}

export { tableDs, woopDs };
