/**
 * WorkOrder - 工作单详情页
 * @since 2020-09-21
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */

import React, { Component } from 'react';
import { Bind } from 'lodash-decorators';
import { DataSet, Button, TextField, Pagination } from 'choerodon-ui/pro';
import { Icon, Spin } from 'choerodon-ui';
import intl from 'utils/intl';
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { isNumber } from 'lodash';
import request from 'utils/request';
import { handleNonPermissionErr } from 'alm/utils/response';

import Card from './Card';
import styles from './fullTextSearchStyle.less';

const organizationId = getCurrentOrganizationId();

// 查询列表数据
const queryListUrl = `${HALM_ATN}/v1/${organizationId}/asset-info/list`;

export default class FullTextSearch extends Component {
  constructor(props) {
    super(props);
    this.cardContentRef = React.createRef();
    this.paginationRef = React.createRef();
    this.state = {
      queryListLoading: false,
      dataList: [],
      // 分页数据
      page: 0,
      size: 10,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      selectedData: {}, // 当前选中的数据
    };

    this.searchFormDS = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'detailCondition',
          type: 'string',
        },
      ],
    });
  }

  componentDidMount() {
    this.handleSearch();
    this.handleSetHeight();
  }

  // 设置右侧导航列表的高度
  @Bind()
  handleSetHeight() {
    // eslint-disable-next-line prefer-destructuring
    const clientHeight = document.documentElement.clientHeight;
    const paginationHeight =
      this.paginationRef.current && this.paginationRef.current.element.offsetHeight;
    const cardContentY =
      this.cardContentRef.current && this.cardContentRef.current.getBoundingClientRect().y;
    const height = clientHeight - cardContentY - 14 - paginationHeight;
    if (isNumber(height) && this.cardContentRef.current) {
      this.cardContentRef.current.style.height = `${height}px`;
    }
  }

  /**
   * 左侧列表数据查询
   * @param {Boolean} initQueryParams - 是否初始化查询条件
   */
  @Bind()
  handleSearch(page = 0, size = 10, initQueryParams) {
    this.setState({
      queryListLoading: true,
    });
    if (initQueryParams) {
      this.searchFormDS.current.reset();
    }
    // 查询条件
    const params = this.searchFormDS.current.toData();
    if(params.detailCondition) {
      request(queryListUrl, {
        method: 'POST',
        query: {
          page,
          size,
        },
        body: {
          ...params,
        },
      }).then(res => {
        handleNonPermissionErr(res, true);
        if (res && res.content) {
          this.setState({
            dataList: res.content,
            pagination: {
              total: res.totalElements,
            },
          });
        }
        this.setState({
          queryListLoading: false,
        });
      });
    }
    
  }

  /**
   * 通过查询按钮进行数据查询 - 此时page应该为0
   * @param {*}
   */
  @Bind()
  async handleSearchByBtn() {
    const { size } = this.state;
    await this.setState({
      page: 0,
    });
    this.handleSearch(0, size);
  }

  // 翻页
  @Bind()
  hanldePageChange(page, size) {
    this.setState({
      page: page - 1,
      size,
    });
    this.handleSearch(page - 1, size);
  }

  // 设置数据
  @Bind()
  handleSetClickedData(records) {
    this.setState({
      selectedData: records,
    });
  }

  get paginationConfig() {
    return {
      showSizeChangerLabel: false,
      hideOnSinglePage: false,
      showQuickJumper: false,
      sizeChangerPosition: 'right',
      showTotal: (total, range) => `${range[0]}-${range[1]} / ${total}`,
      // showSizeChanger: false,
      sizeChangerOptionRenderer: ({ text }) => {
        return `${text} ${intl.get('hzero.c7nUI.Pagination.items_per_page').d('条/页')}`;
      },
      // itemRender: this.pagerRenderer,
    };
  }

  @Bind
  handleKeyDown(e) {
    if (e.keyCode === 13) {
      this.handleSearchByBtn();
    }
  }

  render() {
    const { showSearchFlag, onSetShowSearchFlag, onGotoDetail } = this.props;
    const { selectedData } = this.state;
    const basicProps = {
      onGotoDetail,
      selectedData,
      onSetClickedData: this.handleSetClickedData,
    };
    const { queryListLoading, page, size, pagination, dataList } = this.state;
    const maxHeight = this.paginationRef.current
      ? `calc(100vh - ${286 + this.paginationRef.current.element.offsetHeight}px)`
      : 'calc(100vh - 310px)';
    return (
      <React.Fragment>
        {showSearchFlag ? (
          <React.Fragment>
            <div className={styles['full-text-header']}>
              <div className={styles['full-text-header-content']}>
                <span className={styles['full-text-title']}>
                  {' '}
                  {intl
                    .get('aatn.equipmentAsset.view.message.title.equipmentAssetList')
                    .d('设备/资产列表')}
                </span>
                <Icon
                  type={showSearchFlag ? 'format_indent_decrease' : 'format_indent_increase'}
                  onClick={onSetShowSearchFlag}
                  style={{ fontSize: 18, border: 0, cursor: 'pointer' }}
                />
              </div>
            </div>
            <Spin spinning={queryListLoading}>
              <div className={styles['full-text-content']}>
                <div className={styles['content-search']}>
                  <TextField
                    dataSet={this.searchFormDS}
                    name="detailCondition"
                    placeholder={intl
                      .get('aatn.equipmentAsset.view.message.searchNameOrNum')
                      .d('搜索名称或编号')}
                    style={{ width: 'calc(100% - 60px)' }}
                    onKeyDown={this.handleKeyDown}
                  />
                  <Button
                    style={{
                      backgroundColor: '#3889FF',
                      border: '1px solid #3889FF',
                      color: '#fff',
                      fontSize: 12,
                      borderRadius: '0px 4px 4px 0px',
                      alignSelf: 'center',
                    }}
                    onClick={this.handleSearchByBtn}
                    key="create"
                  >
                    {intl.get('hzero.common.button.search').d('查询')}
                  </Button>
                </div>
                <div
                  className={styles['content-cards']}
                  style={{ maxHeight }}
                  ref={this.cardContentRef}
                >
                  {dataList.length ? (
                    dataList.map(one => {
                      const props = {
                        selfData: one,
                      };
                      return <Card {...props} {...basicProps} />;
                    })
                  ) : (
                    <p style={{ textAlign: 'center' }}>
                      {intl.get(`hzero.c7nUI.Table.emptyText`).d('暂无数据')}
                    </p>
                  )}
                </div>
                <Pagination
                  ref={this.paginationRef}
                  page={page + 1}
                  pageSize={size}
                  total={pagination.total}
                  onChange={this.hanldePageChange}
                  {...this.paginationConfig}
                />
              </div>
            </Spin>
          </React.Fragment>
        ) : (
          <React.Fragment>
            <Icon
              type={showSearchFlag ? 'format_indent_decrease' : 'format_indent_increase'}
              onClick={onSetShowSearchFlag}
              style={{ fontSize: 18, padding: 10, border: 0, cursor: 'pointer' }}
            />
          </React.Fragment>
        )}
      </React.Fragment>
    );
  }
}
