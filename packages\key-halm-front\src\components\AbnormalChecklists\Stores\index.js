import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';

import getLangs from '../Langs';

const organizationId = getCurrentOrganizationId();

const queryAbnormalListUrl = `${HALM_MTC}/v1/${organizationId}/wo-checklists/abnormal-list`;

// 异常检查项table
function abTableDs() {
  return {
    paging: false,
    primaryKey: 'checklistId',
    selection: 'single',
    fields: [
      {
        name: 'parentTypeCode',
        label: getLangs('SOURCE_TYPE'),
        type: 'string',
      },
      {
        name: 'checklistName',
        label: getLangs('CK_NAME'),
        type: 'string',
      },
      {
        name: 'actValueDes',
        label: getLangs('CK_VALUE'),
        type: 'string',
      },
      {
        name: 'alarmRecordCode',
        label: getLangs('ALARM_RECORD_CODR'),
        type: 'string',
      },
      {
        name: 'subsequentProcessMeaning',
        label: getLangs('SUB_SEQ_PROCESS'),
        type: 'string',
      },
      {
        name: 'processNum',
        label: getLangs('PROCESS_NUM'),
        type: 'string',
      },
      {
        name: 'processName',
        label: getLangs('PROCESS_NAME'),
        type: 'string',
      },
      {
        name: 'processDesc',
        label: getLangs('PROCESS_DESC'),
        type: 'string',
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: queryAbnormalListUrl,
          method: 'GET',
          params,
        };
      },
    },
    events: {
      load: ({ dataSet }) => {
        dataSet.handleChangeState({
          abcount: dataSet.records.length,
        });
        dataSet.forEach(record => {
          const { alarmRecordCode, processNum } = record.toData();
          // 有告警(有告警记录编码)且告警状态不等于待响应，或没有告警，且后续处理单号不为空
          if (
            (!!alarmRecordCode && record.get('alarmStatus') !== '01PENDING') ||
            (!alarmRecordCode && !!processNum)
          ) {
            Object.assign(record, { selectable: false });
          }
        });
      },
    },
  };
}

export { abTableDs };
