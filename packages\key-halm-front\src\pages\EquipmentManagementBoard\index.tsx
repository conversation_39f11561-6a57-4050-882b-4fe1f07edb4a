import React, { useEffect, useState, useMemo, useCallback } from 'react';
import moment from 'moment';
import { Content } from 'components/Page';
import { FullScreenContainer } from '@jiaminghi/data-view-react';
import { DataSet, Lov, Row, Col, Form, Spin } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import intl from 'utils/intl';
import { uniq } from 'lodash';
import notification from 'utils/notification';
import logo from '../../assets/equipmentBoard/logo.png';
import styles from './index.module.less';
import PersonnelCurrentStatusChart from './components/PersonnelCurrentStatusChart';
import WorkOrderInfo from './components/WorkOrderInfo';
import ElectricInfo from './components/ElectricInfo';
import OEEChangeInfo from './components/OEEChangeInfo';
import { topFilterFormDS } from './Stores';

const modelPrompt = 'alm.equipmentManagementBoard';
const tenantId = getCurrentOrganizationId();

const CurrentTime = () => {
  const [nowTime, setNowTime] = useState(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
  useEffect(() => {
    const timer = setInterval(() => {
      return setNowTime(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
    }, 1000)

    return () => {  // 每次卸载都执行此函数，清楚定时器
      clearTimeout(timer)
    }
  }, []);
  return <span style={{ color: '#36C5FF', fontSize: 18, fontWeight: 600, marginLeft: '3%',marginBottom:'5%' }}> {nowTime} </span>;
};

const Main = ({ topFilterFormDs }) => {
// const Main = ({isFullScreen}) => {
  // const [pieData, setPieData] = useState([]);
  
  const [maintSiteId, setMaintSiteId] = useState(); // 筛选值
  const [periodType, setPeriodType] = useState("month");
  const [workOrderList, setWorkOrderList] = useState([]);
  const [employeeStatusList, setEmployeeList] = useState([]);
  const [powerChangeList, setPowerChangeList] = useState([]);
  const [oeeChatList, setOeeChatList] = useState([]);
  const [initLoading, setInitLoading] = useState<boolean>(false);


  const [timers, setTimers] = useState<number>(1000000000);
  

  useEffect(() => {
    getTimers();
  }, [])

  const onFilterChange = (e) => {
    setMaintSiteId(e?.maintSiteId);
  }

  const getTimers = async () => {
    const url = `/hpfm/v1/${tenantId}/lovs/value/batch?QMS.MANAGEMENT_FREQUENCY=QMS.MANAGEMENT_FREQUENCY`
    const result = await request(url, {
      method: 'GET',
    });
    const data = result['QMS.MANAGEMENT_FREQUENCY'].filter(item => item.value === 'ASSET')
    if (data.length > 0) {
      setTimers(Number(data[0].meaning))
    }
  }


  useEffect(() => {
    const timer = setInterval(fetchAllInfo, 2 * 60 * 1000)
    fetchAllInfo();
    return () => {
      clearInterval(timer)
    };
  }, [maintSiteId, periodType, timers]);

  const fetchAllInfo = useCallback(() => {
    if(maintSiteId && periodType) {
      setInitLoading(true);
      request(`/kd-tznr/v1/${tenantId}/manage-report/ui`, {
        method: 'POST',
        body: {
          periodType,
          maintSiteId,
        },
      }).then(res => {
        if(res.success) {
          setWorkOrderList(res.rows.repairOrderList);
          setEmployeeList(res.rows.employeeStatusList);
          setPowerChangeList(res.rows.powerChangeList);
          setOeeChatList(getOEEChartList(res.rows.oeeChatList));
        } else {
          notification.warning({ description: res.message });
        }
        setInitLoading(false);
      })
    }
  }, [periodType, maintSiteId]);

  useEffect(() => {
    getAllData(periodType);
  }, [periodType]);

  const getAllData = (type) => {
    const periodList = [];
    if(type === 'month') {
      const currentMonth = moment().startOf('year');
      periodList.push(currentMonth.format('YYYY-MM'));
      for(let i = 1; i < 12; i++) {
        periodList.push(currentMonth.add(1, 'month').format("YYYY-MM"))
      }
    } else if(type === "week") {
      for(let i = 4; i >= 0; i--) {
        periodList.push(moment().startOf('week').subtract(i, 'week').format("YYYY-MM-DD"))
      }
    } else if(type === "day") {
      for(let i = 7; i >= 0; i--) {
        periodList.push(moment().subtract(i, 'day').format("YYYY-MM-DD"))
      }
    }
    return periodList;
  };

  const getOEEChartList = useCallback((data) => {
    const periodList = getAllData(periodType);
    const assetList = uniq(data.map(e => e.assetSpecialtyName)).map(e => {
      return {
        assetSpecialtyName: e,
        periodList: periodList.map(i => {
          if(periodType === "week") {
            const weekFirstDay = moment(i);
            const weekLastDay = moment(i).add(1, 'week');
            const obj = data.find(a => {
              return moment(a.period).startOf('day').isBefore(weekLastDay) && moment(a.period).endOf('day').isAfter(weekFirstDay)
            }) || {};
            return {
              ...obj,
              period: `${weekFirstDay.format("MM.DD")}-${weekFirstDay.add(7, 'day').format('DD')}周次`,
            };
          }
          const obj = data.find(a => a.period === i) || {};
          return {
            ...obj,
            period: i,
          };
        }),
      };
    });
    return assetList
  }, [periodType]);

  return (
    <>
      <Content style={{ padding: 0, margin: 0, height: '100%' }}>
        <Spin spinning={initLoading || false} className={styles['center-loading']}>
          <div className={styles['dashboard-container']}>
            <div className={styles['dashboard-title']}>
              <div className={styles['dashboard-title-left']}>
                <img src={logo} alt="img" style={{ width:'35%',height:'100%',marginLeft: '2%' }} />
                <CurrentTime />
              </div>
              <div className={styles['dashboard-title-center']}>
                {intl.get(`${modelPrompt}.title.equipmentManagementBoard`).d('设备管理看板')}
              </div>
              <div className={styles['dashboard-title-right']}>
                <Form dataSet={topFilterFormDs} labelWidth={200}>
                  <Lov
                    dataSet={topFilterFormDs}
                    name="serviceAreaLov"
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    onChange={onFilterChange}
                    maxTagCount={1}
                    maxTagTextLength={3}
                    maxTagPlaceholder={(restValues) => `+${restValues.length}...`}
                  />
                </Form>
              </div>
            </div>

            <div className={styles['dashboard-content']}>
              <Row style={{ height: "100%" }}>
                <Col span={12} className={styles['dashboard-item-content']} style={{ height: "50%" }}>
                  <PersonnelCurrentStatusChart
                    data={employeeStatusList}
                  />
                </Col>
                <Col span={12} className={styles['dashboard-item-content']} style={{ height: "50%" }}>
                  <WorkOrderInfo
                    data={workOrderList}
                  />
                </Col>
                <Col span={12} className={styles['dashboard-item-content']} style={{ height: "50%" }}>
                  <ElectricInfo
                    data={powerChangeList}
                  />
                </Col>
                <Col span={12} className={styles['dashboard-item-content']} style={{ height: "50%" }}>
                  <OEEChangeInfo
                    data={oeeChatList}
                    onChangePeriodType={setPeriodType}
                  />
                </Col>
              </Row>
            </div>
          </div>
        </Spin>
      </Content>
    </>
  );
};

const IncomeInspectionManagement = () => {
  const [isFullScreen, setIsFullScreen] = useState(false); // 是否全屏

  const topFilterFormDs = useMemo(() => new DataSet(topFilterFormDS()), []);

  const windowFullScreenChange = () => {
    if (document.fullscreenElement) {
      setIsFullScreen(true);
    } else {
      setIsFullScreen(false);
    }
  };
  useEffect(() => {
    document.addEventListener('fullscreenchange', windowFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', windowFullScreenChange);
    };
  }, []);

  return (    
    <>
      <div className={styles['screen-container']}>
        {isFullScreen ? (
          <FullScreenContainer>
            <Main
              topFilterFormDs={topFilterFormDs}
              isFullScreen={isFullScreen}
            />
          </FullScreenContainer>
        ) : (
          <Main
            topFilterFormDs={topFilterFormDs}
            isFullScreen={isFullScreen}
          />
        )}
      </div>
    </>
  );
};
export default IncomeInspectionManagement;
