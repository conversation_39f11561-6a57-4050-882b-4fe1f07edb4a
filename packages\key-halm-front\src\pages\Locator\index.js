/**
 * DynamicColumn - 货位
 * @date: 2020-06-30
 * @author: xunjie
 * @version: 0.0.1
 * @copyright Copyright (c) 2019, Hand
 */
import React, { Component } from 'react';
import { Bind } from 'lodash-decorators';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { Button } from 'hzero-ui';
import { Lov, DataSet, Table, Switch } from 'choerodon-ui/pro';
import { Spin } from 'choerodon-ui';
import { operatorRender, enableRender } from 'utils/renderer';
import formatterCollections from 'utils/intl/formatterCollections';

import { tableDS } from './locatorDS';

@formatterCollections({
  code: ['alm.common', 'alm.component', 'ammt.locators'],
})
class Locator extends Component {
  form;

  constructor(props) {
    super(props);
    this.state = {
      detailLoading: false,
    };
    this.tableDS = new DataSet(tableDS());
  }

  componentDidMount() {}

  /**
   * 编辑
   * @param {*} {}
   */
  @Bind()
  handleEdit(rowDS) {
    const { record } = rowDS;
    // record.editing = true;
    record.setState('editing', true);
  }

  /**
   * 取消
   * @param {*} {}
   */
  @Bind()
  handleCancel(rowDS) {
    const { record } = rowDS;
    if (record.status === 'add') {
      this.tableDS.remove(record);
    } else {
      record.reset();
      // record.editing = false;
      record.setState('editing', false);
    }
  }

  /**
   * 新增
   * @param {*} {}
   */
  @Bind()
  handleAdd() {
    const record = this.tableDS.create({}, 0);
    // record.editing = true;
    record.setState('editing', true);
  }

  /**
   * 保存
   * @param {*} {}
   */
  @Bind()
  async handleSubmit(rowDS) {
    const { record } = rowDS;
    await record.validate(true);
    const res = await this.tableDS.submit();
    if (res) {
      // record.editing = false;
      record.setState('editing', false);
    }
  }

  /**
   * 禁用、启用
   * @param {*} {}
   */
  @Bind()
  async handleActiveOrForbidden(rowDS, flag) {
    const { record } = rowDS;
    record.set('enabledFlag', flag);
    const res = await this.tableDS.submit();
    if (res) {
      this.tableDS.query();
    }
  }

  /**
   * 服务区域变更事件
   */
  @Bind()
  onMaintSiteChange(record) {
    record.init('storeroomFieldLov');
  }

  /**
   * 人员变更
   */
  @Bind
  handleChangeEmployee(employeeInfo, record) {
    record.set('employeeId', employeeInfo.employeeId);
    record.set('employeeName', employeeInfo.employeeName);
  }

  /**
   * 表格 列
   */
  get columns() {
    return [
      {
        name: 'locatorNum',
        width: 160,
        editor: record =>  record.status === 'add' ,
      },
      {
        name: 'locatorName',
        width: 160,
        editor: record => record.getState('editing') && !record.get('employeeId'),
      },
      {
        name: 'maintSiteFieldLov',
        width: 160,
        editor: record =>
          record.status === 'add' && <Lov onChange={() => this.onMaintSiteChange(record)} />,
      },
      {
        name: 'storeroomFieldLov',
        width: 160,
        editor: record => record.status === 'add',
      },
      {
        name: 'employeeName',
        width: 160,
      },
      {
        name: 'enabledFlag',
        width: 100,
        editor: record => record.getState('editing') && <Switch />,
        renderer: ({ value }) => enableRender(value),
      },
      {
        name: 'description',
        editor: record => record.getState('editing'),
      },
      {
        header: intl.get('hzero.common.table.column.option').d('操作'),
        width: 120,
        lock: 'right',
        renderer: rowDS => {
          const { record } = rowDS;
          const operators = !record.getState('editing')
            ? [
              {
                key: 'edit',
                ele: (
                  <a onClick={() => this.handleEdit(rowDS)}>
                    {intl.get('hzero.common.button.edit').d('编辑')}
                  </a>
                ),
                len: 2,
                title: intl.get('hzero.common.button.edit').d('编辑'),
              },
            ]
            : [
              {
                key: 'cancel',
                ele: (
                  <a onClick={() => this.handleCancel(rowDS)}>
                    {intl.get('hzero.common.button.cancel').d('取消')}
                  </a>
                ),
                len: 2,
                title: intl.get('hzero.common.button.cancel').d('取消'),
              },
              {
                key: 'save',
                ele: (
                  <a onClick={() => this.handleSubmit(rowDS)}>
                    {intl.get('hzero.common.button.save').d('保存')}
                  </a>
                ),
                len: 2,
                title: intl.get('hzero.common.button.save').d('保存'),
              },
            ];
          return operatorRender(operators);
        },
      },
    ];
  }

  render() {
    const { detailLoading } = this.state;
    return (
      <React.Fragment>
        <Header title={intl.get('ammt.locators.view.message.title.locators').d('库存货位')}>
          <Button icon="plus" type="primary" onClick={() => this.handleAdd()}>
            {intl.get('hzero.common.button.create').d('新建')}
          </Button>
        </Header>
        <Content>
          <Spin spinning={detailLoading}>
            <Table
              key="locatorList"
              customizedCode="AORI.LOCATOR.LIST"
              dataSet={this.tableDS}
              columns={this.columns}
              queryFieldsLimit={3}
            />
          </Spin>
        </Content>
      </React.Fragment>
    );
  }
}

export default Locator;
