/**
 * 固定资产—列表
 * @since：2021/3/9
 * @author：jxy <<EMAIL>>
 * @copyright Copyright (c) 2020,Hand
 */
import React, { Component } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Button, Table } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { dateRender } from 'utils/renderer';
import { isUndefined, isEmpty } from 'lodash';
import { Bind } from 'lodash-decorators';
import withProps from 'utils/withProps';

import { tableDS } from './Stores/ListDS';
import getLang from './Langs';

@formatterCollections({
  code: ['alm.common', 'alm.component', 'aatn.fixedAssets'],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDS());
    return { listDS };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true }
)
@observer
class FixedAssets extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    // this.tableDS = new DataSet(tableDS());
  }

  /**
   * 删除
   */
  @Bind()
  handleDelete() {
    this.props.listDS.delete(this.props.listDS.selected);
  }

  /**
   * 页面跳转
   * @param {string} id - 设备资产id
   * @param {boolean} flag -明细页是否直接进入编辑状态
   */
  @Bind()
  handleGoToDetail(id, flag = false) {
    const linkUrl = isUndefined(id) ? 'create' : `detail/${id}`;
    this.props.history.push({
      pathname: `/afam/fixed-assets/${linkUrl}`,
      state: {
        isEdit: flag,
      },
    });
  }

  // 获取表格展示列
  get columns() {
    return [
      {
        name: 'fixedAssetName',
        width: 150,
        renderer: ({ value, record }) => {
          return <a onClick={() => this.handleGoToDetail(record.data.fixedAssetId)}>{value}</a>;
        },
      },
      { name: 'financialNum', width: 150 },
      { name: 'assetNum', width: 150 },
      { name: 'transferDate', width: 150, renderer: ({ value }) => dateRender(value) },
      { name: 'accountBookName', width: 120 },
      { name: 'faCategoryName', width: 120 },
      { name: 'solidStateCode', width: 120 },
      { name: 'initialOriginalValue', align: 'right', width: 100 },
      { name: 'currentOriginalValue', align: 'right', width: 100 },
      { name: 'accumulatedDepreciation', align: 'right', width: 100 },
      { name: 'ytdDepreciation', align: 'right', width: 100 },
      { name: 'netValue', align: 'right', width: 100 },
      { name: 'residualValue', align: 'right', width: 100 },
      { name: 'description' },
    ];
  }

  render() {
    return (
      <React.Fragment>
        <Header title={getLang('HEADER')}>
          <Button icon="add" color="primary" onClick={() => this.handleGoToDetail()}>
            {getLang('CREATE')}
          </Button>
          <Button
            icon="delete"
            onClick={this.handleDelete}
            disabled={isEmpty(this.props.listDS.selected)}
          >
            {getLang('DELETE')}
          </Button>
        </Header>
        <Content>
          <Table
            key="fixedAssetsList"
            customizedCode="AORI.FIXED_ASSETS.LIST"
            dataSet={this.props.listDS}
            columns={this.columns}
          />
        </Content>
      </React.Fragment>
    );
  }
}
export default FixedAssets;
