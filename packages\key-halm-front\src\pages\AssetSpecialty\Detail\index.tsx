/**
 * 资产专业管理详情
 * @since 2021-11-05
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React, { useState, useEffect, useRef } from 'react';
import { Spin, Collapse, Tabs } from 'choerodon-ui';
import { Button } from 'choerodon-ui/pro/lib';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';

import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';

import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';

import { DefaultMaintainer } from 'alm/components/DefaultMaintainer';

import Basic from '../components/Basic';
import ManagerOrgList from '../components/ManagerOrgList';
import ManagerList from '../components/ManagerList';

import getLang from '../Langs';

const Index = props => {
  const {
    history,
    history: {
      location: {
        state: { editFlag: isEdit },
      },
    },
    dispatch,
    match: {
      params: { assetSpecialtyId },
    },
  } = props;
  const basicRef = useRef({
    baiscDs: Object,
    isValidate: async () => {
      return false;
    },
    getBasicInfo: () => {
      return {};
    },
    refreshInfo: () => {},
    resetDataSet: () => {},
  });
  const managementOrgRef = useRef({
    isValidate: () => {
      return false;
    },
    getManagementOrgList: () => {},
    refreshList: () => {},
    resetDataSet: () => {},
  });
  const managerRef = useRef({
    isValidate: () => {
      return false;
    },
    getManagerList: () => {},
    refreshList: () => {},
    resetDataSet: () => {},
  });
  const [isNew, setIsNew] = useState(false); // 是否新建
  const [editFlag, setEditFlag] = useState(isEdit); // 是否编辑
  const [loading, setLoading] = useState(false); // 加载中

  const tenantId = getCurrentOrganizationId();

  const basicProps = {
    isNew,
    editFlag,
    tenantId,
    assetSpecialtyId,
    ref: basicRef,
  };
  const ManagerOrgListProps = {
    tenantId,
    editFlag,
    assetSpecialtyId,
    ref: managementOrgRef,
  };
  const ManagerListProps = {
    editFlag,
    assetSpecialtyId,
    ref: managerRef,
  };

  const handleEdit = () => {
    setEditFlag(!editFlag);
    if (editFlag) {
      basicRef.current.resetDataSet();
      managementOrgRef.current.resetDataSet();
      managerRef.current.resetDataSet();
    }
  };

  const handleSave = async () => {
    const validated = await basicRef.current.isValidate();
    if (!validated) {
      return;
    }
    setLoading(true);
    const basicInfo = basicRef.current.getBasicInfo();

    let data = { ...basicInfo };
    if (assetSpecialtyId) {
      const result = await Promise.all([
        managementOrgRef.current.isValidate(),
        managerRef.current.isValidate(),
      ]);
      if (!result[0] || !result[1]) {
        setLoading(false);
        return;
      }
      const managementOrgList = managementOrgRef.current.getManagementOrgList();
      const managerList = managerRef.current.getManagerList();
      data = {
        ...data,
        assetMaintenanceList: [], // 不知道后端删除没 先传个空数组
        assetSpecialtyOrgList: managementOrgList,
        assetSpecialtyManagerList: managerList,
      };
    }
    dispatch({
      type: 'assetSpecialty/addAssetSpecialty',
      payload: {
        tenantId,
        data,
      },
    }).then(res => {
      if (res && !res.failed) {
        if (assetSpecialtyId) {
          basicRef.current.refreshInfo();
          managementOrgRef.current.refreshList();
          managerRef.current.refreshList();
          setEditFlag(false);
        } else {
          setEditFlag(false);
          history.push({
            pathname: `/aafm/asset-specialty/detail/${res.assetSpecialtyId}`,
            state: {
              editFlag,
            },
          });
        }
      }
      setLoading(false);
    });
  };

  useEffect(() => {
    if (!assetSpecialtyId) {
      setIsNew(true);
    }
  }, []);

  const defaultMaintainerProps = {
    editFlag,
    moduleId: assetSpecialtyId,
    moduleName: 'ASSET_SPECIALTY',
  };

  return (
    <PageHeaderWrapper
      title={getLang('HEADER')}
      headerProps={{
        backPath: '/aafm/asset-specialty/list',
      }}
      header={
        <>
          {isNew || editFlag ? (
            <>
              <Button color={ButtonColor.primary} onClick={() => handleSave()}>
                {getLang('SAVE')}
              </Button>
              {!isNew && <Button onClick={() => handleEdit()}>{getLang('CANCEL')}</Button>}
            </>
          ) : (
            <Button color={ButtonColor.primary} onClick={() => handleEdit()}>
              {getLang('EDIT')}
            </Button>
          )}
        </>
      }
    >
      <Tabs defaultActiveKey="basic">
        <Tabs.TabPane key="basic" tab={getLang('BASIC_Tab')}>
          <Spin spinning={loading}>
            <Collapse bordered={false} defaultActiveKey={['1', '2', '3', '4']}>
              <Collapse.Panel header={getLang('BASIC_INFO')} key="1">
                <Basic {...basicProps} />
              </Collapse.Panel>
              {!isNew && (
                <Collapse.Panel header={getLang('TOMANAGEMENTORG')} key="2">
                  <ManagerOrgList {...ManagerOrgListProps} ref={managementOrgRef} />
                </Collapse.Panel>
              )}
              {!isNew && (
                <Collapse.Panel header={getLang('TOMANAGER')} key="3">
                  <ManagerList {...ManagerListProps} ref={managerRef} />
                </Collapse.Panel>
              )}
            </Collapse>
          </Spin>
        </Tabs.TabPane>
        {!isNew && (
          <Tabs.TabPane
            tab={intl.get(`alm.component.tab.defaultMaintainer`).d('默认职务人员')}
            key="defaultMaintainer"
          >
            <DefaultMaintainer {...defaultMaintainerProps} />
          </Tabs.TabPane>
        )}
      </Tabs>
    </PageHeaderWrapper>
  );
};

export default formatterCollections({
  code: ['aatn.assetSpecialty', 'alm.common', 'alm.component'],
})(Index);
