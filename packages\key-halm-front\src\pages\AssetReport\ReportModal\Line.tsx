import React, { FC, useCallback, useState } from 'react';
import classnames from 'classnames';
import { isUndefined } from 'lodash';
import { Dropdown, Menu, TextField } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Action } from 'choerodon-ui/pro/lib/trigger/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import notification from 'utils/notification';
import { HALM_ORI } from 'alm/utils/config';
import getLangs from '../Langs';
import ReportModal from './index';
import './index.module.less';

import moreSvg from '../assets/more.svg';
import renameSvg from '../assets/rename.svg';
import historySvg from '../assets/history.svg';
import deleteSvg from '../assets/delete.svg';
import cardOneSvg from '../assets/card-one.svg';
import cardTwoSvg from '../assets/card-two.svg';
import cancelSvg from '../assets/cancel.svg';

interface Props {
  [key: string]: any;
}

const tenantId = getCurrentOrganizationId();
// 修改接口URL
const editUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports`;
// 删除接口URL
const deleteUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports`;
// 资产报表列表URL
const reportListUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports/list`;

const Line: FC<Props> = props => {
  const {
    index,
    record,
    modal,
    reportList,
    currentReport,
    setCurrentReport,
    setReportList,
  } = props;
  const [reportName, setReportName] = useState('');

  const handleRename = useCallback(() => {
    const newList = reportList.map(i => {
      if (i.assetReportId === record.assetReportId) {
        return { ...i, editFlag: 1 };
      } else {
        return i;
      }
    });
    setReportName(record.assetReportName);
    setReportList(newList);
    updateModal(newList);
  }, [reportList]);

  const handleDefault = useCallback(() => {
    const result: any[] = [];
    result.push({
      ...record,
      showFlag: !record.showFlag ? 1 : 0,
    });
    const defaultRecord = reportList.find(
      i => i.showFlag === 1 && record.assetReportId !== i.assetReportId
    );
    if (!isUndefined(defaultRecord)) {
      result.push({
        ...defaultRecord,
        showFlag: 0,
      });
    }
    request(editUrl, {
      method: 'PUT',
      body: result,
    }).then(async res => {
      if (res && !res.failed) {
        const newList: any = await getReportsList();
        setReportList(newList);
        updateModal(newList);
        notification.success({});
      } else {
        notification.warning({
          message: res.message,
        });
      }
    });
  }, [reportList]);

  const handleCardConfigChange = useCallback(
    payload => {
      const result: any[] = [];
      result.push({
        ...record,
        cardConfig: payload,
      });
      if (payload !== 'NO_DISPLAY') {
        const defaultRecord = reportList.find(
          i => i.cardConfig === payload && record.assetReportId !== i.assetReportId
        );
        if (!isUndefined(defaultRecord)) {
          result.push({
            ...defaultRecord,
            cardConfig: 'NO_DISPLAY',
          });
        }
      }
      request(editUrl, {
        method: 'PUT',
        body: result,
      }).then(async res => {
        if (res) {
          const newList: any = await getReportsList();
          setReportList(newList);
          updateModal(newList);
          notification.success({});
        }
      });
    },
    [record]
  );

  const handleDelete = useCallback(() => {
    if (record.showFlag) {
      notification.error({ message: getLangs('CANT_DELETE') });
      return;
    }
    if (record.cardConfig !== 'NO_DISPLAY') {
      notification.error({ message: getLangs('CANT_DELETE_CARD') });
      return;
    }
    request(deleteUrl, {
      method: 'DELETE',
      body: record,
    }).then(async res => {
      if (res) {
        const list: any = await getReportsList();
        setReportList(list);
        updateModal(list);
        notification.success({});
        if (currentReport.assetReportId === record.assetReportId) {
          setCurrentReport({
            ...setCurrentReport,
            assetReportId: undefined,
            noSearchFlag: 1,
          });
        }
      }
    });
  }, [reportList]);

  const handleSubmit = useCallback(
    e => {
      request(editUrl, {
        method: 'PUT',
        body: [{ ...record, assetReportName: e.target.value }],
      })
        .then(async result => {
          if (result) {
            const list: any = await getReportsList();
            setReportList(list);
            updateModal(list);
            notification.success({});
          }
        })
        .catch(err => {
          handleChangeEdit(record, 0);
          console.log(err);
        });
    },
    [reportList]
  );

  const getReportsList = () => {
    return new Promise(resolve => {
      request(reportListUrl, {
        method: 'GET',
      }).then(res => {
        if (res) {
          resolve(res);
        } else {
          resolve([]);
        }
      });
    });
  };

  const updateModal = list => {
    const modalProps = {
      reportList: list,
      setReportList,
    };
    modal.update({
      children: <ReportModal {...modalProps} />,
    });
  };

  const handleChangeEdit = (currentRecord, editFlag) => {
    const newList = reportList.map(i => {
      if (i.assetReportId === currentRecord.assetReportId) {
        return { ...i, editFlag };
      } else {
        return i;
      }
    });
    updateModal(newList);
  };

  const menu = () => (
    <div className={classnames('menu-list')}>
      <Menu>
        <Menu.Item onClick={handleRename}>
          <img alt="" src={renameSvg} />
          <span>{getLangs('RENAME')}</span>
        </Menu.Item>
        <Menu.Item onClick={handleDefault}>
          <img alt="" src={historySvg} />
          <span>{record.showFlag ? getLangs('CANCEL_DEFAULT') : getLangs('DEFAULT')}</span>
        </Menu.Item>
        <Menu.Item onClick={handleDelete}>
          <img alt="" src={deleteSvg} />
          <span>{getLangs('DELETE')}</span>
        </Menu.Item>
        {record.cardConfig === 'NO_DISPLAY' && (
          <Menu.Item
            onClick={() => {
              handleCardConfigChange('FIRST_CARD');
            }}
          >
            <img alt="" src={cardOneSvg} />
            <span>{getLangs('CARD_ONE')}</span>
          </Menu.Item>
        )}
        {record.cardConfig === 'NO_DISPLAY' && (
          <Menu.Item
            onClick={() => {
              handleCardConfigChange('SECOND_CARD');
            }}
          >
            <img alt="" src={cardTwoSvg} />
            <span>{getLangs('CARD_TWO')}</span>
          </Menu.Item>
        )}
        {record.cardConfig !== 'NO_DISPLAY' && (
          <Menu.Item
            onClick={() => {
              handleCardConfigChange('NO_DISPLAY');
            }}
          >
            <img alt="" src={cancelSvg} />
            <span>{getLangs('CANCEL_DISPLAY')}</span>
          </Menu.Item>
        )}
      </Menu>
    </div>
  );

  const handleChangeReportName = value => {
    setReportName(value);
  };

  return (
    <div
      className={classnames({
        'background-none': index % 2 === 0,
        'background-true': index % 2 !== 0,
      })}
    >
      {record.editFlag ? (
        <TextField
          autoFocus
          value={reportName}
          onChange={handleChangeReportName}
          onBlur={handleSubmit}
        />
      ) : (
        <span>{record.assetReportName}</span>
      )}
      {record.showFlag ? <Tag>{getLangs('DEFAULT')}</Tag> : ''}
      {record.cardConfig === 'FIRST_CARD' && <Tag>{getLangs('FIRST_CARD')}</Tag>}
      {record.cardConfig === 'SECOND_CARD' && <Tag>{getLangs('SECOND_CARD')}</Tag>}
      <Dropdown overlay={menu} trigger={[Action.click]}>
        <img alt="" src={moreSvg} />
      </Dropdown>
    </div>
  );
};

export default Line;
