import React, { useEffect, useState, useMemo } from 'react';
import moment from 'moment';
import classNames from "classnames";
import { Content } from 'components/Page';
import { FullScreenContainer } from '@jiaminghi/data-view-react';
import { DataSet, Lov, Row, Col, Form, Spin, Tooltip } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { HALM_ORI } from 'alm/utils/config';
import notification from "utils/notification";
import intl from 'utils/intl';
import logo from '../../assets/equipmentBoard/logo.png';
import styles from './index.module.less';

import { topFilterFormDS } from './Stores';

const modelPrompt = 'alm.equipmentManagementBoard';
const tenantId = getCurrentOrganizationId();

const CurrentTime = () => {
  const [nowTime, setNowTime] = useState(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));

  useEffect(() => {
    const timer = setInterval(() => {
      return setNowTime(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
    }, 1000)

    return () => {  // 每次卸载都执行此函数，清楚定时器
      clearTimeout(timer)
    }
  }, []);
  return <span style={{ className: '#36C5FF', fontSize: 18, fontWeight: 600, marginLeft: '3%',marginBottom:'5%' }}> {nowTime} </span>;
};

const Main = ({ topFilterFormDs }) => {
  
  const [equipmentData, setEquipmentData] = useState([]);
  const [maintSiteId, setMaintSiteId] = useState(); // 筛选值
  const [loading, setLoading] = useState(false);

  const [timers, setTimers] = useState<number>(1000000000);
  
  useEffect(() => {
    if(maintSiteId) {
      fetchAllInfo();
    }
  }, [maintSiteId])

  useEffect(() => {
    getTimers();
  }, [])

  const onFilterChange = (e) => {
    setMaintSiteId(e?.maintSiteId);
  }

  const getTimers = async () => {
    const url = `/hpfm/v1/${tenantId}/lovs/value/batch?QMS.MANAGEMENT_FREQUENCY=QMS.MANAGEMENT_FREQUENCY`
    const result = await request(url, {
      method: 'GET',
    });
    const data = result['QMS.MANAGEMENT_FREQUENCY'].filter(item => item.value === 'ASSET_STATUS')
    if (data.length > 0) {
      setTimers(Number(data[0].meaning))
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      fetchAllInfo();
    }, timers * 60 * 1000)
    return () => {
      clearInterval(timer)
    };
  }, [maintSiteId, timers])

  const fetchAllInfo = () => {
    setLoading(true);
    request(`${HALM_ORI}/v1/${tenantId}/asset-status/management/dashboard/query/for/ui`, {
      method: 'GET',
      query: {
        maintSiteId,
      },
    }).then(res => {
      if(res.failed) {
        notification.warning({ description: res.message });
      } else {
        const result = res.map(item => {
          const list: any = [];
          for (let i = 0; i < item.equipmentList?.length; i += 12) {
            list.push(item.equipmentList.slice(i, i + 12));
          }
          const obj = {
            ...item,
            equipmentList: list,
          }
          return obj
        })
        setEquipmentData(result);
        setLoading(false);
      }
    })
  };

  const getClassName = (quality) => {
    let className = "";
    switch(quality) {
      case 0:
        className = classNames(styles.equipmentBox, styles["equipmentBox-disconnect"]);
        break;
      case 1:
        className = classNames(styles.equipmentBox, styles["equipmentBox-running"]);
        break;
      case 2:
        className = classNames(styles.equipmentBox, styles["equipmentBox-waiting"]);
        break;
      case 3:
        className = classNames(styles.equipmentBox, styles["equipmentBox-stopping"]);
        break;
      case 4:
        className = classNames(styles.equipmentBox, styles["equipmentBox-fault"]);
        break;
      case 5:
        className = classNames(styles.equipmentBox, styles["equipmentBox-maintenance"]);
        break;
      case 6:
        className = classNames(styles.equipmentBox, styles["equipmentBox-offLine"]);
        break;
      default:
        className = styles.equipmentBox;
    }
    return className;
  };

  return (
    <>
      <Content style={{ padding: 0, margin: 0, height: '100%' }}>
        <Spin spinning={loading || false} className={styles['center-loading']}>
          <div className={styles['dashboard-container']}>
            <div className={styles['dashboard-title']}>
              <div className={styles['dashboard-title-left']}>
                <img src={logo} alt="img" style={{ width:'35%',height:'100%',marginLeft: '2%' }} />
                <CurrentTime />
              </div>
              <div className={styles['dashboard-title-center']}>
                {intl.get(`${modelPrompt}.title.equipmentManagementBoard`).d('设备管理看板')}
              </div>
              <div className={styles['dashboard-title-right']}>
                <Form dataSet={topFilterFormDs} labelWidth={200}>
                  <Lov
                    dataSet={topFilterFormDs}
                    name="serviceAreaLov"
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    onChange={onFilterChange}
                    maxTagCount={1}
                    maxTagTextLength={3}
                    maxTagPlaceholder={(restValues) => `+${restValues.length}...`}
                  />
                </Form>
              </div>
            </div>

            <div className={styles['dashboard-content']}>
              <div>
                <Row className={styles.labelContent}>
                  <Col span={10}></Col>
                  <Col span={2} style={{ display: 'flex' }}>
                    <span className={styles.labelTitle}>{intl.get(`${modelPrompt}.disconnect`).d('断开连接')}</span>
                    <div className={classNames(styles.labelLegend, styles['equipmentBox-disconnect'])} />
                  </Col>
                  <Col span={2} style={{ display: 'flex' }}>
                    <span className={styles.labelTitle}>{intl.get(`${modelPrompt}.running`).d('运行中')}</span>
                    <div className={classNames(styles.labelLegend, styles["equipmentBox-running"])} />
                  </Col>
                  <Col span={2} style={{ display: 'flex' }}>
                    <span className={styles.labelTitle}>{intl.get(`${modelPrompt}.waiting`).d('自动待料中')}</span>
                    <div className={classNames(styles.labelLegend, styles["equipmentBox-waiting"])} />
                  </Col>
                  <Col span={2} style={{ display: 'flex' }}>
                    <span className={styles.labelTitle}>{intl.get(`${modelPrompt}.stopping`).d('停机中')}</span>
                    <div className={classNames(styles.labelLegend, styles["equipmentBox-stopping"])} />
                  </Col>
                  <Col span={2} style={{ display: 'flex' }}>
                    <span className={styles.labelTitle}>{intl.get(`${modelPrompt}.bughalt`).d('故障中')}</span>
                    <div className={classNames(styles.labelLegend, styles["equipmentBox-fault"])} />
                  </Col>
                  <Col span={2} style={{ display: 'flex' }}>
                    <span className={styles.labelTitle}>{intl.get(`${modelPrompt}.notRun`).d('维保中')}</span>
                    <div className={classNames(styles.labelLegend, styles["equipmentBox-maintenance"])} />
                  </Col>
                  <Col span={2} style={{ display: 'flex' }}>
                    <span className={styles.labelTitle}>{intl.get(`${modelPrompt}.offLine`).d('设备离线')}</span>
                    <div className={classNames(styles.labelLegend, styles["equipmentBox-offLine"])} />
                  </Col>
                </Row>
                <div>
                  {equipmentData.map(prod => (
                    <div style={{ display: 'flex', marginBottom: '15px' }}>
                      <div className={styles.prodLineBox}>
                        <p>{prod.locationCode}</p>
                      </div>
                      <div style={{width: '100%'}}>
                        {prod.equipmentList.map((row) => (
                          <div style={{ display: 'flex', justifyContent:"flex-start" }}>
                            {row.map(item => (
                              <div className={getClassName(item.quality)}>
                                <p>
                                  <Tooltip title={item.assetName} theme="dark">
                                    {item.assetName}
                                  </Tooltip>
                                </p>
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              
            </div>
          </div>
        </Spin>
      </Content>
    </>
  );
};

const IncomeInspectionManagement = () => {
  const [isFullScreen, setIsFullScreen] = useState(false); // 是否全屏

  const topFilterFormDs = useMemo(() => new DataSet(topFilterFormDS()), []);

  const windowFullScreenChange = () => {
    if (document.fullscreenElement) {
      setIsFullScreen(true);
    } else {
      setIsFullScreen(false);
    }
  };
  useEffect(() => {
    document.addEventListener('fullscreenchange', windowFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', windowFullScreenChange);
    };
  }, []);

  return (    
    <>
      <div className={styles['screen-container']}>
        {isFullScreen ? (
          <FullScreenContainer>
            <Main
              topFilterFormDs={topFilterFormDs}
              isFullScreen={isFullScreen}
            />
          </FullScreenContainer>
        ) : (
          <Main
            topFilterFormDs={topFilterFormDs}
            isFullScreen={isFullScreen}
          />
        )}
      </div>
    </>
  );
};
export default IncomeInspectionManagement;
