/*
 * @Author: DCY <<EMAIL>>
 * @Date: 2020-08-27 16:45:35
 * @LastEditTime: 2022-12-29 14:46:45
 * @Version: 0.0.1
 * @Copyright: Copyright (c) 2019, Hand
 */
import React from 'react';
import { Bind } from 'lodash-decorators';
import classnames from 'classnames';
import { observer } from 'mobx-react';
import { withRouter } from 'react-router';
import { DataSet, Button, Table, Modal } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/interface';
import { tagRenderer } from 'alm/utils/renderer';
import { ASSET_TRANSACTION_BASIC_TYPE } from 'alm/pages/AssetTransactionBasicType/transactionInfoConfig';
import CountingLineListDS from '../Stores/CountingLineListDS';

import { completeDS, getEmployee, lineDS } from '../Stores/ModalDS';
import getLang from '../Langs';

import Drawer from './Drawer';
import DealModal from './Modal';
import styles from './index.module.less';

@withRouter
@formatterCollections({
  code: ['alm.common', 'alm.component', 'aatn.countingLine'],
})
@observer
export default class countingLineListPage extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.completeDs = new DataSet(completeDS());
    this.listDs = new DataSet(CountingLineListDS(props?.customizedCode));
    this.listDs.setState('permissionData', props?.permissionData ?? {});
    this.listDs.setQueryParameter('batchId', props?.batchId);
    this.listDs.queryDataSet.setState('batchId', props?.batchId);
    this.listDs.setQueryParameter('taskId', props?.taskId);
  }

  @Bind()
  handleSearch() {
    this.listDs.query();
  }

  employeeDs = new DataSet(getEmployee());

  get columns() {
    const { hasOption = true } = this.props;
    return [
      { name: 'assetNum', lock: 'left' },
      {
        name: 'assetName',
        width: 180,
        lock: 'left',
      },
      {
        name: 'countingStatus',
        align: ColumnAlign.center,
        lock: 'left',
        renderer: tagRenderer,
      },
      { name: 'countingPersonName', width: 100 },
      {
        name: 'processStatus',
        align: ColumnAlign.center,
        renderer: tagRenderer,
        width: 100,
      },
      { name: 'handleTypeMeaning', width: 150 },
      {
        name: 'relateNum',
        width: 100,
        renderer: ({ value, record }) => {
          return value ? (
            <a onClick={() => this.handleGotoRelate(record.get('relateId'), record)}>{value}</a>
          ) : (
            ''
          );
        },
      },
      { name: 'handlePersonName', width: 100 },
      { name: 'handleTime' },
      { name: 'handleDescription' },
      { name: 'taskName', width: 150 },
      {
        name: 'lineNumber',
        width: 200,
      },
      hasOption && {
        header: intl.get('hzero.common.table.column.option').d('操作'),
        lock: 'right',
        width: 120,
        renderer: ({ record }) => [
          <a onClick={() => this.handleOpenDrawer(record)}>
            {intl.get('hzero.common.button.detail').d('详情')}
          </a>,
          <a
            style={{ margin: '0 0 0 16px' }}
            disabled={!record.get('dealFlag')}
            onClick={() => this.handleComplete(record)}
          >
            {intl.get('hzero.common.button.dealwith').d('处理')}
          </a>,
        ],
      },
    ];
  }

  @Bind()
  handleGotoRelate(key, record) {
    const handleType = record.get('handleType');
    const basicCode = record.get('basicCode');
    const transactionTypeId = record.get('transactionTypeId');
    const transType = ASSET_TRANSACTION_BASIC_TYPE.find(i => i.basicCode === basicCode) || {};
    let pathname = '';
    switch (handleType) {
      // 资产
      case 'MATCH_ASSET':
        pathname = `/aafm/equipment-asset/detail/${key}`;
        break;
      // 资产事务处理
      case 'ASSET_TRANS_PROCESS':
        pathname = `/aatn/asset-transaction-basic-type/${transType.routeType}/detail/${transactionTypeId}/${key}`;
        break;
      default:
        pathname = `/aafm/equipment-asset/detail/${key}`;
        break;
    }
    this.props.history.push({
      pathname,
    });
  }

  @Bind()
  handleOpenDrawer(record) {
    const props = {
      detail: record.toData(),
    };
    const footer = [];
    if (record.get('dealFlag')) {
      footer.push(
        <Button color="primary" onClick={() => this.handleComplete(record, drawer)}>
          {intl.get('hzero.common.button.dealwith').d('处理')}
        </Button>
      );
    }
    const drawer = Modal.open({
      key: 'detailModal',
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      style: {
        width: 500,
      },
      closable: true,
      drawer: true,
      footer,
      children: <Drawer {...props} />,
    });
  }

  @Bind()
  handleComplete(record, drawer) {
    const employee = this.employeeDs.toData()[0] || {};
    const detail = record.toData();
    const lineDs = new DataSet(lineDS(detail));
    lineDs.setState('permissionData', this.props?.permissionData ?? {});
    lineDs
      .query()
      .then(() => {
        const props = {
          drawer,
          employee,
          detail: record.toData(),
          lineDS: lineDs,
          completeDS: this.completeDs,
          onSearch: this.handleSearch,
        };
        Modal.open({
          key: 'dealModal',
          className: classnames(styles['deal-modal']),
          title: getLang('DEAL_MODAL_TITLE'),
          maskClosable: true, // 点击蒙层是否允许关闭
          keyboardClosable: true, // 按 esc 键是否允许关闭
          destroyOnClose: true, // 关闭时是否销毁
          style: {
            width: 500,
          },
          closable: true,
          children: <DealModal {...props} />,
          footer: [],
        });
      })
      .catch(() => {
        drawer?.close();
      });
  }

  render() {
    return (
      <>
        <Table
          key="countingLineList"
          customizedCode={this.props?.customizedCode}
          searchCode={this.props?.customizedCode}
          dataSet={this.listDs}
          columns={this.columns}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQueryPlaceholder: getLang('COUNTING_LINE_LIST_PLACEHOLDER'),
            queryFieldsLimit: 4,
          }}
        />
      </>
    );
  }
}
