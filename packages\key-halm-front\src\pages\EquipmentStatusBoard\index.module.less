.center-loading {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
}

.screen-container {
  width: 100%;
  height: 100%;

  :global {
    .page-content-wrap {
      height: 100%;
      margin: 0;
    }

    .page-content {
      height: 100%;
      margin: 0;
      .c7n-spin-nested-loading,
      .c7n-spin-container {
        height: 100%;
      }
    }
  }
}

.dashboard-container {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
  // padding: 20px 36px;
  overflow: hidden;
  color: #fff;
  background: url('../../assets/equipmentBoard/bg.png') center center no-repeat;
  background-position: top;
  background-size: cover;
}

.dashboard-title {
  display: flex;
  width: 100%;
  height: 80px;
  height: 10vh;
  min-height: 80px;
  .dashboard-title-left {
    display: grid;
    width: 30%;    
    height: 10%;
  }

  .dashboard-title-center {
    display: flex;
    flex-grow: 0;
    align-items: center;
    justify-content: center;
    width: 40%;
    height: 70%;
    color: #65ffff;
    font-weight: bold;
    font-size: 32px;
    letter-spacing: 0.1em;
  }

  .dashboard-title-right {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    align-items: flex-end;
    justify-content: right;
    width: 30%;
    height: 10%;
    // margin: 38px 10px;
    margin: 3% 1%;
    :global {
      .c7n-pro-field-label label {
        color: #fff;
      }
      .c7n-pro-output.c7n-pro-output,
      .c7n-pro-select-wrapper.c7n-pro-select-wrapper label input,
      .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-focused .c7n-pro-select {
        color: #65ffff;
      }
    }
  }
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-wrap: wrap;
  width: 100%;
  height: calc(100% - 10vh);
  overflow-y: scroll;
  .dashboard-left-side {
    width: 66%;
    height: 100%;
  }

  .dashboard-right-side {
    width: 32%;
    height: 100%;
  }

  .dashboard-item-content {
    padding: 5px 10px 10px 5px;
  }
  .dashboard-item-left-bottom {
    width: 100%;
    height: 60%;
    // margin: 1% 0;
    // background-image: url('./assets/task.png');
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
  }
  .dashboard-item-right-bottom {
    width: 100%;
    height: 49%;
    margin: 2% 0;
    background-image: url('../../assets/equipmentBoard/task.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.labelTitle{
  color: #fff;
  font-size: 14px;
  margin-right: 10px;
}

.labelLegend{
  width: 50px;
  height: 20px;
  border: 1px #fff solid;
  border-radius: 8px;
}

.prodLineBox{
  width: 6%;
  padding: 0 15px;
  background-color: #65ffff33;
  border-radius: 5px;
  margin:0 15px 5px 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #65ffff;
  p{
    text-align: center;
    margin: 0;
    font-weight: 700;
    font-size: 13px;
    color: #65ffff;
  }
}

.labelContent {
  padding: 15px 0;
}

.equipmentBox{
  width: calc( (100% - 60px) / 12  );
  padding: 10px;
  height: 50px;
  border-radius: 5px;
  margin-bottom: 5px;
  margin-right: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  p{
    text-align: center;
    font-weight: 700;
    font-size: 12px;
    margin: 0;
  }
}

.equipmentBox-disconnect {
  background: #d9dcd6;
}

.equipmentBox-running {
  background: #5e9362;
}

.equipmentBox-waiting {
  background: #eeb82d;
}

.equipmentBox-stopping {
  background: #efa267;
}

.equipmentBox-fault {
  background: #d92d3c;
}

.equipmentBox-maintenance {
  background: #042068;
}

.equipmentBox-offLine {
  background: #40aae7;
}