/**
 * 故障分析Tab
 * @date: 2020-09-30
 * @author: DCY <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component } from 'react';
import intl from 'utils/intl';
import { Row, Col, Tooltip } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';
import { routerRedux } from 'dva/router';
import { DataSet, Table } from 'choerodon-ui/pro';
import { dateRender } from 'utils/renderer';
import { woMalfunctionDS } from '../../Stores/MalfunctionTabDS';
import EvalItemTable from './EvalItemTable';
import './index.less';

const prefix = 'aatn.equipmentAsset.model.equipmentAsset';
class MalfunctionTab extends Component {
  constructor(props) {
    super(props);
    this.state = {
      errorArr: [],
    };
  }

  woMalfunctionDS = new DataSet(woMalfunctionDS());

  componentDidMount() {
    this.props.onSearchMal();
  }

  componentDidUpdate(preProps) {
    if (preProps.assetId !== this.props.assetId) {
      this.props.onSearchMal();
    }
  }

  get columns() {
    const { errorArr } = this.state;
    return [
      {
        name: 'woNum',
        width: 100,
        renderer: ({ value, record }) => (
          <a onClick={() => this.handleSwitchToWo(record.data.woId || '', value)}>{value}</a>
        ),
      },
      {
        name: 'woMeaning',
        width: 150,
      },
      {
        name: 'malfunctionTime',
        width: 100,
        renderer: ({ value }) => dateRender(value),
      },
      {
        name: 'errorDetail',
        // width: 300,
        renderer: ({ record }) => {
          const tempArr = [];
          errorArr.forEach(ele => {
            if (record.data[ele]) {
              tempArr.push(record.data[ele]);
            }
          });
          const text = tempArr.length > 0 && tempArr.join('/');
          return (
            <Tooltip placement="top" title={text}>
              {text}
            </Tooltip>
          );
        },
      },
    ];
  }

  /**
   * 跳转到指定工单
   */
  @Bind()
  handleSwitchToWo(woId, woNum) {
    if (woId) {
      this.props.dispatch(
        routerRedux.push({
          pathname: `/amtc/work-order/detail/${woId}/${woNum}`,
        })
      );
    }
  }

  // 获取故障工单数据
  @Bind()
  async getItemList(record) {
    const { assetId } = this.props;
    const res = await this.getEvalHierarchyList(record.evalItemId);
    this.woMalfunctionDS.setQueryParameter('assetId', assetId);
    this.woMalfunctionDS.setQueryParameter('evalItemId', record.evalItemId);
    this.woMalfunctionDS.setQueryParameter('errorArr', res);

    if (record?.basicTypeCode === 'PART') {
      this.woMalfunctionDS.setQueryParameter('partCodeId', record.asmtCodeId);
      this.woMalfunctionDS.setQueryParameter('riskCodeId', null);
      this.woMalfunctionDS.setQueryParameter('causeCodeId', null);
      this.woMalfunctionDS.setQueryParameter('remedyCodeId', null);
    } else if (record?.basicTypeCode === 'RISK') {
      this.woMalfunctionDS.setQueryParameter('riskCodeId', record.asmtCodeId);
      this.woMalfunctionDS.setQueryParameter('causeCodeId', null);
      this.woMalfunctionDS.setQueryParameter('remedyCodeId', null);
    } else if (record?.basicTypeCode === 'CAUSE') {
      this.woMalfunctionDS.setQueryParameter('causeCodeId', record.asmtCodeId);
      this.woMalfunctionDS.setQueryParameter('remedyCodeId', null);
    } else if (record?.basicTypeCode === 'REMEDY') {
      this.woMalfunctionDS.setQueryParameter('remedyCodeId', record.asmtCodeId);
    } else if (!record?.basicTypeCode) {
      this.woMalfunctionDS.setQueryParameter('partCodeId', null);
      this.woMalfunctionDS.setQueryParameter('riskCodeId', null);
      this.woMalfunctionDS.setQueryParameter('causeCodeId', null);
      this.woMalfunctionDS.setQueryParameter('remedyCodeId', null);
    }
    this.woMalfunctionDS.query();
  }

  // 获取评估字段层次
  @Bind()
  async getEvalHierarchyList(val) {
    const { dispatch, tenantId } = this.props;
    let res = await dispatch({
      type: 'equipmentAsset/getEvalHierarchyList',
      payload: { tenantId, evaluateId: val },
    });
    res = res.map(item => {
      return `${item.basicTypeCode.toLowerCase()}CodeMeaning`;
    });
    this.setState({
      errorArr: res,
    });
    return res;
  }

  render() {
    const { tenantId, malTreeList, malLoading } = this.props;
    const evalListProps = {
      tenantId,
      dataSource: malTreeList || [],
      selectItem: this.getItemList,
      loading: malLoading,
    };
    return (
      <React.Fragment>
        {malTreeList.length > 0 ? (
          <Row gutter={16} className="malfunction-tab">
            <Col span={7}>
              <h6>{intl.get(`${prefix}.asmtCodeName`).d('缺陷评估项')}</h6>
              <EvalItemTable {...evalListProps} />
            </Col>
            <Col span={17}>
              <Table
                key="equipmentAssetWoMalfun"
                customizedCode="AORI.EQP_ASSET.WO_MALFUN"
                dataSet={this.woMalfunctionDS}
                columns={this.columns}
              />
            </Col>
          </Row>
        ) : (
          <div className="tab-no-data">{intl.get(`hzero.c7nUI.Table.emptyText`).d('暂无数据')}</div>
        )}
      </React.Fragment>
    );
  }
}
export default MalfunctionTab;
