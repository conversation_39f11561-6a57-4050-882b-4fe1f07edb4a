/**
 * 资产分类-多语言
 * <AUTHOR>
 * @date 2021-03-01
 * @version: 0.0.1
 * @copyright: Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLang = key => {
  const PREFIX = 'aatn.assetSet';
  const LANGS = {
    PREFIX,
    ...getCommonLangs(),
    // titile
    HEADER: intl.get(`${PREFIX}.view.message.title.assetClass`).d('资产分类'),
    DETAIL: intl.get(`${PREFIX}.view.message.title.assetClassDetail`).d('资产分类明细'),

    FILTER: intl.get(`${PREFIX}.view.message.title.filter`).d('筛选'),
    RELATION: intl.get(`${PREFIX}.view.message.title.relation`).d('关联属性组'),
    ORDER_LIST: intl.get(`${PREFIX}.view.message.orderList`).d('资产分类列表'),
    NO_DATA: intl.get(`${PREFIX}.view.message.noData`).d('暂无数据'),
    SEARCH_INFO: intl.get(`${PREFIX}.view.message.searchInfo`).d('搜索名称或编号'),
    // panel
    APPLICABLE_STATUS: intl.get(`${PREFIX}.panel.applicableStatus`).d('适用状态'),
    // TabPane
    ATTRIBUTE_SET_TAB: intl.get(`${PREFIX}.tab.attributeSet`).d('属性组'),
    ASSET_TAB: intl.get(`${PREFIX}.tab.asset`).d('设备/资产'),
    DEFAULT_MAINTAINER_TAB: intl.get(`alm.component.tab.defaultMaintainer`).d('默认职务人员'),
    // button
    SELECT: intl.get(`${PREFIX}.button.select`).d('选择'),
    RELOAD: intl.get(`${PREFIX}.button.reload`).d('刷新'),
    ADD: intl.get(`${PREFIX}.button.add`).d('添加'),
    VIEW: intl.get(`${PREFIX}.button.view`).d('查看'),
    // model
    ASSET_CLASS: intl.get(`${PREFIX}.model.assetSet.assetClass`).d('资产分类'),
    BRAND: intl.get(`${PREFIX}.model.assetSet.brand`).d('品牌/厂商'),
    MODEL: intl.get(`${PREFIX}.model.assetSet.model`).d('规格/型号'),
    NUM: intl.get(`${PREFIX}.model.assetSet.num`).d('编号'),

    ASSET_SPECIALTY: intl.get(`${PREFIX}.model.assetSet.assetSpecialty`).d('资产专业分类'),
    ENABLED_FLAG: intl.get(`${PREFIX}.model.assetSet.enabledFlag`).d('是否启用'),
    ASSET_IMPORTANCE: intl.get(`${PREFIX}.model.assetSet.assetImportance`).d('资产重要性'),
    ATTRIBUTE_SET: intl.get(`${PREFIX}.model.assetSet.attributeSet`).d('属性组'),
    FA_CATEGORY: intl.get(`${PREFIX}.model.assetSet.faCategory`).d('固定资产类别'),
    STRATEGY_SCENE_FLAG: intl
      .get(`${PREFIX}.model.assetSet.strategySceneFlag`)
      .d('启用维保策略场景'),
    DESCRIPTION: intl.get(`${PREFIX}.model.assetSet.description`).d('描述'),
    VISUAL_LABEL_RULE: intl.get(`${PREFIX}.model.assetSet.visualLabelRule`).d('资产标签/铭牌规则'),
    CODE_RULE_FLAG: intl.get(`${PREFIX}.model.assetSet.codeRuleFlag`).d('资产编码规则'),
    CODE_RULE: intl.get(`${PREFIX}.model.assetSet.codeRule`).d('编码规则'),
    MAINTAIN_FLAG: intl.get(`${PREFIX}.model.assetSet.maintainFlag`).d('是否可维修'),
    RELATION_IOT_FLAG: intl.get(`${PREFIX}.model.assetSet.relationIotFlag`).d('关联IOT仪表'),
    CONFIG: intl.get(`${PREFIX}.model.assetSet.config`).d('云平台'),
    CONFIG_NAME: intl.get(`${PREFIX}.model.assetSet.configName`).d('云账户'),
    ATTRIBUTE_SET_NAME: intl.get(`${PREFIX}.model.assetSet.attributeSetName`).d('属性组名称'),
    ATTRIBUTE_SET_TYPE: intl.get(`${PREFIX}.model.assetSet.attributeSetType`).d('属性组类型'),
    ATTRIBUTE_SET_CODE: intl.get(`${PREFIX}.model.assetSet.attributeSetCode`).d('属性组代码'),
    ASSET_DESC: intl.get(`${PREFIX}.model.assetSet.assetDesc`).d('资产全称'),
    VISUAL_LABEL: intl.get(`${PREFIX}.model.assetSet.visualLabel`).d('资产标签/铭牌'),
    ASSET_STATUS: intl.get(`${PREFIX}.model.assetSet.assetStatus`).d('资产状态'),
    ASSET_LOCATION: intl.get(`${PREFIX}.model.assetSet.assetLocation`).d('资产位置'),
    OWNING_ORG: intl.get(`${PREFIX}.model.assetSet.owningOrg`).d('所属组织'),
    USING_ORG: intl.get(`${PREFIX}.model.assetSet.usingOrg`).d('使用组织'),
    USER_PERSON: intl.get(`${PREFIX}.model.assetSet.userPerson`).d('使用人'),
    ASSET_STATUS_NAME: intl.get(`${PREFIX}.model.assetSet.assetStatusName`).d('资产状态'),
    ASSET_STATUS_CODE: intl.get(`${PREFIX}.model.assetSet.assetStatusCode`).d('代码'),
    ASSET_STATUS_TYPE: intl.get(`${PREFIX}.model.assetSet.statusType`).d('状态类型'),
    METER_FLAG: intl.get(`${PREFIX}.model.assetSet.meterFlag`).d('启用停机管理'),
    FIELD_NAME: intl.get(`${PREFIX}.model.assetSet.fieldName`).d('字段名称'),
    FIELD_CODE: intl.get(`${PREFIX}.model.assetSet.fieldCode`).d('编码'),
    FIELD_TYPE: intl.get(`${PREFIX}.model.assetSet.fieldType`).d('字段类型'),
    REQUIRED: intl.get(`${PREFIX}.model.assetSet.required`).d('必输'),
    DEFAULT_VALUE: intl.get(`${PREFIX}.model.assetSet.defaultValue`).d('默认值'),
    WIDTH: intl.get(`${PREFIX}.model.assetSet.width`).d('宽度'),
    ATTR_FIELD_NAME: intl.get(`${PREFIX}.model.assetSet.attrFieldName`).d('表字段'),
    REGULAR_CHECK_FLAG: intl.get(`${PREFIX}.model.assetSet.regularCheckFlag`).d('是否定检'),

    ASSET_CLASS_LEVEL: intl.get(`${PREFIX}.model.assetSet.assetClassLevel`).d('资产分类层级'),
    ASSET_CLASS_CODE: intl.get(`${PREFIX}.model.assetSet.assetClassCode`).d('资产分类编码'),
    ASSET_CLASS_NAME: intl.get(`${PREFIX}.model.assetSet.assetClassName`).d('资产分类名称'),
    PARENT_ASSET_CLASS: intl.get(`${PREFIX}.model.assetSet.parentAssetClass`).d('上级资产分类'),
    ASSET_CLASS_RANK: intl.get(`${PREFIX}.model.assetSet.classLevelName`).d('资产分类层级'),
    CONTROL_RULE: intl.get(`${PREFIX}.view.panel.controlRule`).d('控制规则'),
    ASSET_CLASS_LIST_PLACEHOLDER: intl
      .get(`${PREFIX}.view.message.assetClassListPlaceholder`)
      .d('资产分类名称、编码'),
    CONFIRM_DELETE: intl.get(`${PREFIX}.view.message.confirmDelete`).d('确认删除?'),

    // 提示信息
    CONFIRM_CLEAR_REG_INFO: intl
      .get(`${PREFIX}.view.message.confirmClearRegInfo`)
      .d('资产分类对应资产的定检信息将隐藏，请确认是否关闭“是否定检”按钮？'),
    STANDARD_ASSET_CODE_RULE: intl
      .get(`${PREFIX}.model.assetSet.standardAssetCodeRule`)
      .d('标准资产编码规则'),
  };
  return LANGS[key];
};

export default getLang;
