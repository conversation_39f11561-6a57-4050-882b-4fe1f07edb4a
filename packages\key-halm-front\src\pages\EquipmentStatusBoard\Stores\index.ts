import intl from 'utils/intl';
import { FieldType,FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.workshop.emergencyTask';

const topFilterFormDS =() => ({
  autoCreate: true,
  fields: [
    {
      name: 'serviceAreaLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLov`).d('服务区域'),
      ignore: FieldIgnore.always,
      lovCode: 'AORI.MAINT_SITE_NO_ACL',
      textField: 'maintSiteDescription',
      valueField: 'maintSiteId',
    },
    {
      name: 'maintSiteId',
      bind: 'serviceAreaLov.maintSiteId',
    },
  ],
});

export { topFilterFormDS };
