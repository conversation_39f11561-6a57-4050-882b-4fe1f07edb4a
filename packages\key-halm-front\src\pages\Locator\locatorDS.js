/**
 * @since 2020-06-30
 * <AUTHOR>
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { HALM_MMT } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const tenantId = getCurrentOrganizationId();
const promptCode = 'ammt.locators.model.locators';

/**
 * 货位行信息表格
 */
function tableDS() {
  return {
    name: 'locator',
    autoQuery: true,
    queryFields: [
      {
        name: 'locatorNum',
        type: 'string',
        label: intl.get(`${promptCode}.locatorNum`).d('货位编码'),
      },
      {
        name: 'locatorName',
        type: 'string',
        label: intl.get(`${promptCode}.locatorName`).d('货位名称'),
      },
      {
        name: 'maintSiteFieldLov',
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        lovPara: {
          enabledFlag: 1,
        },
        label: intl.get(`${promptCode}.maintSiteId`).d('服务区域'),
        ignore: 'always',
      },
      {
        name: 'maintSiteId',
        type: 'number',
        bind: 'maintSiteFieldLov.maintSiteId',
      },
      {
        name: 'maintSiteName',
        type: 'string',
        bind: 'maintSiteFieldLov.maintSiteName',
        label: intl.get(`${promptCode}.maintSiteId`).d('服务区域'),
      },
      {
        name: 'storeroomFieldLov',
        type: 'object',
        lovCode: 'AMMT.STOREROOM',
        label: intl.get(`${promptCode}.storeroomId`).d('库房'),
        lovPara: {
          enabledFlag: 1,
          sharedFlag: 0,
        },
        ignore: 'always',
      },
      {
        name: 'storeroomId',
        type: 'number',
        bind: 'storeroomFieldLov.storeroomId',
      },
      {
        name: 'storeroomName',
        type: 'string',
        bind: 'storeroomFieldLov.storeroomName',
        label: intl.get(`${promptCode}.storeroomId`).d('库房'),
      },
      {
        name: 'enabledFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        label: intl.get(`${promptCode}.enabledFlag`).d('是否启用'),
      },
    ],
    fields: [
      {
        name: 'locatorNum',
        type: 'string',
        label: intl.get(`${promptCode}.locatorNum`).d('货位编码'),
        required: true,
      },
      {
        name: 'locatorName',
        type: 'intl',
        label: intl.get(`${promptCode}.locatorName`).d('货位名称'),
        required: true,
        dynamicProps: {
          disabled: ({ record }) => record.get('employeeId'),
        },
        maxLength: 40,
      },
      {
        name: 'maintSiteFieldLov',
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        lovPara: {
          enabledFlag: 1,
        },
        label: intl.get(`${promptCode}.maintSiteId`).d('服务区域'),
        required: true,
        // ignore: 'always',
      },
      {
        name: 'maintSiteId',
        type: 'number',
        bind: 'maintSiteFieldLov.maintSiteId',
      },
      {
        name: 'maintSiteName',
        type: 'string',
        bind: 'maintSiteFieldLov.maintSiteName',
        label: intl.get(`${promptCode}.maintSiteId`).d('服务区域'),
      },

      {
        name: 'storeroomFieldLov',
        type: 'object',
        lovCode: 'AMMT.STOREROOM',
        cascadeMap: {
          maintSiteId: 'maintSiteId',
        },
        lovPara: {
          locatorFlag: 1,
          moveFlag: 0,
          enabledFlag: 1,
          sharedFlag: 0,
        },
        label: intl.get(`${promptCode}.storeroomId`).d('库房'),
        required: true,
        // ignore: 'never',
      },
      {
        name: 'storeroomId',
        type: 'number',
        bind: 'storeroomFieldLov.storeroomId',
      },
      {
        name: 'storeroomName',
        type: 'string',
        bind: 'storeroomFieldLov.storeroomName',
        label: intl.get(`${promptCode}.storeroomId`).d('库房'),
      },
      {
        name: 'employeeId',
        type: 'number',
      },
      {
        name: 'employeeName',
        type: 'string',
        label: intl.get(`${promptCode}.employeeId`).d('工作中心员工'),
      },
      {
        name: 'enabledFlag',
        type: 'boolean',
        label: intl.get(`${promptCode}.enabledFlag`).d('是否启用'),
        labelWidth: 100,
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
        dynamicProps: {
          disabled: ({ record }) => record.get('employeeId'),
        },
      },
      {
        name: 'description',
        type: 'string',
        label: intl.get(`${promptCode}.description`).d('描述'),
        maxLength: 240,
      },
    ],
    events: {
      update: ({ name, value, record }) => {
        if (name === 'workcenterPeopleFieldLov') {
          record.set('contactName', value.contactName);
        }
      },
    },
    transport: {
      read: config => {
        const { params } = config;
        let { data } = config;
        data = {
          ...data,
          tenantId,
        };
        const url = `${HALM_MMT}/v1/${tenantId}/locator`;
        return {
          data,
          params: {
            ...params,
            sharedFlag: 0,
          },
          url,
          method: 'GET',
        };
      },
      submit: ({ data, params }) => {
        // eslint-disable-next-line no-param-reassign
        data[0]._status = data[0]._status === 'create' ? null : data[0]._status;
        // eslint-disable-next-line no-param-reassign
        data[0].tenantId = tenantId;
        return {
          url: `${HALM_MMT}/v1/${tenantId}/locator/batch-save`,
          data,
          params,
          method: 'POST',
        };
      },
      destroy: ({ data, params }) => {
        return {
          url: `${HALM_MMT}/v1/${tenantId}/locator/batch-delete`,
          data,
          params,
          method: 'DELETE',
        };
      },
    },
  };
}

export { tableDS };
