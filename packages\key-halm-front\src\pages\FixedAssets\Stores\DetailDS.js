/**
 * 固定资产详情
 * @since：2021/3/24
 * @author：jxy <<EMAIL>>
 * @copyright Copyright (c) 2020,Hand
 */

import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { isUndefined } from 'lodash';

import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();

const apiPrefix = `${HALM_ATN}/v1`;

// 固定资产详情基本信息DS
function baseDetailDS() {
  return {
    autoCreate: true,
    primaryKey: 'fixedAssetId',
    fields: [
      {
        label: getLang('FA_CATEGORY_NAME'),
        name: 'faCategoryLov',
        type: 'object',
        lovCode: 'AFAM.FIXED_ASSET_CATEGORY',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
        required: true,
      },
      {
        name: 'faCategoryName',
        type: 'string',
        label: getLang('FA_CATEGORY_NAME'),
        bind: 'faCategoryLov.categoryName',
        maxLength: 40,
      },
      {
        name: 'faCategoryId',
        type: 'string',
        bind: 'faCategoryLov.faCategoryId',
      },
      {
        name: 'fixedAssetName',
        type: 'intl',
        label: getLang('FIXED_ASSET_NAME'),
        required: true,
        maxLength: 60,
      },
      {
        name: 'financialNum',
        type: 'string',
        label: getLang('FINANCIAL_NUM'),
        required: true,
        maxLength: 40,
      },
      {
        name: 'accountBookNameLov',
        type: 'object',
        label: getLang('ACCOUNT_BOOK_NAME'),
        lovCode: 'AFAM.ACCOUNT_BOOK',
        lovPara: { organizationId },
        ignore: 'always',
        required: true,
      },
      {
        name: 'accountBookName',
        type: 'string',
        label: getLang('ACCOUNT_BOOK_NAME'),
        bind: 'accountBookNameLov.accountBookName',
      },
      {
        name: 'accountBookId',
        type: 'string',
        bind: 'accountBookNameLov.accountBookId',
      },
      {
        name: 'faOrgName',
        type: 'string',
        label: getLang('FA_ORG_NAME'),
      },
      {
        name: 'costCenterLov',
        type: 'object',
        label: getLang('COST_CENTER'),
        lovCode: 'AATN.COST_CENTER',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
      },
      {
        name: 'costCenterCodeName',
        type: 'string',
        label: getLang('COST_CENTER'),
        bind: 'costCenterLov.name',
      },
      {
        name: 'costCenterCodeId',
        type: 'string',
        bind: 'costCenterLov.codeId',
      },
      {
        name: 'assetLov',
        type: 'object',
        label: getLang('ASSET'),
        lovCode: 'AAFM.ASSET',
        lovPara: { organizationId },
        ignore: 'always',
        textField: 'assetDesc',
      },
      {
        name: 'assetDesc',
        type: 'string',
        label: getLang('ASSET'),
        bind: 'assetLov.assetDesc',
      },
      {
        name: 'assetId',
        type: 'string',
        bind: 'assetLov.assetId',
      },
      {
        name: 'assetNum',
        type: 'string',
        label: getLang('ASSET_NUM'),
        bind: 'assetLov.assetNum',
        disabled: true,
      },
      {
        name: 'description',
        type: 'intl',
        label: getLang('DESCRIPTION'),
        maxLength: 240,
      },
      {
        name: 'solidStateCode',
        type: 'string',
        lookupCode: 'AFAM.FA_STATUS',
        label: getLang('SOLID_STATE_CODE'),
        disabled: true,
        defaultValue: 'AWAIT_DRAFT',
      },
      {
        name: 'transferDate',
        type: 'date',
        label: getLang('TRANSFER_DATE'),
      },
      {
        name: 'transferEmpLov',
        type: 'object',
        label: getLang('TRANSFER_EMPLOYEE'),
        lovCode: 'HPFM.EMPLOYEE',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
        textField: 'name',
      },
      {
        name: 'transferEmployeeName',
        type: 'string',
        label: getLang('TRANSFER_EMPLOYEE'),
        bind: 'transferEmpLov.name',
      },
      {
        name: 'transferEmployeeId',
        type: 'string',
        bind: 'transferEmpLov.employeeId',
      },
      {
        name: 'accountingEmpLov',
        type: 'object',
        label: getLang('ACCOUNTING_EMPLOYEE'),
        lovCode: 'HPFM.EMPLOYEE',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
        textField: 'name',
      },
      {
        name: 'accountingeEmployeeName',
        type: 'string',
        label: getLang('ACCOUNTING_EMPLOYEE'),
        bind: 'accountingEmpLov.name',
      },
      {
        name: 'accountingeEmployeeId',
        type: 'string',
        bind: 'accountingEmpLov.employeeId',
      },
      {
        name: 'postingAccountType',
        type: 'string',
        label: getLang('POSTING_ACC_TYPE'),
      },
      {
        name: 'accountEntry',
        type: 'string',
        label: getLang('ACCOUNT_ENTRY'),
      },
      {
        name: 'accountingVoucherNumber',
        type: 'string',
        label: getLang('ACCOUNTING_NUM'),
      },
      {
        name: 'accountEntryDate',
        type: 'date',
        label: getLang('ACCOUNT_ENTRY_DATE'),
      },
      {
        name: 'conversionRemarks',
        type: 'string',
        label: getLang('REMARK'),
        maxLength: 240,
      },
      {
        name: 'projectLov',
        type: 'object',
        label: getLang('PROJECT'),
        lovCode: 'AMDM.UN_EXTERNAL_ORG',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
      },
      {
        name: 'projectName',
        type: 'string',
        label: getLang('PROJECT'),
        bind: 'projectLov.projectName',
      },
      {
        name: 'projectId',
        type: 'string',
        bind: 'projectLov.projectId',
      },
      {
        name: 'contractLov',
        type: 'object',
        label: getLang('CONTRACT'),
        lovCode: 'AMDM.UN_EXTERNAL_ORG',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
      },
      {
        name: 'contractName',
        type: 'string',
        label: getLang('CONTRACT'),
        bind: 'contractLov.contractName',
      },
      {
        name: 'contractId',
        type: 'string',
        bind: 'contractLov.contractId',
      },
      {
        name: 'acceptSheetLov',
        type: 'object',
        label: getLang('ACCEPT_SHEET'),
        lovCode: 'AMDM.UN_EXTERNAL_ORG',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
      },
      {
        name: 'acceptanceSheetName',
        type: 'string',
        label: getLang('ACCEPT_SHEET'),
        bind: 'acceptSheetLov.acceptanceSheetName',
      },
      {
        name: 'acceptanceSheetId',
        type: 'string',
        bind: 'acceptSheetLov.acceptanceSheetId',
      },
      {
        name: 'acceptOrgLov',
        type: 'object',
        label: getLang('ACCEPT_ORG'),
        lovCode: 'AMDM.UN_EXTERNAL_ORG',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
      },
      {
        name: 'acceptanceOrgName',
        type: 'string',
        label: getLang('ACCEPT_ORG'),
        bind: 'acceptOrgLov.acceptanceOrgName',
      },
      {
        name: 'acceptanceOrgId',
        type: 'string',
        bind: 'acceptOrgLov.acceptanceOrgId',
      },
      {
        name: 'acceptEmpLov',
        type: 'object',
        label: getLang('ACCEPT_EMPLOYEE'),
        lovCode: 'HPFM.EMPLOYEE',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
        textField: 'name',
      },
      {
        name: 'acceptanceEmployeeName',
        type: 'string',
        label: getLang('ACCEPT_EMPLOYEE'),
        bind: 'acceptEmpLov.name',
      },
      {
        name: 'acceptanceEmployeeId',
        type: 'string',
        bind: 'acceptEmpLov.employeeId',
      },
      {
        name: 'faDepreRuleLov',
        type: 'object',
        label: getLang('FA_DEPRE_RULE'),
        lovCode: 'AATN.DEPRECIATION_RULE',
        lovPara: { tenantId: organizationId },
        required: true,
        ignore: 'always',
      },
      {
        name: 'faDeprnRuleName',
        type: 'string',
        label: getLang('FA_DEPRE_RULE'),
        bind: 'faDepreRuleLov.ruleName',
      },
      {
        name: 'faDeprnRuleId',
        type: 'string',
        bind: 'faDepreRuleLov.faDeprnRuleId',
      },
      {
        name: 'depreciationTypeCode',
        type: 'string',
        label: getLang('DEPRE_TYPE'),
        lookupCode: 'AATN.DEPRECIATION_TYPE',
      },
      {
        name: 'depreciationMouth',
        type: 'string',
        label: getLang('DEPRE_MONTH'),
      },
      {
        name: 'residualValueRate',
        type: 'string',
        label: getLang('DEPRE_RATE'),
      },
      {
        name: 'orgValDepreciationCode',
        type: 'string',
        label: getLang('ORG_DEPRE_CODE'),
        lookupCode: 'AATN.ORIGINAL_VAL_DEPRECIATION_RULE',
      },
      {
        name: 'depreciationStartDate',
        type: 'date',
        label: getLang('DEPRE_START_DATE'),
      },
      {
        name: 'currencyCode',
        type: 'string',
        label: getLang('CURRENCY'),
        lookupCode: 'CSYS.CURRENCY',
        defaultValue: 'CNY',
        required: true,
      },
      {
        name: 'initialOriginalValue',
        type: 'number',
        label: getLang('INIT_VALUE'),
        defaultValue: 0.0,
        step: 0.01,
      },
      {
        name: 'currentOriginalValue',
        type: 'number',
        label: getLang('CURR_VALUE'),
        defaultValue: 0,
        disabled: true,
      },
      {
        name: 'originalMonthValue',
        type: 'number',
        label: getLang('ORI_MONTH_VALUE'),
        defaultValue: 0,
        disabled: true,
      },
      {
        name: 'accumulatedDepreciation',
        type: 'number',
        label: getLang('ACCOUNT_DEPRECIATION'),
        step: 0.01,
        defaultValue: 0.0,
        dynamicProps: {
          disabled: ({ record }) => {
            const statusFlag = !isUndefined(record.get('solidStateCode'))
              ? record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED'
              : record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED';
            return !statusFlag;
          },
        },
      },
      {
        name: 'ytdDepreciation',
        type: 'number',
        label: getLang('YTD_DEPRECIATION'),
        step: 0.01,
        dynamicProps: {
          disabled: ({ record }) => {
            const statusFlag = !isUndefined(record.get('solidStateCode'))
              ? record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED'
              : record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED';
            return !statusFlag;
          },
        },
      },
      {
        name: 'netValue',
        type: 'number',
        label: getLang('NET_VALUE'),
        step: 0.01,
        dynamicProps: {
          disabled: ({ record }) => {
            const statusFlag = !isUndefined(record.get('solidStateCode'))
              ? record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED'
              : record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED';
            return !statusFlag;
          },
        },
      },
      {
        name: 'residualValue',
        type: 'number',
        label: getLang('RESIDUAL_VALUE'),
        step: 0.01,
        dynamicProps: {
          disabled: ({ record }) => {
            const statusFlag = !isUndefined(record.get('solidStateCode'))
              ? record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED'
              : record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED';
            return !statusFlag;
          },
        },
      },
      {
        name: 'deprnAccountLastMonth',
        type: 'number',
        label: getLang('DEPRE_LAST_MONTH'),
        step: 0.01,
        defaultValue: 0,
        dynamicProps: {
          disabled: ({ record }) => {
            const statusFlag = !isUndefined(record.get('solidStateCode'))
              ? record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED'
              : record.get('solidStateCode') !== 'AWAIT_DRAFT' &&
                record.get('solidStateCode') !== 'REJECTED';
            return !statusFlag;
          },
        },
      },
    ],
    transport: {
      read: ({ data, params, dataSet }) => {
        const url = `${apiPrefix}/${organizationId}/fixed-assets/${data.fixedAssetId}`;
        return {
          url,
          data,
          dataSet,
          params,
          method: 'GET',
        };
      },
      submit: ({ data }) => {
        return {
          url: `${apiPrefix}/${organizationId}/fixed-assets`,
          method: 'POST',
          data: {
            ...data[0],
            tenantId: organizationId,
          },
        };
      },
    },
  };
}

// 固定资产类别
function faCategoryDS() {
  return {
    transport: {
      read: ({ data }) => {
        const url = `${apiPrefix}/${organizationId}/fa-category/lov/list`;
        return {
          url,
          data,
          method: 'GET',
        };
      },
    },
  };
}

export { baseDetailDS, faCategoryDS };
