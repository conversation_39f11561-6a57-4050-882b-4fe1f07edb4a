/**
 * Bom Tab
 * @date: 2020-09-30
 * @author: DCY <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component } from 'react';
import intl from 'utils/intl';
import { Bind } from 'lodash-decorators';
import { Row, Col, Tree, Icon, Divider, Spin } from 'choerodon-ui';
import { DataSet } from 'choerodon-ui/pro';
import EmptyState from 'alm/components/EmptyState';
import { assetSetDS, assetDS, bomDS, itemDS } from '../../Stores/BomTabDS';
import './index.less';
import Asset from './Asset';
import AssetsSet from './AssetsSet';
import Bom from './Bom';
import Item from './Item';

const prefix = 'aatn.equipmentAsset.model.equipmentAsset';
const { TreeNode } = Tree;

/**
 * 对象property属性定义方法
 * @function defineProperty
 * @param {!object} obj - 目标对象
 * @param {!string} property - 对象属性名称
 * @param {any} value - 属性值
 * @returns
 */
function defineProperty(obj, property, value) {
  Object.defineProperty(obj, property, {
    value,
    writable: true,
    enumerable: false,
    configurable: true,
  });
}
class BomTab extends Component {
  constructor(props) {
    super(props);
    this.state = {
      nodeType: '', // 选中项的节点类型
      selectItemId: '', // 选中项的id
      selectedKeys: [], // tree选中的key
    };
    this.assetSetDS = new DataSet(assetSetDS());
    this.assetDS = new DataSet(assetDS());
    this.bomDS = new DataSet(bomDS());
    this.itemDS = new DataSet(itemDS());
  }

  componentDidMount() {
    this.handleSearch();
  }

  componentDidUpdate(preProps) {
    if (preProps.nodeId !== this.props.nodeId) {
      this.handleSearch();
      // 详情跳转详情重置BOM页签状态
      if (preProps.nodeId) {
        this.hadleResetState();
      }
    }
  }

  @Bind()
  hadleResetState() {
    this.setState({
      nodeType: '',
      selectItemId: '',
      selectedKeys: [],
    });
  }

  /**
   * 获取bom树形数据
   */
  @Bind()
  handleSearch() {
    const { nodeId, nodeType, dispatch, tenantId } = this.props;
    dispatch({
      type: 'equipmentAsset/getNodeChild',
      payload: {
        nodeId,
        nodeType,
        // viewType,
        tenantId,
      },
    });
  }

  @Bind()
  onExpand(expandedKeys) {
    const { dispatch } = this.props;
    dispatch({
      type: 'equipmentAsset/updateState',
      payload: {
        loadedKeys: expandedKeys,
        expandedKeys,
      },
    });
  }

  getIconName(nodeType) {
    let iconName = '';
    switch (nodeType) {
      case 'asset':
        iconName = 'description';
        break;
      case 'assetSet':
        iconName = 'view_quilt';
        break;
      case 'bom':
        iconName = 'project_filled';
        break;
      case 'item':
        iconName = 'project';
        break;
      case 'bom_line_asset_set':
        iconName = 'view_quilt';
        break;
      case 'bom_line_item':
        iconName = 'project';
        break;
      default:
        break;
    }
    return iconName;
  }

  /**
   * 异步加载节点的子数据
   * @param {*} treeNode 节点
   */
  @Bind()
  onLoadData(treeNode) {
    const { treeData, dispatch, tenantId } = this.props;
    return new Promise(resolve => {
      if (treeNode.props.children) {
        resolve();
        return;
      }
      dispatch({
        type: 'equipmentAsset/getNodeChild',
        payload: {
          tenantId,
          rootAssetId: treeNode.props.rootAssetId,
          nodeId: treeNode.props.nodeId,
          nodeType: treeNode.props.dataRef.nodeType,
          viewType: treeNode.props.dataRef.nodeType,
        },
      }).then(res => {
        if (res) {
          defineProperty(
            treeNode.props.dataRef,
            'children',
            res.map(item => ({
              title: item.nodeId,
              key: `${item.nodeName}`,
              isLeaf: true,
              ...item,
            }))
          );
          dispatch({
            type: 'equipmentAsset/updateState',
            payload: {
              treeData: [...treeData],
            },
          });
        }
        resolve();
      });
    });
  }

  @Bind()
  renderTreeNodes(data) {
    return data.map(item => {
      if (item.children) {
        return (
          <TreeNode
            key={item.key}
            title={item.nodeName}
            dataRef={item}
            icon={<Icon type={this.getIconName(item.nodeType)} />}
          >
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          {...item}
          key={item.key}
          title={item.nodeName}
          dataRef={item}
          icon={<Icon type={this.getIconName(item.nodeType)} />}
        />
      );
    });
  }

  // 点击树节点触发
  @Bind()
  async onSelect(selectedKeys, info) {
    const { dispatch } = this.props;
    // eslint-disable-next-line prefer-destructuring
    const nodeId = info.node.props.dataRef.nodeId;
    // eslint-disable-next-line prefer-destructuring
    const nodeType = info.node.props.dataRef.nodeType;
    let props = {};
    switch (nodeType) {
      case 'assetSet':
        this.assetSetDS.setQueryParameter('nodeId', nodeId);
        await this.assetSetDS.query();
        props = { assetSetDS: this.assetSetDS, dispatch };
        break;
      case 'asset':
        this.assetDS.setQueryParameter('nodeId', nodeId);
        await this.assetDS.query();
        props = { assetDS: this.assetDS, dispatch };
        break;
      case 'bom':
        this.bomDS.setQueryParameter('nodeId', nodeId);
        await this.bomDS.query();
        props = { bomDS: this.bomDS, dispatch };
        break;
      case 'item':
        this.itemDS.setQueryParameter('nodeId', nodeId);
        await this.itemDS.query();
        props = { itemDS: this.itemDS, dispatch };
        break;
      case 'bom_line_asset_set':
        this.assetSetDS.setQueryParameter('nodeId', nodeId);
        await this.assetSetDS.query();
        props = { assetSetDS: this.assetSetDS, dispatch };
        break;
      case 'bom_line_item':
        this.itemDS.setQueryParameter('nodeId', nodeId);
        await this.itemDS.query();
        props = { itemDS: this.itemDS, dispatch };
        break;
      default:
        break;
    }
    this.setState({
      selectItemId: nodeId,
      selectedKeys,
      nodeType,
      detailProps: props,
    });
  }

  @Bind()
  changeDetailComponent() {
    const { nodeType, detailProps } = this.state;
    let temp = '';
    switch (nodeType) {
      case 'asset':
        temp = <Asset {...detailProps} />;
        break;
      case 'assetSet':
        temp = <AssetsSet {...detailProps} />;
        break;
      case 'bom':
        temp = <Bom {...detailProps} />;
        break;
      case 'item':
        temp = <Item {...detailProps} />;
        break;
      case 'bom_line_asset_set':
        temp = <AssetsSet {...detailProps} />;
        break;
      case 'bom_line_item':
        temp = <Item {...detailProps} />;
        break;
      default:
        break;
    }
    return temp;
  }

  @Bind()
  async handleSelectItem(id) {
    if (id !== this.state.selectItemId) {
      const { dispatch } = this.props;
      this.assetDS.setQueryParameter('nodeId', id);
      await this.assetDS.query();
      const detailProps = { assetDS: this.assetDS, dispatch };
      this.setState({
        selectItemId: id,
        nodeType: 'asset',
        detailProps,
        selectedKeys: [],
      });
    }
  }

  render() {
    const { treeData, loadedKeys, expandedKeys, headerData, loading } = this.props;
    const { selectItemId, selectedKeys } = this.state;
    return (
      <React.Fragment>
        <Spin spinning={loading}>
          {!!headerData.parentAssetId || treeData.length > 0 ? (
            <Row gutter={16} className="malfunction-tab">
              <Col span={7}>
                {!!headerData.parentAssetId && (
                  <React.Fragment>
                    <h6>{intl.get(`${prefix}.parentAssetId`).d('父资产')}</h6>
                    <div
                      className={[
                        'aseet-set-item',
                        headerData.parentAssetId === selectItemId ? 'aseet-set-item-active' : '',
                      ].join(' ')}
                      onClick={() => this.handleSelectItem(headerData.parentAssetId)}
                    >
                      <Icon type="view_quilt" />
                      <div>{headerData.parentAssetName}</div>
                    </div>
                    <Divider />
                  </React.Fragment>
                )}
                {treeData.length > 0 && (
                  <React.Fragment>
                    <h6>{intl.get(`${prefix}.bom`).d('BOM')}</h6>
                    <Tree
                      showIcon
                      loadData={this.onLoadData}
                      onExpand={this.onExpand}
                      onSelect={this.onSelect}
                      loadedKeys={loadedKeys}
                      expandedKeys={expandedKeys}
                      selectedKeys={selectedKeys}
                    >
                      {this.renderTreeNodes(treeData)}
                    </Tree>
                  </React.Fragment>
                )}
              </Col>
              <Col span={17}>{treeData.length > 0 && this.changeDetailComponent()}</Col>
            </Row>
          ) : (
            <div className="tab-no-data">
              {/* {intl.get(`hzero.c7nUI.Table.emptyText`).d('暂无数据')} */}
              <EmptyState />
            </div>
          )}
        </Spin>
      </React.Fragment>
    );
  }
}
export default BomTab;
