/**
 * 资产信息
 * @date: 2021-09-14
 * @author: changyu.duan <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React, { FC, useEffect, useState, useMemo } from 'react';
import { Button } from 'choerodon-ui/pro';
import { Spin } from 'choerodon-ui';
import { getResponse } from 'utils/utils';
import { History } from 'history';
import { isEmpty } from 'lodash';
import Zmage from 'react-zmage';
import { yesOrNoRender } from 'utils/renderer';
import { searchEquipmentAssetDetail } from 'alm/services/guideWorkbenchService';
import { getCurrentEmployee } from 'alm/services/api';
import { useGetAssetImg } from 'alm/hooks/asset';
import { useGetFilesByModuleList } from 'alm/hooks';
import getLang from '../Langs';
import HeadSummary, { Props as HeadSummaryProps } from '../components/HeadSummary';
import Panel from '../components/Panel';
import CardWrapper from '../components/CardWrapper';
import FieldItem from '../components/FieldItem';
import FileViewItem from '../components/FileViewItem';
import Styles from './index.module.less';
import MeterPoint from '../../EquipmentAsset/Detail/MeterPointTab';
// import PIC_NO from '../../../assets/picNo.svg';
import PIC_NO from '../../../assets/no-pic.jpg';

export interface Props {
  id: number;
  tenantId: number;
  history: History;
}
const Index: FC<Props> = props => {
  const { id, tenantId, history } = props;
  const [detail, setDetail] = useState({} as any);
  const [meterPointTabProps, setMeterPointTabProps] = useState({} as any);

  const [loading, setLoading] = useState(false);

  const [fileUploadAttribute, setFileUploadAttribute] = useState({} as any);

  const [currentEmp, setCurrentEmp] = useState({} as any);

  // 根据资产图片UUID获取资产图片
  const assetImg = useGetAssetImg(detail.uniquePictureUuid);

  // 查询附件的参数
  const fileUploadLogList = useMemo(() => {
    // id存在时才查询
    if (!detail.assetId) {
      return null;
    }
    // 查询附件信息
    let moduleList: any[] = [
      {
        moduleIdList: [detail.assetId],
        moduleName: 'aafm-equipment-asset',
      },
    ];
    if (detail && detail.assetSetId) {
      // 获取属性行(资产-->资产分类-->属性组-->属性行)
      const file = [
        {
          moduleName: 'aafm-asset-set',
          moduleIdList: [detail.assetSetId],
        },
      ];
      moduleList = moduleList.concat(file);
    }
    return moduleList;
  }, [detail]);

  // 查询已经上传的数据
  const fileList = useGetFilesByModuleList(fileUploadLogList);

  useEffect(() => {
    getCurrentEmployee({ tenantId }).then(res => {
      setCurrentEmp(res);
    });
  }, []);

  // 详情跳转
  const handleGoDetail = () => {
    history.push(`/aafm/equipment-asset/detail/${id}`);
  };

  const headSummaryProps: HeadSummaryProps = {
    name: detail.assetDesc,
    code: detail.assetNum,
    // 资产状态
    tagList:
      detail?.assetStatusList?.map(item => ({ ...item, tagName: item.assetStatusName })) || [],
  };

  // 获取资产详情
  useEffect(() => {
    setLoading(true);
    searchEquipmentAssetDetail({ tenantId, assetInfoId: id, attrFieldControlFlag: 1 }).then(res => {
      if (res && res.failed) {
        getResponse(res);
        setLoading(false);
      } else {
        setDetail(res);
        setLoading(false);

        // 附件管理
        const tmpFileUploadAttribute = id
          ? {
              title: [
                {
                  name: getLang('SOURCE_TYPE'),
                  code: 'sourceType',
                  width: 150,
                  index: 1, // 列插入的位置
                },
                {
                  name: getLang('SOURCE_NUMBER'),
                  code: 'sourceNumber',
                  width: 150,
                  index: 2, // 列插入的位置
                  isUrl: true,
                },
              ],
              data: [
                {
                  name: getLang('SOURCE_TYPE'),
                  code: 'sourceType', // code必须与title中code保持一致
                  value: getLang('EQUIP_ASSOCIATED_ACCESSORIES'),
                },
                {
                  name: getLang('SOURCE_NUMBER'),
                  code: 'sourceNumber',
                  value: res.assetNum,
                  pathname: `/aafm/equipment-asset/detail/${id}`,
                },
              ],
            }
          : {};

        setFileUploadAttribute(tmpFileUploadAttribute);

        // 查询仪表点
        setMeterPointTabProps({
          tenantId,
          assetId: id,
          onMetersDetail: meterId => {
            history.push(`/amtr/meters/detail/${meterId}`);
          },
        });
      }
    });
  }, [id, tenantId]);

  const fieldValueRender = (field: any) => {
    if (
      ['LOV', 'SELECT', 'EMPLOYEE', 'ORGANIZATION', 'PARTNER', 'PLATFORM_ORG'].includes(
        field.typeCode
      )
    ) {
      return field.valueMeaning;
    } else if (['MULTI_SELECT', 'MULTI_LOV'].includes(field.typeCode)) {
      return field.valueMeaning && JSON.parse(field.valueMeaning).join();
    } else if (field.typeCode === 'SWITCH') {
      return field.value && field.value !== '0' ? getLang('YES') : getLang('NO');
    } else {
      return field.value;
    }
  };

  const assetImgStyles = {
    flex: 0,
  };

  const attachmentStyles =
    fileList.length > 0 ? { flex: '0 0 1.61rem', height: '1.61rem' } : { flex: 0 };

  const additionalFieldsStyles = detail?.attrField?.length
    ? { flex: '0 0 1.36rem', height: '1.36rem' }
    : { flex: 0 };

  return (
    <Spin spinning={loading}>
      <div className={Styles['gw-asset-detail']}>
        <HeadSummary {...headSummaryProps}>
          <Button icon="link2" onClick={handleGoDetail}>
            {getLang('ASSET_DETAIL')}
          </Button>
        </HeadSummary>
        <Panel title={getLang('BASIC_INFO')}>
          <div className="wrapper">
            <div className="left">
              {/* 资产图片 */}
              <CardWrapper title={getLang('ASSET_IMG')} style={assetImgStyles}>
                <div className={Styles['asset-img']}>
                  {assetImg ? <Zmage src={assetImg} alt="" /> : <img src={PIC_NO} alt="" />}
                </div>
              </CardWrapper>
              {/* 质保信息 */}
              <CardWrapper title={getLang('WARRANTY_INFORMATION')}>
                <FieldItem
                  label={getLang('WARRANTY_EXPIRE_DATE')}
                  value={detail.warrantyExpireDate}
                />
                <FieldItem
                  label={getLang('WARRANTY_START_DATE')}
                  value={detail.warrantyStartDate}
                />
                <FieldItem label={getLang('WARRANTY_DESC')} value={detail.warrantyNotes} />
              </CardWrapper>
            </div>
            <div className="middle">
              <CardWrapper title={getLang('DETAILS')}>
                <FieldItem label={getLang('ASSET_NAME')} value={detail.assetDesc} />
                <FieldItem label={getLang('ASSETCLASS')} value={detail.assetSetName} />
                <FieldItem label={getLang('ASSET_LOCATION')} value={detail.assetLocationName} />
                <FieldItem label={getLang('MODEL')} value={detail.model} />
                <FieldItem label={getLang('SERIAL_NUM')} value={detail.serialNum} />
                <FieldItem label={getLang('TRACKINGNUM')} value={detail.trackingNum} />
                <FieldItem label={getLang('BRAND')} value={detail.brand} />
                <FieldItem label={getLang('FIXED_ASSET_NUM')} value={detail.financialNum} />
                <FieldItem
                  label={getLang('MAINTAIN_FLAG')}
                  value={yesOrNoRender(detail.maintainFlag)}
                />
                <FieldItem label={getLang('COST_CENTER')} value={detail.costCenterMeaning} />
                <FieldItem label={getLang('INTERNAL_ORDER')} value={detail.internalOrderMeaning} />
                <FieldItem label={getLang('WBS_ELEMENT')} value={detail.wbsElementMeaning} />

                <FieldItem label={getLang('DESCRIPTION')} value={detail.description} />
              </CardWrapper>
            </div>
            <div className="middle">
              <CardWrapper title={getLang('TRACK_AND_MANAGE')}>
                <FieldItem
                  label={getLang('ASSET_IMPORTANCE')}
                  value={detail.assetImportanceMeaning}
                />
                <FieldItem label={getLang('PARENT_ASSET')} value={detail.parentAssetName} />
                <FieldItem label={getLang('OWNING_ORG')} value={detail.owningOrgName} />
                <FieldItem label={getLang('USING_ORG')} value={detail.usingOrgName} />
                <FieldItem label={getLang('OWNING_PERSON')} value={detail.owningPersonName} />
                <FieldItem label={getLang('ASSET_USER_PERSON')} value={detail.userPersonName} />
              </CardWrapper>
              <CardWrapper title={getLang('ATTACHMENT')} style={attachmentStyles}>
                {!!fileList.length && (
                  <div className={Styles['file-content']}>
                    {fileList.map(item => {
                      const { uploadLogId, fileName, fileUrl } = item as any;
                      return (
                        <FileViewItem
                          label={fileName}
                          value={fileUrl}
                          key={uploadLogId}
                          record={item}
                          currentEmployee={currentEmp}
                          attribute={fileUploadAttribute}
                        />
                      );
                    })}
                  </div>
                )}
              </CardWrapper>
            </div>
            <div className="right">
              <CardWrapper title={getLang('SOURCE_AND_DATE')}>
                <FieldItem
                  label={getLang('ASSET_SOURCE_TYPE')}
                  value={detail.assetSourceTypeMeaning}
                />
                <FieldItem
                  label={getLang('AOS_RECEIVING_REPORT')}
                  value={detail.sourceAcceptanceName}
                />
                <FieldItem label={getLang('SOURCE_CONTRACT')} value={detail.sourceContractName} />
                <FieldItem label={getLang('SOURCE_PROJECT')} value={detail.sourceProjectName} />
                <FieldItem label={getLang('SUPPLIER_ORG')} value={detail.supplierOrgMeaning} />
                <FieldItem label={getLang('MANUFACTURER_NAME')} value={detail.manufacturerName} />
                <FieldItem
                  label={getLang('ASSET_SOURCE_DETAIL')}
                  value={detail.assetSourceDetail}
                />
                <FieldItem label={getLang('RECEIVED_DATE')} value={detail.receivedDate} />
                <FieldItem label={getLang('ORIGINAL_COST')} value={detail.originalCost} />
                <FieldItem label={getLang('CURRENCY_CODE')} value={detail.currencyCode} />
                <FieldItem label={getLang('START_DATE')} value={detail.startDate} />
              </CardWrapper>

              <CardWrapper title={getLang('ADDITIONAL_FIELDS')} style={additionalFieldsStyles}>
                {!!detail.attrField && (
                  <div className={Styles['attach-field-content']}>
                    {detail.attrField.map(item => {
                      const { fieldName } = item as any;
                      return <FieldItem label={fieldName} value={fieldValueRender(item)} />;
                    })}
                  </div>
                )}
              </CardWrapper>
            </div>
          </div>
        </Panel>
        <Panel title={getLang('METER_POINT')} style={{ padding: '0.08rem' }}>
          {!isEmpty(meterPointTabProps) && <MeterPoint {...meterPointTabProps} />}
        </Panel>
      </div>
    </Spin>
  );
};
export default Index;
