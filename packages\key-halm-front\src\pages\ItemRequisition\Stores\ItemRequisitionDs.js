/**
 * @since 2020-07-31
 * <AUTHOR>
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { HALM_MMT } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { dateCalc, checkWhetherTheQuantityIsGreaterThan0 } from 'alm/utils/utils';
import { queryValueStatusFlag } from 'alm/services/materialsService';

const organizationId = getCurrentOrganizationId();

const commonModelPrompt = 'ammt.itemRequisition.model.itemRequisition';

const woopStatusList = [
  'APPROVED',
  'WSCH',
  'WRD',
  'WPCOND',
  'INPRG',
  'COMPLETED',
  'PRECLOSED',
  'WPREV',
  'REWORK',
  'RETURNED',
];

// 查询列表数据
const queryListUrl = `${HALM_MMT}/v1/${organizationId}/item-requisition/list`;
// 查询明细表单数据
const queryFormUrl = `${HALM_MMT}/v1/${organizationId}/item-requisition/detail`;
// 查询明细行数据
const queryTableUrl = `${HALM_MMT}/v1/${organizationId}/item-requisition/line/list`;

function tableDs() {
  return {
    autoQuery: true,
    primaryKey: 'itemReqId',
    queryFields: [
      {
        name: 'itemReqNum',
        label: intl.get(`${commonModelPrompt}.itemReqNum`).d('物料申请单号'),
        type: 'string',
      },
      {
        name: 'maintSiteId',
        label: intl.get(`${commonModelPrompt}.maintSiteId`).d('服务区域'),
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        lovPara: {
          tenantId: organizationId,
        },
      },
      {
        name: 'transTypeId',
        label: intl.get(`${commonModelPrompt}.transTypeId`).d('申请类型'),
        type: 'object',
        lovCode: 'AMMT.TRANSTYPES',
        lovPara: {
          tenantId: organizationId,
        },
      },
      {
        name: 'reqStatus',
        label: intl.get(`${commonModelPrompt}.reqStatus`).d('状态'),
        type: 'string',
        lookupCode: 'AMMT.REQ_STATUS',
      },
      {
        name: 'woNums',
        label: intl.get(`${commonModelPrompt}.woNums`).d('工单'),
        type: 'object',
        lovCode: 'AMTC.WO',
        lovPara: {
          tenantId: organizationId,
          warehouseWorkbenchFlag: 1,
        },
      },
      {
        name: 'itemId',
        label: intl.get(`${commonModelPrompt}.itemId`).d('物料'),
        type: 'object',
        lovCode: 'AMMT.INVENTORY_ITEMS',
        lovPara: {
          tenantId: organizationId,
        },
      },
      {
        name: 'applyTimeFrom',
        label: intl.get(`${commonModelPrompt}.applyTimeFrom`).d('申请时间从'),
        type: 'date',
        dynamicProps: {
          max: ({ record }) => {
            const max = record.get('applyTimeTo');
            if (max) {
              return dateCalc(max, 'desc', 1);
            }
          },
        },
      },
      {
        name: 'applyTimeTo',
        label: intl.get(`${commonModelPrompt}.applyTimeTo`).d('申请时间至'),
        type: 'date',
        dynamicProps: {
          min: ({ record }) => {
            const min = record.get('applyTimeFrom');
            if (min) {
              return dateCalc(min, 'asc', 1);
            }
          },
        },
        processValue: value => {
          if (value && value.format) {
            return `${value.format('YYYY-MM-DD')} 23:59:59`;
          } else {
            return value;
          }
        },
      },
    ],
    fields: [
      {
        name: 'icon',
        label: intl.get(`${commonModelPrompt}.icon`).d('图标'),
        type: 'string',
      },
      {
        name: 'itemReqNum',
        label: intl.get(`${commonModelPrompt}.itemReqNum`).d('物料申请单号'),
        type: 'string',
      },
      {
        name: 'transTypeName',
        label: intl.get(`${commonModelPrompt}.transTypeId`).d('申请类型'),
        type: 'string',
      },
      {
        name: 'reqStatusMeaning',
        label: intl.get(`${commonModelPrompt}.reqStatus`).d('状态'),
        type: 'string',
      },
      {
        name: 'maintSiteName',
        label: intl.get(`${commonModelPrompt}.maintSiteId`).d('服务区域'),
        type: 'string',
      },
      {
        name: 'applyTime',
        label: intl.get(`${commonModelPrompt}.applyTime`).d('申请时间'),
        type: 'dateTime',
      },
      {
        name: 'requesterName',
        label: intl.get(`${commonModelPrompt}.requesterName`).d('申请人'),
        type: 'string',
      },
      {
        name: 'woNums',
        label: intl.get(`${commonModelPrompt}.woNums`).d('单据编号'),
        type: 'string',
      },
      {
        name: 'woNames',
        label: intl.get(`${commonModelPrompt}.woNames`).d('单据概述'),
        type: 'string',
      },
      {
        name: 'itemReqNum',
        label: intl.get(`${commonModelPrompt}.itemReqNum`).d('物料申请单号'),
        type: 'string',
      },
      {
        name: 'description',
        label: intl.get(`${commonModelPrompt}.description`).d('描述'),
        type: 'string',
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: queryListUrl,
          method: 'GET',
          params: {
            ...params,
            tenantId: organizationId,
          },
        };
      },
    },
    events: {
      query: ({ params }) => {
        const { itemCategoryId, maintSiteId, woNums, transTypeId, itemId } = params;
        if (itemCategoryId) {
          // eslint-disable-next-line no-param-reassign
          params.itemCategoryId = itemCategoryId.itemCategoryId;
        }
        if (maintSiteId) {
          // eslint-disable-next-line no-param-reassign
          params.maintSiteId = maintSiteId.maintSiteId;
        }
        if (woNums) {
          // eslint-disable-next-line no-param-reassign
          params.woNums = woNums.woNum;
        }
        if (transTypeId) {
          // eslint-disable-next-line no-param-reassign
          params.transTypeId = transTypeId.transTypeId;
        }
        if (itemId) {
          // eslint-disable-next-line no-param-reassign
          params.itemId = itemId.itemId;
        }
      },
      select: ({ dataSet }) => {
        dataSet.modifyBtnsClickStatus();
      },
      unSelect: ({ dataSet }) => {
        dataSet.modifyBtnsClickStatus();
      },
      selectAll: ({ dataSet }) => {
        dataSet.modifyBtnsClickStatus();
      },
      unSelectAll: ({ dataSet }) => {
        dataSet.modifyBtnsClickStatus();
      },
      load: ({ dataSet }) => {
        dataSet.modifyBtnsClickStatus();
      },
    },
  };
}

// 明细页
function basicInfoFormDS(that) {
  return {
    autoCreate: true,
    fields: [
      {
        name: 'itemReqNum',
        label: intl.get(`${commonModelPrompt}.itemReqNum`).d('物料申请单号'),
        type: 'string',
      },
      {
        name: 'maintSiteName',
        label: intl.get(`${commonModelPrompt}.maintSiteId`).d('服务区域'),
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        lovPara: {
          tenantId: organizationId,
        },
        required: true,
      },
      {
        name: 'transTypeName',
        label: intl.get(`${commonModelPrompt}.transTypeId`).d('申请类型'),
        type: 'object',
        lovCode: 'AMMT.TRANSTYPES',
        lovPara: {
          tenantId: organizationId,
          requestFlag: 1,
          enabledFlag: 1,
        },
        required: true,
      },
      {
        name: 'basicTypeCode',
        type: 'string',
        bind: 'transTypeName.basicTypeCode',
      },
      {
        name: 'reqStatus',
        label: intl.get(`${commonModelPrompt}.reqStatus`).d('状态'),
        type: 'string',
        lookupCode: 'AMMT.REQ_STATUS',
      },
      {
        name: 'applyTime',
        label: intl.get(`${commonModelPrompt}.applyTime`).d('申请时间'),
        type: 'dateTime',
        defaultValue: new Date(),
        required: true,
      },
      {
        name: 'requesterName',
        label: intl.get(`${commonModelPrompt}.requesterName`).d('申请人'),
        type: 'object',
        lovCode: 'HALM.EMPLOYEE',
        lovPara: {
          tenantId: organizationId,
        },
        required: true,
      },
      {
        name: 'costCenterName',
        label: intl.get(`${commonModelPrompt}.costCenter`).d('成本中心'),
        type: 'object',
        lovCode: 'AATN.COST_CENTER',
        lovPara: {
          tenantId: organizationId,
        },
      },
      {
        name: 'description',
        label: intl.get(`${commonModelPrompt}.description`).d('描述'),
        type: 'string',
        maxLength: 240,
      },
    ],
    transport: {
      read: ({ dataSet }) => {
        return {
          url: `${queryFormUrl}/${dataSet.itemReqId}`,
          method: 'GET',
        };
      },
    },
    events: {
      update: ({ name, value }) => {
        if (name === 'transTypeName') {
          that.setState({
            basicTypeCode: value && value.basicTypeCode,
          });
        }
      },
    },
  };
}

// 明细页
function basicInfoTableDS(that) {
  return {
    selection: false,
    primaryKey: 'reqLineId',
    dataToJSON: 'all',
    fields: [
      {
        name: 'lineNum',
        label: intl.get(`${commonModelPrompt}.lineNum`).d('行号'),
        type: 'string',
      },
      {
        name: 'itemLov',
        label: intl.get(`${commonModelPrompt}.itemName`).d('物料名称'),
        type: 'object',
        lovCode: 'AMMT.INVENTORY_ITEMS',
        dynamicProps: {
          lovPara: () => {
            return {
              maintSiteId: that.maintSiteId,
              enabledFlag: 1,
            };
          },
        },
        required: true,
        ignore: 'always',
      },
      {
        name: 'itemId',
        type: 'number',
        bind: 'itemLov.itemId',
      },
      {
        name: 'itemName',
        type: 'string',
        bind: 'itemLov.itemName',
      },
      {
        name: 'itemNum',
        type: 'string',
        bind: 'itemLov.itemNum',
      },
      {
        name: 'brand',
        label: intl.get(`${commonModelPrompt}.brand`).d('品牌'),
        type: 'string',
      },
      {
        name: 'model',
        label: intl.get(`${commonModelPrompt}.model`).d('规格型号'),
        type: 'string',
      },
      {
        name: 'uomName',
        label: intl.get(`${commonModelPrompt}.uomName`).d('单位'),
        type: 'string',
      },
      {
        name: 'demandQuantity',
        label: intl.get(`${commonModelPrompt}.demandQuantity`).d('申请数量'),
        type: 'number',
        required: true,
        min: 0,
        validator: value => checkWhetherTheQuantityIsGreaterThan0(value),
      },
      {
        name: 'processedQuantity',
        label: intl.get(`${commonModelPrompt}.processedQuantity`).d('处理数量'),
        type: 'number',
      },
      {
        name: 'reqLineStatus',
        label: intl.get(`${commonModelPrompt}.reqLineStatus`).d('申请状态'),
        type: 'string',
        lookupCode: 'AMMT.REQ_STATUS',
        defaultValue: 'DRAFT',
      },
      {
        name: 'fromMaintSiteLov',
        label: intl.get(`${commonModelPrompt}.fromMaintSiteName`).d('来源服务区域'),
        type: 'object',
        lovCode: 'AMDM.MAINT_SITE_FOR_ITEM_REQ',
        dynamicProps: {
          lovPara: ({ record }) => {
            const itemId = record.get('itemId');
            return {
              itemId,
              enabledFlag: 1,
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'fromMaintSiteId',
        type: 'number',
        bind: 'fromMaintSiteLov.maintSiteId',
      },
      {
        name: 'fromMaintSiteName',
        type: 'string',
        bind: 'fromMaintSiteLov.maintSiteName',
      },
      {
        name: 'fromStoreroomId',
        type: 'number',
      },
      {
        name: 'fromStoreroomName',
        type: 'string',
        label: intl.get(`${commonModelPrompt}.fromStoreroomName`).d('来源库房'),
      },
      {
        name: 'fromLocatorId',
        type: 'number',
      },
      {
        name: 'fromLocatorName',
        type: 'string',
        label: intl.get(`${commonModelPrompt}.fromLocatorName`).d('来源货位'),
      },
      {
        name: 'fromValueStatusMeaning',
        label: intl.get(`${commonModelPrompt}.fromValueStatus`).d('来源新旧件'),
        type: 'string',
        lookupCode: 'AMMT.VALUE_STATUS',
      },
      {
        name: 'toMaintSiteLov',
        label: intl.get(`${commonModelPrompt}.toMaintSiteName`).d('目标服务区域'),
        type: 'object',
        lovCode: 'AMDM.MAINT_SITE_FOR_ITEM_REQ',
        dynamicProps: {
          lovPara: ({ record }) => {
            const itemId = record.get('itemId');
            return {
              itemId,
              enabledFlag: 1,
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'toMaintSiteId',
        type: 'number',
        bind: 'toMaintSiteLov.maintSiteId',
      },
      {
        name: 'toMaintSiteName',
        type: 'string',
        bind: 'toMaintSiteLov.maintSiteName',
      },
      {
        name: 'toStoreroomLov',
        label: intl.get(`${commonModelPrompt}.toStoreroomName`).d('目标库房'),
        type: 'object',
        lovCode: 'AMMT.STOREROOM',
        dynamicProps: {
          lovPara: ({ record }) => {
            const toMaintSiteId = record.get('toMaintSiteId');
            return {
              enabledFlag: 1,
              maintSiteId: toMaintSiteId,
              tenantId: organizationId,
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'toStoreroomId',
        type: 'number',
        bind: 'toStoreroomLov.storeroomId',
      },
      {
        name: 'toStoreroomName',
        type: 'string',
        bind: 'toStoreroomLov.storeroomName',
      },
      {
        name: 'toLocatorLov',
        label: intl.get(`${commonModelPrompt}.toLocatorName`).d('目标货位'),
        type: 'object',
        lovCode: 'AMMT.LOCATOR',
        valueField: 'locatorId',
        textField: 'locatorName',
        dynamicProps: {
          lovPara: ({ record }) => {
            const toStoreroomId = record.get('toStoreroomId');
            return {
              enabledFlag: 1,
              storeroomId: toStoreroomId,
              tenantId: organizationId,
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'toLocatorId',
        type: 'number',
        bind: 'toLocatorLov.locatorId',
      },
      {
        name: 'toLocatorName',
        type: 'string',
        bind: 'toLocatorLov.locatorName',
      },
      {
        name: 'lotNumber',
        label: intl.get(`${commonModelPrompt}.lotNumber`).d('批次'),
        type: 'string',
      },
      {
        name: 'toValueStatus',
        label: intl.get(`${commonModelPrompt}.toValueStatus`).d('目标新旧件'),
        type: 'string',
        lookupCode: 'AMMT.VALUE_STATUS',
      },
      {
        name: 'woopLov',
        label: intl.get(`${commonModelPrompt}.woopName`).d('工作单任务'),
        type: 'object',
        lovCode: 'AMTC.WOOP',
        valueField: 'woopId',
        textField: 'woopName',
        dynamicProps: {
          required: () => {
            const { basicTypeCode } = that.state;
            return ['INV_WO', 'WO_RETURNED_INV_IN'].includes(basicTypeCode);
          },
          lovPara: () => {
            return {
              maintSiteId: that.maintSiteId,
              woopStatusList: woopStatusList.join(','),
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'woopId',
        type: 'number',
        bind: 'woopLov.woopId',
      },
      {
        name: 'woopName',
        type: 'string',
        bind: 'woopLov.woopName',
      },
      {
        name: 'description',
        label: intl.get(`${commonModelPrompt}.description`).d('描述'),
        type: 'string',
      },
    ],
    transport: {
      read: ({ params, dataSet }) => {
        const { itemReqId } = dataSet;
        return {
          url: queryTableUrl,
          method: 'GET',
          params: {
            ...params,
            itemReqId,
          },
        };
      },
    },
    events: {
      load: ({ dataSet }) => {
        dataSet.forEach(async record => {
          const newItem = record;
          const itemId = newItem.get('itemId');
          const fromMaintSiteId = newItem.get('fromMaintSiteId');
          const toMaintSiteId = newItem.get('toMaintSiteId');

          let isEditFromValueStatusFlag = 0;
          let isEditToValueStatusFlag = 0;
          if (itemId && fromMaintSiteId) {
            isEditFromValueStatusFlag = await queryValueStatusFlag({
              itemId,
              maintSiteId: fromMaintSiteId,
            });
          }
          if (itemId && toMaintSiteId) {
            isEditToValueStatusFlag = await queryValueStatusFlag({
              itemId,
              maintSiteId: toMaintSiteId,
            });
          }
          record.init('isEditFromValueStatusFlag', isEditFromValueStatusFlag);
          record.init('isEditToValueStatusFlag', isEditToValueStatusFlag);
        });
      },
      update: async ({ name, value, record }) => {
        if (name === 'itemLov') {
          record.set('brand', value?.brand);
          record.set('model', value?.model);
          record.set('uomName', value?.uomName);
          record.set('lotControlFlag', value?.lotControlFlag);
          record.set('woopLov', null);

          if (record.get('fromMaintSiteId')) {
            record.set('fromMaintSiteLov', null);
          }
          if (record.get('toMaintSiteId')) {
            record.set('toMaintSiteLov', null);
            record.set('lotNumber', null);
          }
        } else if (name === 'fromMaintSiteLov') {
          // 清空库房、货位、批次和新旧件
          record.set('fromStoreroomId', null);
          record.set('fromStoreroomName', null);
          record.set('fromLocatorId', null);
          record.set('fromLocatorName', null);
          record.set('lotNumber', null);
          record.set('fromValueStatus', null);
          record.set('fromValueStatusMeaning', null);

          const fromMaintSiteId = record.get('fromMaintSiteId');
          const itemId = record.get('itemId');
          if (fromMaintSiteId && itemId) {
            const flag = await queryValueStatusFlag({
              itemId,
              maintSiteId: fromMaintSiteId,
            });
            record.set('isEditFromValueStatusFlag', flag);
          } else {
            record.set('isEditFromValueStatusFlag', 0);
          }
        } else if (name === 'toMaintSiteLov') {
          record.set('toStoreroomLov', null);
          record.set('toValueStatus', null);

          const toMaintSiteId = record.get('toMaintSiteId');
          const itemId = record.get('itemId');
          if (toMaintSiteId && itemId) {
            const flag = await queryValueStatusFlag({
              itemId,
              maintSiteId: toMaintSiteId,
            });
            record.set('isEditToValueStatusFlag', flag);
          } else {
            record.set('isEditToValueStatusFlag', 0);
          }
        } else if (name === 'toStoreroomLov') {
          record.set('locatorFlagTo', value?.locatorFlag);
          record.set('toLocatorLov', null);
        }
      },
    },
  };
}

export { tableDs, basicInfoFormDS, basicInfoTableDS };
