import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import getLangs from '../Langs';

const organizationId = getCurrentOrganizationId();

function tableDS() {
  return {
    autoQuery: true,
    selection: false,
    primaryKey: 'checklistGroupId',
    queryFields: [
      {
        name: 'checklistGroupName',
        type: 'string',
        label: getLangs('GROUP_NAME'),
      },
      {
        name: 'checklistGroupCode',
        type: 'string',
        label: getLangs('GROUP_CODE'),
      },
      {
        name: 'typeCode',
        type: 'string',
        label: getLangs('GROUP_TYPE_CODE'),
        lookupCode: 'AMTC.CHECKLIST_GROUP_TYPE',
      },
      {
        name: 'maintSiteLov',
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        label: getLangs('MAINTSITE'),
        ignore: 'always',
      },
      {
        name: 'maintSiteId',
        type: 'number',
        bind: 'maintSiteLov.maintSiteId',
      },
      {
        name: 'pointName',
        type: 'string',
        label: getLangs('POINT'),
        ignore: 'always',
      },
      {
        name: 'routeLov',
        type: 'object',
        lovCode: 'AMTC.ROUTE',
        label: getLangs('ROUTE'),
        ignore: 'always',
        lovPara: {
          enabledFlag: 1,
        },
      },
      {
        name: 'routeId',
        type: 'number',
        bind: 'routeLov.routeId',
      },
      {
        name: 'enabledFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        label: getLangs('ENABLED_FLAG'),
        defaultValue: 1,
      },
    ],
    fields: [
      {
        name: 'checklistGroupName',
        type: 'string',
        label: getLangs('GROUP_NAME'),
      },
      {
        name: 'checklistGroupCode',
        type: 'string',
        label: getLangs('GROUP_CODE'),
      },
      {
        name: 'typeCode',
        type: 'string',
        label: getLangs('GROUP_TYPE_CODE'),
        lookupCode: 'AMTC.CHECKLIST_GROUP_TYPE',
      },
      {
        name: 'maintSiteNameStr',
        type: 'string',
        label: getLangs('MAINTSITES'),
      },
      {
        name: 'description',
        type: 'string',
        label: getLangs('GROUP_DESC'),
      },
      {
        name: 'enabledFlag',
        type: 'number',
        label: getLangs('ENABLED_FLAG'),
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          params,
          url: `${HALM_MTC}/v1/${organizationId}/checklist-groups`,
          method: 'GET',
        };
      },
    },
  };
}

export { tableDS };
