// 标准作业任务、维保计划步骤共用

import React, { PureComponent } from 'react';
import { Form, Select, Output, IntlField, NumberField, Lov, Switch } from 'choerodon-ui/pro';
import { Bind } from 'lodash-decorators';
import { observer } from 'mobx-react';
import CommonComponent from 'alm/components/CommonComponent';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { isNumber } from 'lodash';
import { yesOrNoRender } from 'utils/renderer';
import { HALM_ORI } from 'alm/utils/config';

const tenantId = getCurrentOrganizationId();

@observer
export default class WoopModal extends PureComponent {
  componentDidMount() {
    const { isNew, header, woopDs, isFirstWoop, headerTypeCode, record, headerId } = this.props;
    if (isNew) {
      // 如果不是第一条数据，工作职责“默认首道工序”
      // 如果是第一条数据，工作职责“默认为工单负责人”
      const data = isFirstWoop
        ? {
            defaultJobCode: 'WO_OWNER',
          }
        : {
            defaultJobCode: 'DEFAULT_FIRST_WOOP',
          };
      woopDs.create({
        ...data,
        durationUomCode: headerTypeCode === 'ACT' ? header.durationUomCode : header.durationUom,
        durationUomCodeMeaning:
          headerTypeCode === 'ACT' ? header.durationUomCodeMeaning : header.durationUomMeaning,
        standardHour: 0,
        needSignFlag: header?.ifShowSignFlag === 1 ? 1 : 0,
      });
      // 初始化序号
      this.handleOpNumInit();
    } else {
      woopDs.loadData([{ ...record }]);
    }
    woopDs.headerId = headerId;
  }

  handleOpNumInit() {
    // 查询任务最大行号 给序号设置初始值
    const { headerId, headerTypeCode } = this.props;
    request(`${HALM_ORI}/v1/${tenantId}/actOp/max-line`, {
      method: 'GET',
      query: {
        headerId,
        headerTypeCode,
      },
    }).then(res => {
      let initOpNumber = 10;
      if (!res?.failed && isNumber(res)) {
        initOpNumber = Math.ceil((res + 1) / 10) * 10;
      }
      this.props.woopDs.current.set('activityOpNumber', initOpNumber);
    });
  }

  @Bind
  handleAssetChange(record) {
    this.props.woopDs.current.set('assetLocationId', record?.assetLocationId);
    this.props.woopDs.current.set('assetLocationName', record?.assetLocationName);
  }

  @Bind
  handleLocationChange() {
    this.props.woopDs.current.set('assetId', null);
    this.props.woopDs.current.set('descAndLabel', null);
  }

  @Bind
  handleDefaultJobChange() {
    const record = this.props.woopDs.current;
    record.set('ownerGroupId', null);
    record.set('ownerGroupName', null);
    record.set('ownerId', null);
    record.set('ownerName', null);
  }

  render() {
    const { isNew, isEdit, header, isFirstWoop, woopDs } = this.props;
    const { maintSiteId, woBasicType } = header;
    const durationUomCodeMeaning = woopDs.current?.get('durationUomCodeMeaning');

    return (
      <React.Fragment>
        {isNew || isEdit ? (
          <Form dataSet={woopDs} columns={1} labelWidth={120}>
            <IntlField name="actOpName" />
            <NumberField name="activityOpNumber" />
            {woBasicType === 'CHECKING_TYPE' && [
              <Lov name="assetLocationLov" onChange={this.handleLocationChange} />,
              <Lov name="assetLov" onChange={this.handleAssetChange} />,
            ]}
            <NumberField name="standardHour" suffix={durationUomCodeMeaning} />
            <Select
              name="defaultJobCode"
              optionsFilter={record => {
                if (isFirstWoop) {
                  return record.get('value') === 'WO_OWNER' || record.get('value') === 'CUSTOM';
                } else {
                  return true;
                }
              }}
              onChange={this.handleDefaultJobChange}
            />
            <CommonComponent
              name="ownerGroupName"
              disabled={woopDs.current?.get('defaultJobCode') !== 'CUSTOM'}
              isEdit
              dataSet={woopDs}
              queryParams={{
                maintSiteId,
              }}
            />
            <CommonComponent
              name="ownerName"
              disabled={woopDs.current?.get('defaultJobCode') !== 'CUSTOM'}
              isEdit
              dataSet={woopDs}
              queryParams={{
                maintSiteId,
              }}
            />
            {header?.ifShowSignFlag === 1 ? <Switch name="needSignFlag" /> : null}
            <IntlField rows={3} type="multipleLine" name="description" />
            <Switch name="importance" />
          </Form>
        ) : (
          <Form dataSet={woopDs} columns={1} labelWidth={120}>
            <Output name="actOpName" />
            <Output name="activityOpNumber" />
            {woBasicType === 'CHECKING_TYPE' && [
              <Output name="assetLocationLov" />,
              <Output name="assetLov" />,
            ]}
            <Output name="standardHour" suffix={durationUomCodeMeaning} />
            <Output name="defaultJobCode" />
            <Output name="ownerGroupName" />
            <Output name="ownerName" />
            {header?.ifShowSignFlag === 1 ? (
              <Output name="needSignFlag" renderer={({ text }) => yesOrNoRender(Number(text))} />
            ) : null}
            <Output name="description" />
            <Switch name="importance" disabled />
          </Form>
        )}
      </React.Fragment>
    );
  }
}
