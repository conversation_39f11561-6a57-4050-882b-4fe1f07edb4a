/**
 * 固定资产
 * @since：2021/3/9
 * @author：jxy <<EMAIL>>
 * @copyright Copyright (c) 2020,Hand
 */

import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();

// 查询列表数据
const apiPrefix = `${HALM_ATN}/v1`;

function tableDS() {
  return {
    autoQuery: true,
    selection: 'multiple',
    pageSize: 10,
    primaryKey: 'fixedAssetId',
    queryFields: [
      {
        label: getLang('FIXED_ASSET_NAME'),
        name: 'fixedAssetName',
        type: 'string',
      },
      {
        label: getLang('FINANCIAL_NUM'),
        name: 'financialNum',
        type: 'string',
      },
      {
        label: getLang('ACCOUNT_BOOK_NAME'),
        name: 'accountBookLov',
        type: 'object',
        lovCode: 'AFAM.ACCOUNT_BOOK',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'accountBookId',
        type: 'string',
        bind: 'accountBookLov.accountBookId',
      },
      {
        label: getLang('TRANSFER_DATA_FROM'),
        name: 'transferDateFrom',
        type: 'date',
        max: 'transferDateTo',
      },
      {
        label: getLang('TRANSFER_DATA_TO'),
        name: 'transferDateTo',
        type: 'date',
        min: 'transferDateFrom',
      },
      {
        name: 'solidStateCode',
        type: 'string',
        lookupCode: 'AFAM.FA_STATUS',
        label: getLang('SOLID_STATE_CODE'),
      },
      {
        label: getLang('ASSET_NUM'),
        name: 'assetClassLov',
        type: 'object',
        lovCode: 'AAFM.ASSET_NUM',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
      },
      {
        name: 'assetId',
        type: 'string',
        bind: 'assetClassLov.assetId',
      },
    ],
    fields: [
      {
        name: 'fixedAssetName',
        type: 'string',
        label: getLang('FIXED_ASSET_NAME'),
      },
      {
        name: 'financialNum',
        type: 'string',
        label: getLang('FINANCIAL_NUM'),
      },
      {
        name: 'assetNum',
        type: 'string',
        label: getLang('ASSET_NUM'),
      },
      {
        name: 'transferDate',
        type: 'date',
        label: getLang('TRANSFER_DATE'),
      },
      {
        name: 'accountBookName',
        type: 'string',
        label: getLang('ACCOUNT_BOOK_NAME'),
      },
      {
        name: 'faCategoryName',
        type: 'string',
        label: getLang('FA_CATEGORY_NAME'),
      },
      {
        name: 'solidStateCode',
        type: 'string',
        lookupCode: 'AFAM.FA_STATUS',
        label: getLang('SOLID_STATE_CODE'),
      },
      {
        name: 'initialOriginalValue',
        type: 'number',
        label: getLang('INIT_VALUE'),
      },
      {
        name: 'currentOriginalValue',
        type: 'number',
        label: getLang('CURR_VALUE'),
      },
      {
        name: 'accumulatedDepreciation',
        type: 'number',
        label: getLang('ACCOUNT_DEPRECIATION'),
      },
      {
        name: 'ytdDepreciation',
        type: 'number',
        label: getLang('YTD_DEPRECIATION'),
      },
      {
        name: 'netValue',
        type: 'number',
        label: getLang('NET_VALUE'),
      },
      {
        name: 'residualValue',
        type: 'number',
        label: getLang('RESIDUAL_VALUE'),
      },
      {
        name: 'description',
        type: 'string',
        label: getLang('DESCRIPTION'),
      },
    ],
    transport: {
      read: ({ data, params, dataSet }) => {
        const url = `${apiPrefix}/${organizationId}/fixed-assets`;
        return {
          url,
          data,
          dataSet,
          params,
          method: 'GET',
        };
      },
      destroy: ({ data }) => {
        const url = `${apiPrefix}/${organizationId}/fixed-assets`;
        return {
          url,
          method: 'DELETE',
          data,
        };
      },
    },
  };
}

export { tableDS };
