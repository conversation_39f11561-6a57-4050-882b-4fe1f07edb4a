import React from 'react';
import { DataSet, Form, Select, Lov } from 'choerodon-ui/pro';
import { Alert } from 'choerodon-ui';
import { observer } from 'mobx-react';
import EmployeesLov from 'alm/components/EmployeesLov';
import getLang from './Langs';

interface ModalProps {
  dataSet: DataSet;
}
interface WorkCenter {
  workCenterId?: Number;
  workCenterName?: String;
  employeeId?: Number;
  employeeName?: String;
  maintSiteId?: Number;
  maintSiteName?: String;
}
const ModalForm = (props: ModalProps) => {
  const currentRecord = props.dataSet.current;
  const handleWorkCenterChange = (e: WorkCenter, record: DataSet['current']) => {
    if (record) {
      record?.set('workcenterId', e.workCenterId);
      record?.set('workcenterName', e.workCenterName);
      record?.set('staffId', e?.employeeId);
      record?.set('staffName', e?.employeeName);
      if (e) {
        record?.set('maintSiteId', e?.maintSiteId);
        record?.set('maintSiteName', e?.maintSiteName);
      }
    }
  };

  const handleWorkStaffChange = (e: WorkCenter, record: DataSet['current']) => {
    if (record) {
      const positionType = record?.get('positionType');
      if (e && positionType !== 'CHECKER') {
        record?.set('workcenterId', e?.workCenterId);
        record?.set('workcenterName', e?.workCenterName);
        record?.set('maintSiteId', e?.maintSiteId);
        record?.set('maintSiteName', e?.maintSiteName);
      }
      record?.set('staffId', e?.employeeId);
      record?.set('staffName', e?.employeeName);
    }
  };

  const handleMaintSiteChange = (record: DataSet['current']) => {
    const positionType = record?.get('positionType');
    record?.set('workcenterId');
    record?.set('workcenterName');
    if (positionType !== 'CHECKER') {
      record?.set('staffId');
      record?.set('staffName');
    }
  };

  const handlePositionTypeChange = (
    value: String,
    oldValue: String,
    record: DataSet['current']
  ) => {
    record?.set('serviceTypes');
    if (value === 'CHECKER' || !value) {
      record?.set('workcenterId');
      record?.set('workcenterName');
      record?.set('staffId');
      record?.set('staffName');
    }
    if (oldValue === 'CHECKER') {
      record?.set('staffId');
      record?.set('staffName');
    }
  };
  return (
    <>
      <Alert
        closable
        style={{ marginBottom: '16px' }}
        message={getLang('BATCH_EDIT_WARNING')}
        type="warning"
        showIcon
      />
      <Form dataSet={props.dataSet}>
        <Select
          name="positionType"
          onChange={(value, oldValue) => handlePositionTypeChange(value, oldValue, currentRecord)}
        />
        <Select
          name="serviceTypes"
          optionsFilter={i => {
            const positionType = currentRecord?.get('positionType');
            if (positionType === 'PRINCIPAL') {
              return i.get('value') !== 'SUBCONTRACTING' && i.get('value') !== 'SERVICE_REQUEST';
            } else if (positionType === 'CHECKER') {
              // 当单据职务为验收员时，业务单据限制为工作单（保养、技改大修）、工作单（故障维修类）
              return ['FAULT_MAINTAIN_WO', 'FAILURE_WO'].includes(i.get('value'));
            } else {
              return true;
            }
          }}
        />
        <Lov name="maintSiteLov" onChange={() => handleMaintSiteChange(currentRecord)} />
        <EmployeesLov
          name="workcenterName"
          queryParams={{
            maintSiteId: currentRecord?.get('maintSiteId'),
          }}
          record={currentRecord}
          value={currentRecord?.get('workcenterName')}
          onOk={e => handleWorkCenterChange(e, currentRecord)}
        />
        {currentRecord?.get('positionType') === 'CHECKER' ? (
          <Lov name="staffName" onChange={e => handleWorkStaffChange(e, currentRecord)} />
        ) : (
          <EmployeesLov
            name="staffName"
            queryParams={{
              maintSiteId: currentRecord?.get('maintSiteId'),
            }}
            record={currentRecord}
            value={currentRecord?.get('staffName')}
            onOk={e => handleWorkStaffChange(e, currentRecord)}
          />
        )}
      </Form>
    </>
  );
};

export default observer(ModalForm);
