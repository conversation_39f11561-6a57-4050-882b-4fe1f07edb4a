/**
 * 变更记录详情
 * @since：2021/6/25
 * @author：lkj <<EMAIL>>
 * @copyright Copyright (c) 2021,Hand
 */
import React, { Component } from 'react';
import { routerRedux } from 'dva/router';
import { withRouter } from 'react-router';
import { Modal, Button } from 'choerodon-ui/pro';
import { Bind } from 'lodash-decorators';
import intl from 'utils/intl';

import CloseImg from 'alm/assets/img/close.png';
import ChangeLogsDetail from './Detail/index';

import styles from './index.less';

const prompt = 'aatn.equipmentAsset';

@withRouter
class ChangeLogsModal extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  @Bind()
  renderLogModalTitle() {
    return (
      <React.Fragment>
        <div className={styles['change-logs-modal-title']} onClick={this.handleCloseModal}>
          <span className={styles['title-desc']}>
            {intl.get(`${prompt}.button.changeLogs`).d('变更记录')}
          </span>
          <a className={styles['detail-record']} onClick={this.handleDetailLogs}>
            {intl.get(`${prompt}.modal.detailRecord`).d('详细记录')}
          </a>
          <a>
            <img src={CloseImg} alt="" className={styles['title-img']} />
          </a>
        </div>
      </React.Fragment>
    );
  }

  @Bind()
  handleCloseModal() {
    Modal.destroyAll();
  }

  @Bind()
  handleDetailLogs() {
    const { dispatch, moduleNum, moduleName, history } = this.props;
    if (dispatch) {
      dispatch(
        routerRedux.push({
          pathname: `/aafm/change-logs`,
          state: {
            moduleNum,
            moduleName,
          },
        })
      );
    } else if (history) {
      history.push(`/aafm/change-logs`, {
        moduleNum,
        moduleName,
      });
    }
    this.handleCloseModal();
  }

  /**
   * 跳转到指定单据详情页
   */
  @Bind()
  handleLinkToDetail(url) {
    const { dispatch, history } = this.props;
    if (dispatch) {
      dispatch(
        routerRedux.push({
          pathname: url,
        })
      );
    } else if (history) {
      history.push(url);
    }
    this.handleCloseModal();
  }

  // 查看变更记录
  @Bind()
  handleShowChangeLogs() {
    const { moduleNum, moduleName } = this.props;
    const modalProps = { moduleNum, moduleName, onToDetail: this.handleLinkToDetail };
    Modal.open({
      key: 'changeLogs',
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      drawer: true,
      style: {
        width: 560,
      },
      title: this.renderLogModalTitle(),
      footer: null,
      children: <ChangeLogsDetail {...modalProps} />,
    });
  }

  render() {
    return (
      <Button onClick={this.handleShowChangeLogs}>
        {intl.get(`${prompt}.button.changeLogs`).d('变更记录')}
      </Button>
    );
  }
}

export default ChangeLogsModal;
