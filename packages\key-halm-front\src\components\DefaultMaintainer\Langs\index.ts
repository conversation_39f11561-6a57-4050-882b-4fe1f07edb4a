/**
 * 默认维护人员-多语言
 * @date 2021-12-20
 * <AUTHOR> <<EMAIL>>
 * @version 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLang = key => {
  const PREFIX = 'alm.component';
  const LANGS = {
    PREFIX,
    ...getCommonLangs(),

    // tab
    DEFAULT_MAINTAINER: intl.get(`${PREFIX}.tab.defaultMaintainer`).d('默认职务人员'),

    // text
    BATCH_EDIT_WARNING: intl
      .get(`${PREFIX}.message.defaultMaintainer.batchUpdateWarning`)
      .d('所选批量修改对象所处服务区域可能存在差异，批量维护值仅生效于业务单据所在服务区域。'),
    BATCH_EDIT_CONFIRM: intl
      .get(`${PREFIX}.message.defaultMaintainer.batchUpdateConfirm`)
      .d('批量修改操作会替换原默认配置中同职务同服务区域的配置'),

    // button
    BATCH_EDIT: intl.get(`${PREFIX}.button.defaultMaintainer.batchEdit`).d('批量修改默认职务人员'),
    CONTINUE_ADD: intl.get(`${PREFIX}.button.defaultMaintainer.continueAdd`).d('继续添加'),

    // modal title
    BATCH_EDIT_TITLE: intl
      .get(`${PREFIX}.title.defaultMaintainer.batchEditTitle`)
      .d('批量修改默认职务人员'),

    // 字段
    POSITION_TYPE: intl.get(`${PREFIX}.model.defaultMaintainer.positionType`).d('单据职务'),
    SERVICE_TYPE: intl.get(`${PREFIX}.model.defaultMaintainer.serviceTypes`).d('业务单据'),
    MAINT_SITE: intl.get(`${PREFIX}.model.defaultMaintainer.maintSite`).d('服务区域'),
    WORK_CENTER: intl.get(`${PREFIX}.model.defaultMaintainer.workcenter`).d('工作中心'),
    DEFAULT_STAFF: intl.get(`${PREFIX}.model.defaultMaintainer.defaultStaff`).d('人员'),
    UPDATE_DATE: intl.get(`${PREFIX}.model.defaultMaintainer.lastUpdateDate`).d('更新日期'),
  };
  return LANGS[key];
};

export default getLang;
