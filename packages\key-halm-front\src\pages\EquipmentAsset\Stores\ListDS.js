/**
 * 设备资产列表DS
 * @date: 2020-09-30
 * @author: DCY <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { HALM_ATN, HALM_PFM } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const apiPrefix = `${HALM_ATN}/v1`;
const organizationId = getCurrentOrganizationId();
const promptCode = 'aatn.equipmentAsset.model.equipmentAsset';

// 标签模板预览DS
function labelTplDS() {
  return {
    autoQuery: false,
    selection: false,
    primaryKey: 'labelTemplateCode',
    fields: [
      {
        name: 'labelTemplateCode',
        type: 'string',
        label: intl.get(`${promptCode}.labelTemplate`).d('选择标签模板'),
        lookupCode: 'HALM.LABEL_TEMPLATE',
        required: true,
      },
    ],
  };
}

function listDS() {
  return {
    transport: {
      read: ({ params, dataSet, data }) => {
        const url = `${apiPrefix}/${organizationId}/asset-info/list`;
        const preConditionList = dataSet.getState('preConditionList') || [];
        return {
          url,
          data: {
            ...data,
            preConditionList,
          },
          params,
          method: 'POST',
        };
      },
      destroy: ({ data }) => {
        const url = `${apiPrefix}/${organizationId}/asset-info`;
        return {
          url,
          method: 'DELETE',
          data,
        };
      },
    },
    autoQuery: false,
    selection: 'multiple',
    cacheSelection: true,
    pageSize: 10,
    primaryKey: 'assetId',
    fields: [
      {
        label: intl.get(`${promptCode}.icon`).d('图标'),
        name: 'icon',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetDesc`).d('资产全称'),
        name: 'assetDesc',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetName`).d('资产全称'),
        name: 'assetName',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetNum`).d('资产编号'),
        name: 'assetNum',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.visualLabel`).d('资产标签/铭牌'),
        name: 'visualLabel',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetClass`).d('资产分类'),
        name: 'assetSetName',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetSpecialty`).d('资产专业分类'),
        name: 'assetSpecialtyName',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetStatus`).d('资产状态'),
        name: 'statusName',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetLocation`).d('资产位置'),
        name: 'assetLocationName',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.brand`).d('品牌/厂商'),
        name: 'brand',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.model`).d('规格/型号'),
        name: 'model',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.owningOrg`).d('所属组织'),
        name: 'owningOrgName',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.usingOrg`).d('使用组织'),
        name: 'usingOrgName',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.userPerson`).d('使用人'),
        name: 'userPersonName',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetImportance`).d('资产重要性'),
        name: 'assetImportanceMeaning',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.warrantyExpireDate`).d('质保到期日'),
        name: 'warrantyExpireDate',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.expire`).d('是否已经到期'),
        name: 'expire',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.trackingNum`).d('其他跟踪编号'),
        name: 'trackingNum',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.description`).d('描述'),
        name: 'description',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.receivedDate`).d('交付日期'),
        name: 'receivedDate',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.startDate`).d('启用日期'),
        name: 'startDate',
        type: 'string',
      },
    ],
    queryFields: [
      {
        label: intl.get(`${promptCode}.assetDesc`).d('资产全称'),
        name: 'assetDesc',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.visualLabel`).d('资产标签/铭牌'),
        name: 'visualLabel',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetNum`).d('资产编号'),
        name: 'assetNum',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetClass`).d('资产分类'),
        name: 'assetClassLov',
        type: 'object',
        lovCode: 'AAFM.ASSET_SET',
        lovPara: { organizationId, effectiveAssetsClassFlag: 1, enabledFlag: 1 },
        ignore: 'always',
      },
      {
        name: 'assetSetId',
        type: 'string',
        bind: 'assetClassLov.assetSetId',
      },
      {
        label: intl.get(`${promptCode}.brand`).d('品牌/厂商'),
        name: 'brand',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.model`).d('规格/型号'),
        name: 'model',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.assetImportance`).d('资产重要性'),
        name: 'assetImportance',
        type: 'string',
        lookupCode: 'AAFM.ASSET_IMPORTANCE',
      },
      {
        label: intl.get(`${promptCode}.serialNum`).d('序列号'),
        name: 'serialNum',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.trackingNum`).d('其他跟踪编号'),
        name: 'trackingNum',
        type: 'string',
      },
      {
        label: intl.get(`${promptCode}.receivedDateFrom`).d('交付日期从'),
        name: 'receivedDateFrom',
        type: 'date',
        max: 'receivedDateTo',
      },
      {
        label: intl.get(`${promptCode}.receivedDateTo`).d('交付日期至'),
        name: 'receivedDateTo',
        type: 'date',
        min: 'receivedDateFrom',
      },
      {
        label: intl.get(`${promptCode}.startDateFrom`).d('启用日期从'),
        name: 'startDateFrom',
        type: 'date',
        max: 'startDateTo',
      },
      {
        label: intl.get(`${promptCode}.startDateTo`).d('启用日期至'),
        name: 'startDateTo',
        type: 'date',
        min: 'startDateFrom',
      },
      {
        label: intl.get(`${promptCode}.assetLocation`).d('资产位置'),
        name: 'assetLocationLov',
        type: 'object',
        lovCode: 'AMDM.LOCATIONS',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'assetLocationId',
        type: 'string',
        bind: 'assetLocationLov.assetLocationId',
      },
      {
        label: intl.get(`${promptCode}.subLocationFlag`).d('包含子位置'),
        name: 'subLocationFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
      },
      {
        label: intl.get(`${promptCode}.subOrgFlag`).d('包含子组织'),
        name: 'subOrgFlag',
        type: 'number',
        lookupCode: 'HPFM.FLAG',
      },
      {
        label: intl.get(`${promptCode}.assetSpecialty`).d('资产专业分类'),
        name: 'assetSpecialtyLov',
        type: 'object',
        lovCode: 'AAFM.SPECIAL_ASSET_CLASS',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'assetSpecialtyId',
        type: 'string',
        bind: 'assetSpecialtyLov.assetSpecialtyId',
      },
      {
        label: intl.get(`${promptCode}.assetStatus`).d('资产状态'),
        name: 'assetStatusLov',
        type: 'object',
        lovCode: 'AAFM.ASSET_STATUS',
        lovPara: { organizationId },
        ignore: 'always',
      },
      {
        name: 'assetStatusId',
        type: 'string',
        bind: 'assetStatusLov.assetStatusId',
      },
      {
        label: intl.get(`${promptCode}.userPerson`).d('使用人'),
        name: 'userPersonLov',
        type: 'object',
        lovCode: 'HALM.EMPLOYEE',
        lovPara: { tenantId: organizationId },
        ignore: 'always',
      },
      {
        name: 'userPersonId',
        type: 'string',
        bind: 'userPersonLov.employeeId',
      },
      {
        label: intl.get(`${promptCode}.description`).d('描述'),
        name: 'description',
        type: 'string',
      },
    ],
  };
}

// 查询默认规则
function frameworkDs() {
  return {
    // primaryKey: '',
    autoQuery: true,
    selection: false,
    fields: [
      {
        name: 'iotFlag',
        label: intl.get('apfm.frameWork.model.frameWork.iotFlag').d('启用IOT服务'),
        type: 'number',
      },
    ],
    transport: {
      read: config => {
        const { params, data } = config;
        const url = `${HALM_PFM}/v1/${organizationId}/frameworks`;
        const axiosConfig = {
          data,
          params,
          url,
          method: 'GET',
        };
        return axiosConfig;
      },
    },
  };
}

// 新建弹窗
function createModalDS() {
  return {
    autoCreate: true,
    fields: [
      {
        label: intl.get(`${promptCode}.assetClass`).d('资产分类'),
        name: 'assetClassLov',
        type: 'object',
        lovCode: 'AAFM.ASSET_SET',
        lovPara: { organizationId, effectiveAssetsClassFlag: 1 },
        ignore: 'always',
        required: true,
      },
      {
        name: 'assetSetId',
        type: 'string',
        bind: 'assetClassLov.assetSetId',
      },
      {
        label: intl.get(`${promptCode}.assetClass`).d('资产分类'),
        name: 'assetSetName',
        type: 'string',
        bind: 'assetClassLov.assetSetName',
      },
      {
        label: intl.get(`${promptCode}.assetLocation`).d('资产位置'),
        name: 'assetLocationLov',
        type: 'object',
        lovCode: 'AMDM.LOCATIONS',
        lovPara: { organizationId, assetLocationFlag: 1 },
        ignore: 'always',
        required: true,
      },
      {
        name: 'assetLocationId',
        type: 'string',
        bind: 'assetLocationLov.assetLocationId',
      },
      {
        label: intl.get(`${promptCode}.assetLocation`).d('资产位置'),
        name: 'assetLocationName',
        type: 'string',
        bind: 'assetLocationLov.locationName',
      },
    ],
  };
}
export { labelTplDS, listDS, frameworkDs, createModalDS };
