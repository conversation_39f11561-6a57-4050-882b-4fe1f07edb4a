import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, DataToJSON, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { omit } from 'lodash';
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ATN}/v1/${organizationId}`;

const fieldListDS = (): DataSetProps => ({
  autoQuery: false,
  paging: false,
  dataToJSON: DataToJSON.all,
  selection: DataSetSelection.multiple,
  primaryKey: 'confId',
  fields: [
    {
      label: getLang('SEQ_NUM'),
      name: 'seqNum',
      type: FieldType.string,
    },
    {
      label: getLang('FIELD_NAME'),
      name: 'fieldName',
      type: FieldType.string,
    },
    {
      label: getLang('FIELD_CODE'),
      name: 'fieldCode',
      type: FieldType.string,
    },
    {
      label: getLang('FIELD_SOURCE'),
      name: 'sourceCode',
      type: FieldType.string,
      lookupCode: 'AORI.FIELD_SOURCE',
    },
    {
      label: getLang('FIELD_TYPE'),
      name: 'typeCode',
      type: FieldType.string,
      lookupCode: 'AORI.FIELD_TYPE',
    },
    {
      label: getLang('DEFAULT_VALUE'),
      name: 'defaultValue',
      type: FieldType.string,
    },
    {
      label: getLang('WIDTH'),
      name: 'width',
      type: FieldType.string,
      lookupCode: 'AORI.FIELD_WIDTH',
    },
    {
      label: getLang('REQUIRED'),
      name: 'requiredFlag',
      type: FieldType.boolean,
      lookupCode: 'HPFM.FLAG',
      trueValue: 1,
      falseValue: 0,
      defaultValue: 0,
    },
  ],
  transport: {
    read: ({ params, data }) => {
      const url = `${apiPrefix}/field-confs/list`;
      return {
        url,
        data: {
          ...data,
        },
        params,
        method: 'GET',
      };
    },
    destroy: ({ data }) => {
      const url = `${apiPrefix}/field-confs`;
      return {
        url,
        method: 'DELETE',
        data: data[0],
      };
    },
    submit: ({ data }) => {
      return {
        url: `${apiPrefix}/field-confs/sort`,
        data,
        method: 'POST',
      };
    },
  },
});

const areaExchangeDS = (): DataSetProps => ({
  dataToJSON: DataToJSON.all,
  fields: [
    {
      label: getLang('SELECT_AREA'),
      name: 'area',
      type: FieldType.object,
      lovCode: 'AORI.FIEDL_AREA',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          const templateCode = dataSet.getState('templateCode');
          const currentAreaCode = dataSet.getState('currentAreaCode');
          return { tenantId: organizationId, templateCode, currentAreaCode };
        },
      },
    },
  ],
});

const areaConfigDS = (): DataSetProps => ({
  dataToJSON: DataToJSON.all,
  fields: [
    {
      name: 'areaConfName',
      type: FieldType.intl,
      maxLength: 8,
      required: true,
    },
  ],
  transport: {
    submit: ({ data }) => {
      const newData = data.map(line => {
        const newLine = omit(line, ['__id', '_status']);
        newLine.deleteFlag = line._status === 'delete' ? 1 : 0;
        return newLine;
      });
      const url = `${apiPrefix}/area-confs`;
      return {
        url,
        data: newData,
        method: 'POST',
      };
    },
  },
});

export { fieldListDS, areaExchangeDS, areaConfigDS };
