/**
 * 固定资产多语言
 * @since：2021/3/9
 * @author：jxy <<EMAIL>>
 * @copyright Copyright (c) 2020,Hand
 */

import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLang = key => {
  const PREFIX = 'aatn.fixedAssets';
  const LANGS = {
    PREFIX,
    ...getCommonLangs(),
    // title
    HEADER: intl.get(`${PREFIX}.view.message.title`).d('固定资产'),
    ORDER_LIST: intl.get(`${PREFIX}.view.message.orderList`).d('固定资产列表'),
    SEARCH_INFO: intl.get(`${PREFIX}.view.message.searchInfo`).d('搜索固定资产'),
    VALUE_CHANGE_DETAIL: intl.get(`${PREFIX}.view.message.valueChangeDetail`).d('价值变动详情'),
    // panel
    BASIC_INFO: intl.get(`${PREFIX}.view.message.panel.A`).d('基础信息'),
    TRANSFER_INFO: intl.get(`${PREFIX}.view.message.panel.B`).d('转固信息'),
    SOURCE_INFO: intl.get(`${PREFIX}.view.message.panel.C`).d('来源信息'),
    FADEPRN_METHODS: intl.get(`${PREFIX}.view.message.panel.D`).d('折旧方法'),
    FIXED_ASSET_VALUES: intl.get(`${PREFIX}.view.message.panel.E`).d('固定资产价值'),
    // TabPane
    VALUE_TAB: intl.get(`${PREFIX}.view.message.panel.F`).d('价值变动'),
    // button
    TRANSFER: intl.get(`${PREFIX}.button.transfer`).d('转固生效'),
    // model
    FIXED_ASSET_NAME: intl.get(`${PREFIX}.model.fixedAssets.fixedAssetName`).d('名称'),
    FINANCIAL_NUM: intl.get(`${PREFIX}.model.fixedAssets.financialNum`).d('固定资产编号'),
    ACCOUNT_BOOK_NAME: intl.get(`${PREFIX}.model.fixedAssets.accountBookName`).d('资产账簿'),
    TRANSFER_DATA_FROM: intl.get(`${PREFIX}.model.fixedAssets.transferDateFrom`).d('转固日期从'),
    TRANSFER_DATA_TO: intl.get(`${PREFIX}.model.fixedAssets.transferDateTo`).d('转固日期至'),
    SOLID_STATE_CODE: intl.get(`${PREFIX}.model.fixedAssets.solidStateCode`).d('转固状态'),
    ASSET_NUM: intl.get(`${PREFIX}.model.fixedAssets.assetNum`).d('资产编号'),
    TRANSFER_DATE: intl.get(`${PREFIX}.model.fixedAssets.transferDate`).d('转固日期'),
    INIT_VALUE: intl.get(`${PREFIX}.model.fixedAssets.initialOriginalValue`).d('初始原值'),
    FA_CATEGORY_NAME: intl.get(`${PREFIX}.model.fixedAssets.faCategoryName`).d('固定资产类别'),
    CURR_VALUE: intl.get(`${PREFIX}.model.fixedAssets.currentOriginalValue`).d('当前原值'),
    ACCOUNT_DEPRECIATION: intl
      .get(`${PREFIX}.model.fixedAssets.accumulatedDepreciation`)
      .d('累计折旧'),
    YTD_DEPRECIATION: intl.get(`${PREFIX}.model.fixedAssets.ytdDepreciation`).d('YTD折旧'),
    NET_VALUE: intl.get(`${PREFIX}.model.fixedAssets.netValue`).d('净值'),
    RESIDUAL_VALUE: intl.get(`${PREFIX}.model.fixedAssets.residualValue`).d('残值'),
    DESCRIPTION: intl.get(`${PREFIX}.model.fixedAssets.description`).d('描述'),
    //
    MAINTAIN_FLAG: intl.get(`${PREFIX}.model.fixedAssets.maintainFlag`).d('是否可维修'),
    RELATION_IOT_FLAG: intl.get(`${PREFIX}.model.fixedAssets.relationIotFlag`).d('是否关联IOT仪表'),
    CONFIG: intl.get(`${PREFIX}.model.fixedAssets.config`).d('云平台'),
    CONFIG_NAME: intl.get(`${PREFIX}.model.fixedAssets.configName`).d('云账户'),
    ATTRIBUTE_SET_NAME: intl.get(`${PREFIX}.model.fixedAssets.attributeSetName`).d('属性组名称'),
    ATTRIBUTE_SET_TYPE: intl.get(`${PREFIX}.model.fixedAssets.attributeSetType`).d('属性组类型'),
    ATTRIBUTE_SET_CODE: intl.get(`${PREFIX}.model.fixedAssets.attributeSetCode`).d('属性组代码'),
    ASSET_DESC: intl.get(`${PREFIX}.model.fixedAssets.assetDesc`).d('资产全称'),
    VISUAL_LABEL: intl.get(`${PREFIX}.model.fixedAssets.visualLabel`).d('资产标签/铭牌'),
    ASSET_STATUS: intl.get(`${PREFIX}.model.fixedAssets.assetStatus`).d('资产状态'),
    ASSET_LOCATION: intl.get(`${PREFIX}.model.fixedAssets.assetLocation`).d('资产位置'),
    OWNING_ORG: intl.get(`${PREFIX}.model.fixedAssets.owningOrg`).d('所属组织'),
    USING_ORG: intl.get(`${PREFIX}.model.fixedAssets.usingOrg`).d('使用组织'),
    USER_PERSON: intl.get(`${PREFIX}.model.fixedAssets.userPerson`).d('使用人'),
    FA_ORG_NAME: intl.get(`${PREFIX}.model.fixedAssets.faOrgName`).d('资产所属部门'),
    COST_CENTER: intl.get(`${PREFIX}.model.fixedAssets.costCenter`).d('成本中心'),
    ASSET: intl.get(`${PREFIX}.model.fixedAssets.asset`).d('设备/资产'),
    TRANSFER_EMPLOYEE: intl.get(`${PREFIX}.model.fixedAssets.transferEmployee`).d('转固人员'),
    ACCOUNTING_EMPLOYEE: intl.get(`${PREFIX}.model.fixedAssets.accountingeEmployee`).d('入账人员'),
    POSTING_ACC_TYPE: intl.get(`${PREFIX}.model.fixedAssets.postingAccountType`).d('入账科目类型'),
    ACCOUNT_ENTRY: intl.get(`${PREFIX}.model.fixedAssets.accountEntry`).d('入账科目'),
    ACCOUNTING_NUM: intl
      .get(`${PREFIX}.model.fixedAssets.accountingVoucherNumber`)
      .d('会计凭证编号'),
    ACCOUNT_ENTRY_DATE: intl.get(`${PREFIX}.model.fixedAssets.accountEntryDate`).d('入账日期'),
    REMARK: intl.get(`${PREFIX}.model.fixedAssets.conversionRemarks`).d('备注'),
    PROJECT: intl.get(`${PREFIX}.model.fixedAssets.project`).d('项目'),
    CONTRACT: intl.get(`${PREFIX}.model.fixedAssets.contract`).d('合同'),
    ACCEPT_SHEET: intl.get(`${PREFIX}.model.fixedAssets.acceptanceSheet`).d('验收单'),
    ACCEPT_ORG: intl.get(`${PREFIX}.model.fixedAssets.acceptanceOrg`).d('验收部门'),
    ACCEPT_EMPLOYEE: intl.get(`${PREFIX}.model.fixedAssets.acceptanceEmployee`).d('验收人员'),
    FA_DEPRE_RULE: intl.get(`${PREFIX}.model.fixedAssets.faDeprnRule`).d('折旧规则'),
    DEPRE_TYPE: intl.get(`${PREFIX}.model.fixedAssets.depreciationTypeCode`).d('折旧类型'),
    DEPRE_MONTH: intl.get(`${PREFIX}.model.fixedAssets.depreciationMouth`).d('折旧月份'),
    DEPRE_RATE: intl.get(`${PREFIX}.model.fixedAssets.residualValueRate`).d('残值率(%)'),
    ORG_DEPRE_CODE: intl
      .get(`${PREFIX}.model.fixedAssets.orgValDepreciationCode`)
      .d('原值增/减折旧规则'),
    DEPRE_START_DATE: intl
      .get(`${PREFIX}.model.fixedAssets.depreciationStartDate`)
      .d('折旧起始日期'),
    CURRENCY: intl.get(`${PREFIX}.model.fixedAssets.currency`).d('货币'),
    ORI_MONTH_VALUE: intl.get(`${PREFIX}.model.fixedAssets.originalMonthValue`).d('月初原值'),
    DEPRE_LAST_MONTH: intl
      .get(`${PREFIX}.model.fixedAssets.deprnAccountLastMonth`)
      .d('上月折旧金额'),
    PERIOD_NAME: intl.get(`${PREFIX}.model.fixedAssets.periodName`).d('期间'),
    CHANGE_TYPE_CODE: intl.get(`${PREFIX}.model.fixedAssets.changeTypeCode`).d('计算类型'),
    CHANGE_VALUE: intl.get(`${PREFIX}.model.fixedAssets.changeValue`).d('金额'),
    // message
    DELETE_INFO: intl.get(`${PREFIX}.view.message.detailLine.delete`).d('是否删除选中的固定资产？'),
    DATE_NOT_NULL: intl.get(`${PREFIX}.view.message.detail.null`).d('折旧起始日期不能为空！'),
    PRICE_ERROR: intl.get(`${PREFIX}.view.message.priceError`).d('金额不能大于初始原值！'),
  };
  return LANGS[key];
};

export default getLang;
