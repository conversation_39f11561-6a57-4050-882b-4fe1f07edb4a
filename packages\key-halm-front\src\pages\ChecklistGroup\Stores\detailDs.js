import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';
import getLangs from '../Langs';

const organizationId = getCurrentOrganizationId();

// 头明细
function detailDs() {
  return {
    autoQuery: false,
    selection: false,
    autoCreate: true,
    fields: [
      {
        name: 'checklistGroupName',
        type: 'intl',
        label: getLangs('GROUP_NAME'),
        maxLength: 40,
        required: true,
      },
      {
        name: 'checklistGroupCode',
        type: 'string',
        label: getLangs('GROUP_CODE'),
        maxLength: 40,
        required: true,
      },
      {
        name: 'typeCode',
        type: 'string',
        label: getLangs('GROUP_TYPE_CODE'),
        lookupCode: 'AMTC.CHECKLIST_GROUP_TYPE',
        required: true,
      },
      {
        name: 'maintSiteLov',
        label: getLangs('MAINTSITES'),
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        lovPara: {
          organizationId,
        },
        ignore: 'always',
        multiple: true,
      },
      {
        name: 'maintSiteIds',
        type: 'number',
        bind: 'maintSiteLov.maintSiteId',
        multiple: true,
      },
      {
        name: 'maintSiteNames',
        label: getLangs('MAINTSITES'),
        type: 'string',
        bind: 'maintSiteLov.maintSiteName',
      },
      {
        name: 'enabledFlag',
        label: getLangs('ENABLED_FLAG'),
        type: 'boolean',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'description',
        type: 'intl',
        label: getLangs('GROUP_DESC'),
        maxLength: 240,
      },
    ],
    transport: {
      read: ({ params, dataSet }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/checklist-groups/${dataSet.checklistGroupId}`,
          method: 'GET',
          params,
        };
      },
    },
  };
}

// 检查项
function checklistListDs() {
  return {
    selection: false,
    paging: false,
    primaryKey: 'checklistId',
    idField: 'checklistId',
    parentField: 'parentChecklistId',
    expandField: 'expand',
    fields: [
      {
        name: 'expand',
        type: 'boolean',
      },
      {
        name: 'checklistName',
        label: getLangs('CHECKLIST_NAME'),
        type: 'string',
      },
      {
        name: 'checkTimingMeaning',
        label: getLangs('BUSINESS_SCENARIO'),
        type: 'string',
      },
      {
        name: 'methodCode',
        label: getLangs('METHOD_CODE'),
        type: 'string',
      },
      {
        name: 'standardReference',
        label: getLangs('REFERENCE'),
        type: 'string',
      },
      {
        name: 'importance',
        label: getLangs('IMPORTANCE'),
        type: 'boolean',
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'Y',
      },
      {
        name: 'columnTypeMeaning',
        label: getLangs('COLUMN_TYPE'),
        type: 'string',
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          params: { ...params, tenantId: organizationId },
          url: `${HALM_MTC}/v1/${organizationId}/base-checklists`,
          method: 'GET',
        };
      },
    },
  };
}

// 适用对象
function workObjListDs() {
  return {
    autoQuery: false,
    primaryKey: 'id',
    selection: false,
    fields: [
      {
        name: 'moduleTypeMeaning',
        type: 'string',
        label: getLangs('OBJ_TYPE'),
      },
      {
        name: 'objName',
        type: 'string',
        label: getLangs('OBJ_NAME'),
      },
      {
        name: 'pointTypeMeaning',
        type: 'string',
        label: getLangs('POINT_TYPE'),
      },
      {
        name: 'objCode',
        type: 'string',
        label: getLangs('OBJ_CODE'),
      },
    ],
    transport: {
      read: ({ params, dataSet }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/checklist-groups/adapt-range-rel/${dataSet.checklistGroupId}`,
          method: 'GET',
          params,
        };
      },
    },
  };
}

// 适用对象新建
function addWorkObjDs({ maintSiteIds }) {
  const maintSiteIdStr = maintSiteIds.join();
  return {
    autoCreate: true,
    fields: [
      {
        name: 'routeLov',
        type: 'object',
        lovCode: 'AMTC.ROUTE',
        label: getLangs('ROUTE'),
        multiple: true,
        lovPara: {
          maintSiteIds: maintSiteIdStr,
          enabledFlag: 1,
        },
      },
      {
        name: 'route',
        type: 'string',
        bind: 'routeLov.routeId',
      },
      {
        name: 'routeNames',
        type: 'string',
        bind: 'routeLov.routeName',
      },
      {
        name: 'point',
        type: 'string',
        bind: 'pointNames.pointId',
      },
      {
        name: 'pointNames',
        type: 'object',
        label: getLangs('POINT'),
        textField: 'pointName',
        valueField: 'pointId',
      },
    ],
  };
}

export { detailDs, checklistListDs, workObjListDs, addWorkObjDs };
