/**
 * Location - 位置
 * @date: 2020-06-10
 * @author: qzq <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */

import React, { Component } from 'react';
import { DataSet, Table, Button, Spin, Icon } from 'choerodon-ui/pro';
import { Bind } from 'lodash-decorators';
import { isUndefined } from 'lodash';
import classNames from 'classnames';
import { Header, Content } from 'components/Page';
// import { getCurrentOrganizationId } from 'utils/utils';
import { operatorRender, enableRender, yesOrNoRender } from 'utils/renderer';
import intl from 'utils/intl';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MDM } from 'alm/utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { DefaultMaintainerModal } from 'alm/components/DefaultMaintainer';

import { tableDS } from './Stores';
import './index.less';

const organizationId = getCurrentOrganizationId();
const queryUrl = `${HALM_MDM}/v1/${organizationId}/asset-locations`;

@formatterCollections({
  code: ['amdm.location', 'alm.common', 'alm.component'],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDS());
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true }
)
class Location extends Component {
  /**
   * state初始化
   * @param {props} props -参数
   */
  constructor(props) {
    super(props);
    this.state = {};
    // this.props.listDS = new DataSet(tableDS());
  }

  /**
   * 新增位置
   * 跳转到新增明细页
   */
  @Bind()
  handleAddLocation() {
    const { history } = this.props;
    const path = `/amdm/location/create`;
    history.push(path);
  }

  /**
   * 跳转到详情页
   * @param {string} id 位置行id
   * @param {boolean} isCreateFlag 判断是新增下级还是新增顶级
   */
  @Bind()
  handleLinkToDetail(record, flag = 0, editFlag = false) {
    const { history } = this.props;
    const id = record.pristineData.assetLocationId;
    const linkUrl = isUndefined(id)
      ? 'create'
      : flag
      ? `create-sub/${id}`
      : `detail/${id}/${editFlag}`;
    const path = `/amdm/location/${linkUrl}`;
    const state = {
      parentId: record.get('assetLocationId'),
      parentName: record.get('locationName'),
      maintSiteId: record.get('maintSiteId'),
      maintSiteName: record.get('maintSiteName'),
    };
    history.push(path, state);
  }

  // /**
  //  * 启用禁用
  //  * @param {*} ds dataset对象
  //  * @param {*} flag 启用/禁用标识
  //  */
  // @Bind()
  // async handleEnable(ds, flag) {
  //   this.props.listDS.currentObj = ds.record.pristineData;
  //   this.props.listDS.enabledFlag = flag;
  //   ds.record.set('enabledFlag', flag);
  //   const res = await this.props.listDS.submit();
  //   if (res) {
  //     this.props.listDS.query();
  //   }
  // }

  // 点击展开
  handleExpand(expanded, record) {
    // 判断节点是否异步加载子结点
    if (expanded && record.get('childFlag') && !record.children) {
      record.setState('loadding', true);

      // 查询其下全部子数据（不是子孙数据），只受 是否启用 条件限制，不受其他查询条件限制
      const querryParams = this.props.listDS.queryDataSet.toData();
      const { enabledFlag, locationTypeId, maintSiteId, assetLocationFlag, directMaintainFlag } =
        querryParams[0] || {};

      request(queryUrl, {
        method: 'GET',
        query: {
          enabledFlag,
          locationTypeId,
          maintSiteId,
          assetLocationFlag,
          directMaintainFlag,
          parentLocationId: record.data.assetLocationId,
          selectOneLevelFlag: 1,
        },
      }).then(res => {
        const recordsChildren = res.content;
        this.props.listDS.data = [...this.props.listDS.toData(), ...recordsChildren];

        record.setState('loadding', false);
      });
    }
  }

  // icon 渲染问题， 首先判断record的值和自定义状态来判断出叶节点和父节点进行不同的渲染
  expandicon({ prefixCls, expanded, expandable, record, onExpand }) {
    if (!record.get('childFlag')) {
      // 子结点渲染
      return <span style={{ paddingLeft: '0.18rem' }} />;
    }
    if (record.getState('loadding') === true) {
      // 自定义状态渲染
      return <Spin tip="loding" delay={200} size="small" />;
    }
    const iconPrefixCls = `${prefixCls}-expand-icon`;
    const classString = classNames(iconPrefixCls, {
      [`${iconPrefixCls}-expanded`]: expanded,
    });
    return (
      <Icon
        type="baseline-arrow_right"
        className={classString}
        onClick={onExpand}
        tabIndex={expandable ? 0 : -1}
      />
    );
  }

  expandedRowRender = () => {
    return false;
  };

  // 位置 List Table列
  get columns() {
    return [
      {
        name: 'locationName',
        width: 300,
        renderer: ({ value, record }) => {
          return <a onClick={() => this.handleLinkToDetail(record)}>{value}</a>;
        },
      },
      {
        name: 'locationCode',
        width: 150,
      },
      {
        name: 'locationTypeName',
        width: 150,
      },
      {
        name: 'maintSiteName',
        // width: 150,
      },
      {
        name: 'assetLocationFlag',
        width: 120,
        align: 'left',
        renderer: ({ value }) => yesOrNoRender(value),
      },
      {
        name: 'directMaintainFlag',
        width: 120,
        align: 'left',
        renderer: ({ value }) => yesOrNoRender(value),
      },
      // {
      //   name: 'manageOrgName',
      //   width: 150,
      // },
      // {
      //   name: 'stockFlag',
      //   width: 100,
      //   renderer: ({ value }) => enableRender(value),
      // },
      {
        name: 'description',
        // width: 150,
      },
      {
        name: 'enabledFlag',
        width: 100,
        align: 'left',
        renderer: ({ value }) => enableRender(value),
      },
      {
        header: intl.get('hzero.common.table.column.option').d('操作'),
        width: 150,
        lock: 'right',
        // align: 'center',
        renderer: rowDS => {
          const { record } = rowDS;
          // const {
          //   data: { enabledFlag },
          // } = record || {};
          const children = [
            {
              key: 'edit',
              ele: (
                <a onClick={() => this.handleLinkToDetail(record, 0, true)}>
                  {intl.get('hzero.common.button.edit').d('编辑')}
                </a>
              ),
              len: 4,
              title: intl.get('hzero.common.button.edit').d('编辑'),
            },
            {
              key: 'children',
              ele: (
                <a onClick={() => this.handleLinkToDetail(record, 1, false)}>
                  {intl.get('hzero.common.button.addChildren').d('新增下级')}
                </a>
              ),
              len: 4,
              title: intl.get('hzero.common.button.addChildren').d('新增下级'),
            },
          ];
          // const enable = [
          //   ...children,
          //   {
          //     key: 'enable',
          //     ele: (
          //       <a onClick={() => this.handleEnable(rowDS, 1)}>
          //         {intl.get('hzero.common.button.enable').d('启用')}
          //       </a>
          //     ),
          //     len: 2,
          //     title: intl.get('hzero.common.button.enable').d('启用'),
          //   },
          // ];
          // const disable = [
          //   ...children,
          //   {
          //     key: 'disable',
          //     ele: (
          //       <a onClick={() => this.handleEnable(rowDS, 0)}>
          //         {intl.get('hzero.common.button.disable').d('禁用')}
          //       </a>
          //     ),
          //     len: 2,
          //     title: intl.get('hzero.common.button.disable').d('禁用'),
          //   },
          // ];
          return operatorRender(children);
        },
      },
    ];
  }

  render() {
    return (
      <React.Fragment>
        <Header title={intl.get(`amdm.location.view.title.location`).d('位置')}>
          <Button icon="add" color="primary" onClick={this.handleAddLocation}>
            {intl.get('hzero.common.button.create').d('新建')}
          </Button>
          <DefaultMaintainerModal
            moduleDataSet={this.props.listDS}
            primaryKey="assetLocationId"
            moduleName="LOCATION"
          />
        </Header>
        <Content>
          <Table
            key="locationList"
            customizedCode="AORI.LOCATION.LIST"
            mode="tree"
            queryBar="filterBar"
            dataSet={this.props.listDS}
            columns={this.columns}
            expandIcon={this.expandicon}
            className="halm-tree-table"
            onExpand={this.handleExpand.bind(this)}
            expandedRowRenderer={record => this.expandedRowRender(record)}
            queryBarProps={{ fuzzyQuery: false, queryFieldsLimit: 7 }}
          />
        </Content>
      </React.Fragment>
    );
  }
}
export default Location;
