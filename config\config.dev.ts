/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-06-14 10:18:31
 * @LastEditTime: 2023-06-15 11:52:21
 * @LastEditors: <<EMAIL>>
 */
import { IConfig } from 'umi'; // ref: https://umijs.org/config/

export default {
  define: {
    'process.env': {
      API_HOST: 'http://************:30080',
      CUSZ_CODE_BEFORE: 'KD', // 个性化单元前缀
      LOV_CODE_BEFORE: 'MT', // lov值集前缀
      HMES_BASIC: '/kd-mes',
      TARZAN_COMMON: '/kd-tznc',
      HRPT_COMMON: '/hrpt',
      TARZAN_MODEL: '/kd-tznm',
      TARZAN_REPORT: '/kd-tznr',
      TARZAN_METHOD: '/kd-tznd',
      TARZAN_SAMPLING: '/kd-tznq',
      TARZAN_HSPC: '/tzns',
      TARZAN_MONGO: '/tzng',
      ADDITIONAL: process.env.ADDITIONAL,
      PLATFORM_VERSION: 'SAAS',
      CLIENT_ID: 'localhost',
      BASE_PATH: '/',
      MULTIPLE_SKIN_ENABLE: true,
      SKIP_NO_CHANGE_MODULE: true,
      REACT_APP_SC_DISABLE_SPEEDY: 'false',
      PACKAGE_PUBLIC_URL: '',
      SKIP_TS_CHECK_IN_START: true,
      // aps
      POOL_QUERY: 'query',
      // 服务的请求url前缀
      APS_COMMON: '/kd-tznc',
      APS_METHOD: '/aps',
      APS_METHODTZND: '/tznd',
      BASE_SERVER: '/kd-aps',
      BASE_SERVERPURCHASE: '/aps-purchase',
      BASE_SERVERPLAN: '/aps-mltp',
      TARZAN_METHODTZND: '/tznd',
      API_PREFIX: '/kd-aori',
    },
  },
} as IConfig;
