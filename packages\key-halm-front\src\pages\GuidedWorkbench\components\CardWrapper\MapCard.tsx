import React, { useEffect, useState } from 'react';
// import BasicBMap from 'alm/components/BasicBMap';
import classNames from 'classnames';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_PFM } from 'alm/utils/config';
import notification from 'utils/notification';

import Styles from './index.module.less';
import PIC_PNG from '../../../../assets/pic.png';
import getLang from '../../Langs';

const organizationId = getCurrentOrganizationId();
const queryLonAndLatUrl = `${HALM_PFM}/v1/${organizationId}/maps/query-lon-and-lat`;

function index(props) {
  const { title, mapAddress } = props as any;
  const [latLon, setLatLon] = useState<any>();
  /* 直接使用地址，地图组件内部获取经纬度失败后切换到获取成功的地址会出现地图不显示的情况
     以及从有地图切换到获取经纬度失败的地址，依旧显示上一次地址的问题
     所以提前获取经纬度
  */
  useEffect(() => {
    handleSearch();
  }, [mapAddress]);
  // 处理手动输入可能会导致的特殊字符等问题
  const textFilter = str => {
    const pattern = new RegExp("[`~%!@^=''?~！$@￥……&‘”“'？*()（ ），,。.、]");
    let rs = '';
    if (str && str.length) {
      for (let i = 0; i < str.length; i++) {
        rs += str.substr(i, 1).replace(pattern, '');
      }
    }
    return rs;
  };

  // 根据当前默认地址address查询其经纬度然后生成地图
  const handleSearch = () => {
    const newAddress = textFilter(mapAddress);
    request(queryLonAndLatUrl, {
      method: 'GET',
      query: {
        address: newAddress,
      },
    }).then(res => {
      if (res && res.success) {
        const { data } = res;
        const lon = parseFloat(data.lng).toFixed(6);
        const lat = parseFloat(data.lat).toFixed(6);
        // 初始化地图并创建标记点
        setLatLon({
          initialLon: lon,
          initialLat: lat,
        } as any);
      } else {
        setLatLon(undefined);
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  const mapDetailProps = {
    // address: mapAddress,
    mapHeight: '100%',
    allowedDrag: false,
    isShowSpinOrSkeleton: false,
    allowedScrollWheelZoom: false,
    skeletonProps: {
      active: true,
      title: false,
      paragraph: {
        rows: 2,
      },
    },
  };

  return (
    <div className={classNames(Styles['card-wrapper'], Styles['map-wrapper'])}>
      <div className={Styles['map-left']}>
        <h6>{title}</h6>
        <div>{props.children}</div>
      </div>
      {latLon ? (
        <div className={Styles['map-right']}>
          {/* <BasicBMap {...mapDetailProps} {...latLon} /> */}
        </div>
      ) : (
        <div className={Styles['no-data']}>
          <img src={PIC_PNG} alt="" />
          <span>{getLang('EMPTY_TEXT')}</span>
        </div>
      )}
    </div>
  );
}

export default index;
