import React, { Component } from 'react';
import { isUndefined } from 'lodash';
import { Bind } from 'lodash-decorators';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import { yesOrNoRender, operatorRender } from 'utils/renderer';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { DefaultMaintainerModal } from 'alm/components/DefaultMaintainer';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/interface';
import getLang from '../Langs';
import { tableDS } from '../Stores';

@formatterCollections({
  code: ['alm.common', 'alm.component', 'aatn.assetSet'],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDS());
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true }
)
class AssetSet extends Component {
  // 获取表格展示列
  get columns() {
    return [
      {
        name: 'assetSetNum',
        width: 400,
        renderer: ({ value, record }) => {
          return <a onClick={() => this.handleGotoDetail(record.data.assetSetId)}>{value}</a>;
        },
      },
      { name: 'assetSetName' },
      {
        name: 'classLevelName',
      },
      { name: 'faCategoryName' },
      {
        name: 'enabledFlag',
        renderer: ({ value }) => yesOrNoRender(value),
      },
      {
        header: getLang('OPTION'),
        renderer: rowDS => {
          const { record } = rowDS;
          const minLevelFlag = record?.get('classLevelCode') === 'ASC';
          const disProps = { disabled: minLevelFlag };
          const operators = [
            {
              key: 'edit',
              ele: (
                <a onClick={() => this.handleGotoDetail(record.data.assetSetId, true)}>
                  {getLang('EDIT')}
                </a>
              ),
              len: 2,
              title: getLang('EDIT'),
            },
            {
              key: 'addChildren',
              ele: (
                <a {...disProps} onClick={() => this.handleAddChildren(record)}>
                  {getLang('ADD_CHILDREN')}
                </a>
              ),
              len: 4,
              title: getLang('ADD_CHILDREN'),
            },
            {
              key: 'delete',
              ele: (
                <Popconfirm
                  placement="topRight"
                  title={getLang('CONFIRM_DELETE')}
                  onConfirm={() => this.handleDelete(record)}
                  okText={getLang('SURE')}
                  cancelText={getLang('CANCEL')}
                >
                  <a style={{ color: 'red' }}>{getLang('DELETE')}</a>
                </Popconfirm>
              ),
              len: 2,
              title: getLang('DELETE'),
            },
          ];
          return operatorRender(operators);
        },
      },
    ];
  }

  /**
   * 页面跳转
   * @param {string} id - 设备资产id
   * @param {boolean} flag -明细页是否直接进入编辑状态
   */
  @Bind()
  handleGotoDetail(id, flag = false) {
    const { history } = this.props;
    const linkUrl = isUndefined(id) ? 'create' : `detail/${id}`;
    history.push({
      pathname: `/aori/assetClass/${linkUrl}`,
      state: {
        isEdit: flag,
      },
    });
  }

  @Bind()
  handleAddChildren(record) {
    const { history } = this.props;
    history.push({
      pathname: `/aori/assetClass/create`,
      query: {
        parentClassId: record.get('assetSetId'),
        parentClassName: record.get('assetSetName'),
        parentClassLevelCode: record.get('classLevelCode'),
      },
    });
  }

  @Bind()
  async handleDelete(record) {
    await this.props.listDS.delete(record, false);
  }

  // 这里面可以控制node结点的判断来实现是否展示为叶结点
  @Bind()
  nodeCover({ record }) {
    const nodeProps = {};
    if (record.get('childFlag') !== 1) {
      nodeProps.isLeaf = true;
    }
    return nodeProps;
  }

  render() {
    return (
      <>
        <Header title={getLang('HEADER')}>
          <Button icon="add" color="primary" onClick={() => this.handleGotoDetail()}>
            {getLang('CREATE')}
          </Button>
          <DefaultMaintainerModal
            moduleDataSet={this.props.listDS}
            primaryKey="assetSetId"
            moduleName="ASSET_SET"
          />
        </Header>
        <Content>
          <Table
            key="assetClassList"
            customizedCode="AORI.ASSET_CLASS.ASSET_CLASS_LIST"
            mode="tree"
            treeAsync
            dataSet={this.props.listDS}
            columns={this.columns}
            searchCode="AORI.ASSET_CLASS.ASSET_CLASS_LIST"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQueryPlaceholder: getLang('ASSET_CLASS_LIST_PLACEHOLDER'),
            }}
            onRow={this.nodeCover}
          />
        </Content>
      </>
    );
  }
}

export default AssetSet;
