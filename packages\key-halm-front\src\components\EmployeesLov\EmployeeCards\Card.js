/**
 * 员工Lov组件
 * @date: 2021/03/01
 * <AUTHOR>
 * @copyright Copyright (c) 2021, Hand
 */
import React from 'react';
import { Row, Col, Popover, Tooltip } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { toRGBA } from 'alm/utils/utils';
import classNames from 'classnames';
import getLangs from '../Langs';
import styles from '../index.module.less';

@formatterCollections({
  code: ['alm.common', 'alm.component'],
})
export default class Card extends React.PureComponent {
  render() {
    const { card, selectedCardKeys = [], wcLovFlag, hrSkillFlag, onClickCard } = this.props;
    const cardKey = `${card.employeeId}_${card.workCenterId}`;
    const content = (
      <div className="card-popover-more">
        {card.employeeSkillList &&
          card.employeeSkillList.map(skill => (
            <span
              style={{
                color: skill.skillLevelColor,
                backgroundColor: toRGBA(skill.skillLevelColor, 0.16),
              }}
            >
              {skill.skillName}
            </span>
          ))}
      </div>
    );

    return (
      <React.Fragment>
        <div
          className={classNames(
            styles['employee-card'],
            hrSkillFlag === 1 || !wcLovFlag ? 'employee-card-50' : 'employee-card-33',
            selectedCardKeys.findIndex(k => k === cardKey) !== -1 && styles['card-selected']
          )}
          onClick={() => onClickCard(card)}
        >
          <Row>
            <Col span={24}>
              <div className={styles['employee-name']}>
                <span>
                  <Tooltip title={card.employeeName}>{card.employeeName}</Tooltip>
                </span>
                <span>
                  <Tooltip title={card.employeeNum}>{card.employeeNum} </Tooltip>
                </span>
              </div>
              {/* LOV处于工作中心菜单时不显示工作中心字段 */}
              <div className={styles['employee-line']}>
                {wcLovFlag !== 1 && (
                  <>
                    <span>{getLangs('WORK_CENTER')}：</span>
                    <span>{card.workCenterName}</span>
                  </>
                )}
              </div>
              <div className={styles['employee-skills']}>
                {hrSkillFlag === 1 && (
                  <>
                    <div className={styles['skills-normal']}>
                      {card.employeeSkillList &&
                        card.employeeSkillList.map(skill => (
                          <span
                            style={{
                              color: skill.skillLevelColor,
                              backgroundColor: toRGBA(skill.skillLevelColor, 0.16),
                            }}
                          >
                            {skill.skillName}
                          </span>
                        ))}
                    </div>
                    {card?.employeeSkillList?.length > 0 && (
                      <Popover placement="topRight" content={content} trigger="click">
                        <span className={styles['card-more']} />
                      </Popover>
                    )}
                  </>
                )}
              </div>
            </Col>
          </Row>
        </div>
      </React.Fragment>
    );
  }
}
