/**
 * 标准检查组
 * @date 2021/09/18
 * <AUTHOR>
 * @copyright Copyright (c) 2021,Hand
 */
import React, { Component } from 'react';
import { Button, Table, DataSet } from 'choerodon-ui/pro';
import { Bind } from 'lodash-decorators';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { enableRender } from 'utils/renderer';
import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import PointLov from 'alm/components/PointLov';

import getLangs from './Langs';
import { tableDS } from './Stores/listDs';

const modelPrompt = 'alm.checklistGroup.model.checklistGroup';
@formatterCollections({ code: ['alm.common', 'alm.component', 'aori.checklistGroup'] })
@withProps(
  () => {
    const listDS = new DataSet(tableDS());
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true },
)
export default class ChecklistGroupList extends Component {
  constructor(props) {
    super(props);
    this.state = {};

    props.listDS.queryDataSet.addEventListener('reset', () => {
      props.listDS.setQueryParameter('pointId', null);
    });
  }

  @Bind()
  handleCreate() {
    this.props.history.push({
      pathname: `/aori/checklist-group/create`,
    });
  }

  @Bind()
  handleViewOrEidt(record, isEdit = false) {
    this.props.history.push({
      pathname: `/aori/checklist-group/detail/${record.get('checklistGroupId')}`,
      state: { isEdit },
    });
  }

  get columns() {
    return [
      {
        name: 'checklistGroupName',
        renderer: ({ value, record }) => {
          return <a onClick={() => this.handleViewOrEidt(record)}>{value}</a>;
        },
      },
      {
        name: 'checklistGroupCode',
        width: 150,
      },
      {
        name: 'typeCode',
      },
      {
        name: 'maintSiteNameStr',
      },
      {
        name: 'description',
        width: 150,
      },
      {
        name: 'enabledFlag',
        width: 100,
        align: 'center',
        renderer: ({ value }) => enableRender(value),
      },
      {
        header: getLangs('OPTION'),
        width: 60,
        renderer: ({ record }) => {
          return <a onClick={() => this.handleViewOrEidt(record, true)}>{getLangs('EDIT')}</a>;
        },
      },
    ];
  }

  /**
   *  标准检查组导入
   */
   @Bind()
  handleMeterImport() {
    openTab({
      key: `/aori/checklist-group/data-import/STANDARD_INSPECTION_GR_IMPORT`,
      title: intl.get(`${modelPrompt}.title.import`).d('标准检查组导入'),
      search: queryString.stringify({
        action: intl.get(`${modelPrompt}.title.import`).d('标准检查组导入'),
      }),
    });
  }

   render() {
     return (
       <React.Fragment>
         <Header title={getLangs('TITLE')}>
           <Button icon="add" color="primary" onClick={this.handleCreate}>
             {getLangs('CREATE')}
           </Button>
           <Button icon="file_upload" onClick={this.handleMeterImport} key="import">
             {intl.get('hzero.common.button.import').d('导入')}
           </Button>
         </Header>
         <Content>
           <Table
             key="checklistGroup"
             customizedCode="AORI.CHECKLIST_GROUP.LIST"
             dataSet={this.props.listDS}
             columns={this.columns}
             queryFields={{
               pointName: <PointLov isQuery listDS={this.props.listDS} />,
             }}
           />
         </Content>
       </React.Fragment>
     );
   }
}
