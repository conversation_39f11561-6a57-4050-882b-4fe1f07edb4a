/**
 * 位置相关逻辑
 * @since 2021/09/14
 * <AUTHOR> <<EMAIL>>
 */
import React from 'react';
import { notification } from 'choerodon-ui';
import { Form, Modal, Output } from 'choerodon-ui/pro';
// import BasicBMap from 'alm/components/BasicBMap';
import LocationTreeModal from 'alm/pages/ServiceApply/Detail/LocationTreeModal';
import { getAssetLocationInfo } from './commonApi';
import {
  handleClearFault as onClearFault,
  handlePlannerAndOwner as onPlannerAndOwner,
  handleChangeCostObjOfLocOrAsset as onChangeCostObjOfLocOrAsset,
} from './index';

import getLangs from '../Langs';

/**
 * 资产/设备 值改变(通用模块5、6)
 * 2021/10/08
 * 1、模块6相比模块5，需要对成本对象进行联动修改
 * 2、模块5由于组件的特殊性，需要单独赋值
 * 3、模块5还需单独清空“缺陷评估项、故障部位、故障现象”
 * @param {*} value 当前值
 * @param {*} lovRecord lov选中的行
 */
const handleChangeLocation = async (lovRecord, record, moreProps = {}, moreFun = {}) => {
  const { searchItem, handleRecord, serviceType } = moreProps;
  const { onChangeShowFault, onChangeRcAssesment, onMoreOperations } = moreFun;
  const { sourceParamType } = searchItem;
  const hasOwner = !['SUB', 'SR'].includes(sourceParamType);

  const strategy = {
    SR: () => {
      // 由于模块5组件的特殊性，需要单独赋值
      record.set('assetLocationId', lovRecord?.assetLocationId);
      record.set('assetLocationName', lovRecord?.locationName);
      // 隐藏并清空“缺陷评估项、故障部位、故障现象”
      onChangeShowFault(false);
      onClearFault(record);
      onChangeRcAssesment();
    },
    SUB: () => {
      onChangeCostObjOfLocOrAsset(lovRecord, record);
    },
  };

  record.set('assetId', null);
  record.set('descAndLabel', null);

  if (lovRecord && lovRecord.maintSiteId !== record.get('maintSiteId')) {
    record.set('maintSiteId', lovRecord.maintSiteId);
    record.set('maintSiteName', lovRecord.maintSiteName);
  }

  // 为了获取最新设备信息设置了延迟
  setTimeout(() => {
    onPlannerAndOwner({
      queryData: {
        serviceType,
      },
      type: hasOwner ? 'both' : 'planner',
      record,
      handleRecord,
    });
  }, 0);

  switch (sourceParamType) {
    case 'SR':
      strategy.SR();
      break;
    case 'SUB':
      strategy.SUB();
      break;
    default:
  }

  // 更多操作，处理一些额外的操作
  if (onMoreOperations) {
    onMoreOperations(lovRecord);
  }
};

/**
 * 位置地图Modal
 */
const handleOpenMapModal = (record, moreProps) => {
  const { that, locationModalDS } = moreProps;
  const locationId = record.get('assetLocationId');
  if (that.mapTimer) {
    const message = '请不要重复点击';
    notification.warning({ message });
  } else {
    that.mapTimer = setTimeout(async () => {
      const res = await getAssetLocationInfo(locationId);

      if (res && res.regionIds) {
        const mapProps = {
          address: res.regionName.split('/').join(''),
          mapHeight: '200px',
          dataLoadSuccess: data => {
            locationModalDS.loadData([
              {
                address: res.regionName,
                currentLatLng: `${Number(data.lng).toFixed(4)}，${Number(data.lat).toFixed(4)}`,
              },
            ]);
          },
        };
        Modal.open({
          style: {
            width: 650,
          },
          key: Modal.key(),
          title: '地图校准',
          closable: true,
          okCancel: false,
          children: (
            <>
              <Form dataSet={locationModalDS} columns={2}>
                <Output name="address" />
                <Output name="currentLatLng" />
              </Form>
              {/* <BasicBMap {...mapProps} /> */}
            </>
          ),
        });
      } else {
        const message = '请维护位置信息!';
        notification.warning({ message });
      }
      clearTimeout(that.mapTimer);
      that.mapTimer = '';
    });
  }
};

const handleOpenLocTreeModal = (record, moreProps, moreFun) => {
  const { that, tenantId } = moreProps;
  const locationTreeModalProps = {
    tenantId,
    maintSiteId: record?.get('maintSiteId'),
    directMaintainFlag: 1,
    enabledFlag: 1,
  };
  Modal.open({
    style: {
      width: 650,
    },
    key: Modal.key(),
    title: getLangs('SELECT_LOCATION'),
    closable: true,
    children: <LocationTreeModal {...locationTreeModalProps} ref={that.locTreeRef} />,
    onOk: () => {
      const { locationTreeDS } = that.locTreeRef.current;
      const selectData =
        locationTreeDS.selected.length > 0 ? locationTreeDS.selected[0].data : null;
      handleChangeLocation(selectData, record, moreProps, moreFun);
    },
  });
};

export { handleOpenMapModal, handleChangeLocation, handleOpenLocTreeModal };
