#!/bin/bash
###
 # @Description:
 # <AUTHOR> <EMAIL>
 # @Date: 2023-06-14 10:18:31
 # @LastEditTime: 2023-06-15 15:07:24
 # <AUTHOR> <EMAIL>
###
set -e

find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_PATH ${BUILD_BASE_PATH:-/} g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find /usr/share/nginx/html -name '*.html' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find /usr/share/nginx/html -name '*.css' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_API_HOST $BUILD_API_HOST g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_CLIENT_ID $BUILD_CLIENT_ID g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_WEBSOCKET_HOST $BUILD_WEBSOCKET_HOST g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_PLATFORM_VERSION $BUILD_PLATFORM_VERSION g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_IM_ENABLE $BUILD_IM_ENABLE g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_IM_WEBSOCKET_HOST $BUILD_IM_WEBSOCKET_HOST g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TRACE_LOG_ENABLE $BUILD_TRACE_LOG_ENABLE g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_CUSTOMIZE_ICON_NAME $BUILD_CUSTOMIZE_ICON_NAME g"

find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_HMES_BASIC /kd-mes g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_COMMON /kd-tznc g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_HRPT_COMMON /hrpt g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_MODEL /kd-tznm g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_REPORT /kd-tznr g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_METHOD /kd-tznd g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_SAMPLING /kd-tznq g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_HSPC /kd-tzns g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_APS_METHODTZND /kd-tznd g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_APS_COMMON /kd-tznc g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_APS_METHOD /kd-aps g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_POOL_QUERY query g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_SERVERPLAN /kd-aps-mltp g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_SERVERPURCHASE /kd-aps-purchase g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_SERVER /kd-aps g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_CUSZ_CODE_BEFORE KD g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_LOV_CODE_BEFORE KD g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_API_PREFIX /kd-aori g"


exec "$@"
