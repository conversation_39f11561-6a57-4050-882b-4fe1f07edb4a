import { HZERO_PLATFORM } from 'utils/config';
import { HALM_ATN } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ATN}/v1`;

/**
 * 查询当前用户在当前租户下的员工
 * @param detail 详情页的DS
 */
function getEmployee() {
  return {
    autoQuery: true,
    dataKey: 'content',
    transport: {
      read: ({ data, params }) => {
        const url = `${HZERO_PLATFORM}/v1/${organizationId}/employee-users/employee`;
        return {
          data,
          params,
          url,
          method: 'GET',
        };
      },
    },
  };
}

function completeDS() {
  return {
    autoCreate: true,
    fields: [
      {
        label: getLang('HANDLE_TYPE'),
        name: 'handleType',
        type: 'string',
        lookupCode: 'ACNT.COUNT_PROCESS_TYPE',
        required: true,
      },
      {
        name: 'employeeLov',
        type: 'object',
        lovCode: 'HALM.EMPLOYEE',
        lovPara: { tenantId: organizationId },
        label: getLang('HANDLE_PERSON_NAME'),
        ignore: 'always',
        disabled: true,
      },
      {
        name: 'handlePersonName',
        type: 'string',
        bind: 'employeeLov.employeeName',
        label: getLang('HANDLE_PERSON_NAME'),
      },
      {
        name: 'handlePersonId',
        type: 'number',
        bind: 'employeeLov.employeeId',
      },
      {
        label: getLang('HANDLE_DESCRIPTION'),
        name: 'handleDescription',
        type: 'string',
        maxLength: 240,
      },
      {
        label: getLang('ASSET_NUM'),
        name: 'assetLov',
        type: 'object',
        lovCode: 'ACNT.CHOOSE_ASSET',
        ignore: 'always',
        dynamicProps: {
          required: ({ record }) => {
            return record.get('handleType') === 'MATCH_ASSET';
          },
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              lineId: record.get('countingLineId'),
            };
          },
        },
      },
      {
        name: 'relateId',
        type: 'number',
        bind: 'assetLov.assetId',
      },
      {
        label: getLang('ASSET_NUM'),
        name: 'assetName',
        type: 'string',
        bind: 'assetLov.assetName',
      },
      {
        name: 'transactionTypeLov',
        type: 'object',
        lovCode: 'ACNT.CHOOSE_TRANSACTION',
        lovPara: {
          tenantId: organizationId,
          enabledFlag: 1,
        },
        label: getLang('TRANSACTION_TYPE'),
        noCache: true,
        ignore: 'always',
        dynamicProps: {
          required: ({ record }) => {
            return record.get('handleType') === 'ASSET_TRANS_PROCESS';
          },
        },
      },
      {
        name: 'transactionTypeId',
        type: 'number',
        bind: 'transactionTypeLov.transactionTypeId',
      },
      {
        label: getLang('TRANSACTION_TYPE'),
        name: 'transactionTypeName',
        type: 'string',
        bind: 'transactionTypeLov.transactionTypeName',
      },
    ],
  };
}

function lineDS(detail) {
  return {
    selection: false,
    paging: false,
    primaryKey: 'tplLineId',
    transport: {
      read: ({ dataSet }) => {
        const permissionData = dataSet.getState('permissionData');
        const { lineId, taskTplId } = detail;
        const url = `${apiPrefix}/${organizationId}/counting-result`;
        return {
          url,
          method: 'GET',
          params: {
            lineId,
            taskTplId,
            ...permissionData,
          },
        };
      },
    },
  };
}

export { getEmployee, completeDS, lineDS };
