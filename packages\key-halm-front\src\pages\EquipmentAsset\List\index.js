/**
 * List - 资产/设备列表
 * @date: 2020-09-30
 * @author: DCY <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component } from 'react';
import intl from 'utils/intl';
import { DataSet, Button, Table, Modal, Form, Lov } from 'choerodon-ui/pro';
import { isUndefined, isEmpty } from 'lodash';
import axios from 'axios';
import { Bind } from 'lodash-decorators';
import { observer } from 'mobx-react';
import { Header, Content } from 'components/Page';
// import CusExcelExport from 'alm/components/CusExcelExport';
import ExcelExport from 'components/ExcelExportPro';
import { API_HOST } from 'utils/config';
import { HALM_ATN, HALM_ORI } from 'alm/utils/config';
import { getCurrentOrganizationId, getDateFormat } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { operatorRender, yesOrNoRender, dateRender } from 'utils/renderer';
import notification from 'utils/notification';
import moment from 'moment';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
// import withProps from 'utils/withProps';
import { DefaultMaintainerModal } from 'alm/components/DefaultMaintainer';
import QueryBar from '../../../components/QueryBar';
import { listDS, labelTplDS, frameworkDs, createModalDS } from '../Stores/ListDS';
import Preview from './Preview';
import PrintElement from './PrintElement';

const tenantId = getCurrentOrganizationId();
const prompt = 'aatn.equipmentAsset';
const apiPrefix = `${HALM_ORI}/v1`;

// @withProps(
//   () => {
//     const tableDS = new DataSet(listDS());
//     return {
//       tableDS,
//     };
//   },
//   { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true  }
// )
@formatterCollections({
  code: ['alm.common', 'alm.component', 'aatn.equipmentAsset'],
})
@observer
class EquipmentAsset extends Component {
  constructor(props) {
    super(props);
    this.tableDS = new DataSet(listDS());
    this.frameworkDs = new DataSet(frameworkDs());
    this.labelTplDs = new DataSet({ ...labelTplDS() });
    this.exportRef = React.createRef();
    this.exportCkBoxRef = React.createRef();
  }

  // 获取表格展示列
  get columns() {
    return [
      {
        name: 'assetDesc',
        width: 200,
        lock: 'left',
        renderer: ({ value, record }) => {
          return <a onClick={() => this.handleGotoDetail(record)}>{value}</a>;
        },
      },
      { name: 'assetName', width: 150 },
      { name: 'assetNum', width: 150 },
      { name: 'visualLabel', width: 150 },
      {
        name: 'assetSetName',
      },
      { name: 'assetSpecialtyName' },
      { name: 'statusName', width: 130 },
      { name: 'assetLocationName' },
      { name: 'brand' },
      { name: 'model' },
      { name: 'owningOrgName' },
      { name: 'usingOrgName' },
      { name: 'userPersonName' },
      { name: 'assetImportanceMeaning' },
      { name: 'warrantyExpireDate', renderer: ({ value }) => dateRender(value) },
      {
        name: 'expire',
        align: 'center',
        width: 100,
        renderer: ({ record }) =>
          yesOrNoRender(
            moment(new Date()).format(getDateFormat()) >
              moment(record.data.warrantyExpireDate).format(getDateFormat())
              ? 1
              : 0,
          ),
      },
      {name: 'trackingNum'},
      {name: 'description'},
      {name: 'receivedDate'},
      {name: 'startDate'},
      {
        header: intl.get('hzero.common.table.column.option').d('操作'),
        lock: 'right',
        renderer: rowDS => {
          const { record } = rowDS;
          const operators = [
            {
              key: 'edit',
              ele: (
                <a onClick={() => this.handleGotoDetail(record, true)}>
                  {intl.get('hzero.common.button.edit').d('编辑')}
                </a>
              ),
              len: 2,
              title: intl.get('hzero.common.button.edit').d('编辑'),
            },
          ];
          return operatorRender(operators);
        },
      },
    ];
  }

  componentDidUpdate() {
    const {
      history,
      location: { state },
    } = this.props;
    if (state?.createFlag) {
      this.handleCreate();
      history.replace(window.location.pathname, {});
    }
  }

  /**
   * 页面跳转
   * @param {boolean} flag -明细页是否直接进入编辑状态
   */
  @Bind()
  handleGotoDetail(record, flag = false) {
    const { assetId } = record.data;
    const linkUrl = isUndefined(assetId) ? 'create' : `detail/${assetId}`;
    this.props.history.push({
      pathname: `/aafm/equipment-asset/${linkUrl}`,
      state: {
        isEdit: flag,
      },
    });
  }

  @Bind()
  handleCreate() {
    const modal = Modal.open({
      key: Modal.key(),
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      okText: intl.get(`alm.common.button.next`).d('下一步'),
      cancelText: intl.get(`hzero.common.button.cancel`).d('取消'),
      children: this.getCreateModalChildren(),
      onOk: async () => {
        const result = await this.createModalDS.validate();
        if (result) {
          await this.handleCreateModalOk();
        } else {
          return false;
        }
      },
      onCancel: () => this.handleCancel(modal),
    });
  }

  @Bind()
  getCreateModalChildren() {
    this.createModalDS = new DataSet(createModalDS());
    return (
      <Form dataSet={this.createModalDS}>
        <Lov
          name="assetClassLov"
          onChange={record => this.createModalDS.current.set('assetSet', record)}
        />
        <Lov name="assetLocationLov" />
      </Form>
    );
  }

  @Bind()
  async handleCreateModalOk() {
    const params = {
      ...this.createModalDS.current.toData(),
      otherSourceFlag: false,
      // 资产分类详情
      assetSet: this.createModalDS.current.get('assetSet'),
    };
    this.props.history.push({
      pathname: `/aafm/equipment-asset/create`,
      search: queryString.stringify({ originData: JSON.stringify(params) }),
      state: {
        isEdit: false,
      },
    });
  }

  @Bind()
  handleCancel(modal) {
    this.createModalDS.reset();
    modal.close();
  }

  /**
   *  设备资产导入
   */
  @Bind()
  handleImport() {
    openTab({
      key: `/aafm/equipment-asset/data-import/AATN.ASSET`,
      title: intl.get(`${prompt}.title.equipAssetImport`).d('设备/资产导入'),
      search: queryString.stringify({
        action: intl.get(`${prompt}.title.equipAssetImport`).d('设备/资产导入'),
      }),
    });
  }

  /**
   *  设备资产-导出模板-导入
   */
  @Bind()
  handleExImport() {
    openTab({
      key: `/aafm/equipment-asset/data-import/AATN.ASSET_EX`,
      title: intl.get(`${prompt}.title.equipAssetImportExport`).d('设备/资产导出模板-导入'),
      search: queryString.stringify({
        action: intl.get(`${prompt}.title.equipAssetImportExport`).d('设备/资产导出模板-导入'),
      }),
    });
  }

  @Bind()
  handleChangePrintRef(ref) {
    this.printDomRef = ref;
  }

  // 打开标签模板预览
  @Bind()
  handlePreview() {
    const selectedRowKeys = [];
    if (!isEmpty(this.tableDS.selected)) {
      this.tableDS.selected.forEach(item => {
        selectedRowKeys.push(item.data.assetId);
      });
    }
    this.labelTplDs.create();
    Modal.open({
      key: Modal.key(),
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      title: intl.get('hzero.common.button.preview').d('预览'),
      children: React.createElement(Preview, {
        selectedRowKeys,
        labelTplDs: this.labelTplDs,
        onChangePrintRef: this.handleChangePrintRef,
      }),
      drawer: true,
      style: {
        width: 1000,
      },
      okText: intl.get(`${prompt}.button.workLabel`).d('标签打印'),
      onOk: this.modelOk,
      cancelText: intl.get('hzero.common.button.close').d('关闭'),
    });
  }

  // PDF下载
  @Bind()
  async modelOk() {
    const flag = await this.labelTplDs.validate();
    // 1.直接打印
    if (flag) {
      PrintElement({
        content: this.printDomRef,
      });
    }
    return false;
  }

  /**
   * 同步
   */
  @Bind()
  handleSync() {
    axios({
      url: `${API_HOST}${apiPrefix}/${tenantId}/asset-info/syncIot`,
      method: 'POST',
      data: this.tableDS.selected.map(i => i.toData()),
    }).then(res => {
      if (res) {
        if (res.unAbledFlag === 0) {
          notification.success();
          this.tableDS.query();
        } else {
          const unAbledList = res.unAbledList.join(',');
          const message = `${intl
            .get(`${prompt}.view.message.assetNum`)
            .d('资产编号')}:  ${unAbledList} , ${intl
            .get(`${prompt}.view.message.syncError`)
            .d('同步失败')}`;
          notification.warning({
            duration: null,
            message,
          });
          this.tableDS.query();
        }
      }
    });
  }

  renderBar = props => {
    const queryProps = {
      ...props,
      moduleName: 'ASSET_SET',
    };
    return <QueryBar {...queryProps} />;
  };

  render() {
    const selectedRowKeys = [];
    if (!isEmpty(this.tableDS.selected)) {
      this.tableDS.selected.forEach(item => {
        selectedRowKeys.push(item.data.assetId);
      });
    }
    // const exportParams = isEmpty(selectedRowKeys) ? {} : { assetIds: selectedRowKeys.join(',') };
    const exportParams = this.tableDS.getState('preConditionList')?.length > 0 ? { preConditionList: this.tableDS.getState('preConditionList') } : {};
    const iotFlag = this.frameworkDs.current && this.frameworkDs.current.get('iotFlag');
    return (
      <React.Fragment>
        <Header title={intl.get(`${prompt}.title.equipmentAsset`).d('设备/资产')}>
          <Button icon="add" color="primary" onClick={this.handleCreate}>
            {intl.get('hzero.common.button.create').d('新建')}
          </Button>
          {iotFlag ? (
            <Button
              icon="autorenew"
              onClick={this.handleSync}
              disabled={isEmpty(this.tableDS.selected)}
            >
              {intl.get('hzero.common.button.sync').d('同步')}
            </Button>
          ) : null}
          {/* <CusExcelExport
            requestUrl={`${HALM_ATN}/v1/${tenantId}/asset-info/export`}
            queryParams={exportParams}
            showDynamicField
          /> */}
          <ExcelExport
            method="POST"
            allBody
            exportAsync
            requestUrl={`${HALM_ATN}/v1/${tenantId}/asset-info/export`}
            queryParams={exportParams}
            buttonText={intl.get('hzero.common.button.export').d('导出')}
            modalProps={{ drawer: false }}
          />
          <Button icon="vertical_align_top" onClick={this.handleImport}>
            {intl.get('hzero.common.button.import').d('导入')}
          </Button>
          <Button icon="vertical_align_top" onClick={this.handleExImport}>
            {intl.get('hzero.common.button.import-export').d('导出模板-导入')}
          </Button>
          <Button
            icon="printer"
            onClick={this.handlePreview}
            disabled={isEmpty(this.tableDS.selected)}
          >
            {intl.get(`${prompt}.button.selectTplPrint`).d('选择模版并打印')}
          </Button>
          <DefaultMaintainerModal
            moduleDataSet={this.tableDS}
            primaryKey="assetId"
            moduleName="ASSET"
          />
        </Header>
        <Content>
          <Table
            key="eqpAssetList"
            customizedCode="AORI.EQP_ASSET.LIST"
            dataSet={this.tableDS}
            columns={this.columns}
            queryBar={this.renderBar}
          />
        </Content>
      </React.Fragment>
    );
  }
}

export default EquipmentAsset;
