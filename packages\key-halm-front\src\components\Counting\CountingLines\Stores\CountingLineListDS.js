/**
 * CountingLineListDS - 盘点清单
 * @date: 2020-07-24
 * @author: changyu.duan <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2019, Hand
 */
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_ATN } from 'alm/utils/config';

const apiPrefix = `${HALM_ATN}/v1`;
const promptCode = 'aatn.countingLine.model.countingLine';
const organizationId = getCurrentOrganizationId();

export default customizedCode => ({
  // 暂不设置主键，清单数据会有重复，导致查询渲染出错
  // primaryKey: 'lineId',
  autoQuery: true,
  selection: false,
  pageSize: 10,
  fields: [
    {
      label: intl.get(`${promptCode}.lineNumber`).d('盘点清单编号'),
      name: 'lineNumber',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.assetNum`).d('资产编号'),
      name: 'assetNum',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.assetName`).d('资产名称'),
      name: 'assetName',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.countingStatus`).d('盘点状态'),
      name: 'countingStatusMeaning',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.countingStatus`).d('盘点状态'),
      name: 'countingStatus',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.countingType`).d('盘点类型'),
      name: 'countingTypeName',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.processStatus`).d('处理状态'),
      name: 'processStatusMeaning',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.processStatus`).d('处理状态'),
      name: 'processStatus',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.batchNumber`).d('盘点计划编号'),
      name: 'batchNumber',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.batchName`).d('盘点计划名称'),
      name: 'batchName',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.taskNumber`).d('盘点任务编号'),
      name: 'taskNumber',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.task`).d('盘点任务'),
      name: 'taskName',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.countingPerson`).d('盘点人员'),
      name: 'countingPersonName',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.handleType`).d('处理类型'),
      name: 'handleTypeMeaning',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.relateNum`).d('关联编号'),
      name: 'relateNum',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.handlePerson`).d('处理人'),
      name: 'handlePersonName',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.handleTime`).d('处理时间'),
      name: 'handleTime',
      type: 'dataTime',
    },
    {
      label: intl.get(`${promptCode}.handleDescription`).d('处理说明'),
      name: 'handleDescription',
      type: 'string',
    },
  ],
  queryFields: [
    {
      label: intl.get(`${promptCode}.assetNum`).d('资产编号'),
      name: 'assetNum',
      type: 'string',
    },
    {
      label: intl.get(`${promptCode}.assetName`).d('资产名称'),
      name: 'assetName',
      type: 'string',
      maxLength: 40,
    },
    {
      label: intl.get(`${promptCode}.countingStatus`).d('盘点状态'),
      name: 'countingStatus',
      type: 'string',
      lookupCode: 'ACNT.COUNTING_LINE_STATUS',
    },
    {
      label: intl.get(`${promptCode}.countingPerson`).d('盘点人员'),
      name: 'countingPersonLov',
      type: 'object',
      lovCode: 'HALM.EMPLOYEE',
      lovPara: { tenantId: organizationId },
      ignore: 'always',
      multiple: true,
    },
    {
      name: 'countingPersonIds',
      type: 'number',
      bind: 'countingPersonLov.employeeId',
      multiple: true,
    },
    customizedCode !== 'AORI.TASK.COUNTING_LINES' && {
      label: intl.get(`${promptCode}.task`).d('盘点任务'),
      name: 'taskLov',
      type: 'object',
      lovCode: 'ACNT.COUNTING_TASK',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          const batchId = dataSet.getState('batchId');
          return {
            tenantId: organizationId,
            batchId,
          };
        },
      },
      ignore: 'always',
    },
    customizedCode !== 'AORI.TASK.COUNTING_LINES' && {
      name: 'taskId',
      type: 'number',
      bind: 'taskLov.taskId',
    },
    {
      label: intl.get(`${promptCode}.processStatus`).d('处理状态'),
      name: 'processStatus',
      type: 'string',
      lookupCode: 'ACNT.COUNT_PROCESS_STATUS',
    },
    {
      label: intl.get(`${promptCode}.lineNumber`).d('盘点清单编号'),
      name: 'lineNumber',
      type: 'string',
      maxLength: 40,
    },
  ],
  transport: {
    read: ({ data, dataSet }) => {
      const permissionData = dataSet?.getState('permissionData') ?? {};
      const isBatch = customizedCode === 'AORI.BATCH.COUNTING_LINES';
      const url = `${apiPrefix}/${organizationId}/counting-line`;
      const batchUrl = `${apiPrefix}/${organizationId}/counting-line/count-plan`;
      return {
        data: { ...data, ...permissionData },
        url: isBatch ? batchUrl : url,
        method: 'GET',
      };
    },
  },
});
