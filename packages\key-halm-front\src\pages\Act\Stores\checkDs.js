/**
 * 检查项list
 * @since 2021-01-20
 * <AUTHOR>
 * @copyright Copyright (c) 2020, Hand
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';
import getLangs from '../Langs';

const organizationId = getCurrentOrganizationId();

const queryListUrl = `${HALM_MTC}/v1/${organizationId}/act-checklists/list/c7n-tree`;

const commonFields = () => [
  {
    name: 'expand',
    type: 'boolean',
  },
  {
    name: 'checklistName',
    label: getLangs('CHECKLIST_NAME'),
    type: 'string',
  },
  {
    name: 'businessScenarioMeaning',
    label: getLangs('SCENARIO'),
    type: 'string',
  },
  {
    name: 'methodCode',
    label: getLangs('METHOD_CODE'),
    type: 'string',
  },
  {
    name: 'standardReference',
    label: getLangs('STANDARD_REFER'),
    type: 'string',
  },
  {
    name: 'columnTypeMeaning',
    label: getLangs('COLUMN_TYPE'),
    type: 'string',
  },
  {
    name: 'importance',
    label: '重要程度',
    type: 'boolean',
    trueValue: "Y",
    falseValue: "N",
    defaultValue: "Y",
  },
];

// 标准作业检查项
function actTableDs(headerTypeCode) {
  return {
    selection: false,
    paging: 'server',
    pageSize: 10,
    primaryKey: 'checklistId',
    dataKey: 'content',
    idField: 'checklistId',
    parentField: 'parentChecklistId',
    expandField: 'expand',
    fields: commonFields(),
    transport: {
      read: ({ params }) => {
        return {
          url: queryListUrl,
          method: 'GET',
          params: {
            ...params,
            parentTypeCode: headerTypeCode,
            tenantId: organizationId,
          },
        };
      },
    },
  };
}

// 任务检查项 - 查询ds
function woopSearchDs() {
  return {
    autoCreate: true,
    fields: [
      {
        name: 'activityOpNumber',
        type: 'number',
        label: getLangs('ACTIVITY_OP_NUMBER'),
      },
      {
        name: 'actOpName',
        type: 'string',
        label: getLangs('ACT_OP_NAME'),
      },
    ],
  };
}

// 任务检查项
function woopTableDs(headerTypeCode = 'ACT') {
  const _fields =
    headerTypeCode === 'ACT'
      ? [
          {
            name: 'parentName',
            label: getLangs('ACT_OP_NAME'),
            type: 'string',
          },
          ...commonFields(),
        ]
      : commonFields();
  return {
    selection: false,
    paging: 'server',
    pageSize: 10,
    primaryKey: 'checklistId',
    dataKey: 'content',
    idField: 'checklistId',
    parentField: 'parentChecklistId',
    expandField: 'expand',
    queryFields: [
      {
        name: 'activityOpNumber',
        type: 'number',
        label: getLangs('ACTIVITY_OP_NUMBER'),
      },
      {
        name: 'actOpName',
        type: 'string',
        label: getLangs('ACT_OP_NAME'),
      },
    ],
    fields: [
      {
        name: 'activityOpNumber',
        label: getLangs('ACTIVITY_OP_NUMBER'),
        type: 'string',
      },
      ..._fields,
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: queryListUrl,
          method: 'GET',
          params: {
            ...params,
            parentTypeCode: headerTypeCode === 'ACT' ? 'ACT_OP' : 'MAINTAIN_PLAN_STEP',
            tenantId: organizationId,
          },
        };
      },
    },
  };
}

export { actTableDs, woopTableDs, woopSearchDs };
