import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';

const getLangs = key => {
  const PREFIX = 'aori.checklistGroup';
  const MODELPREFIX = `${PREFIX}.model.checklistGroup`;
  const LANGS = {
    ...getCommonLangs(),
    // title
    TITLE: intl.get(`${PREFIX}.view.message.title`).d('标准检查组'),
    TITLE_CHECK: intl.get(`${PREFIX}.view.message.title.checklistModal`).d('检查项'),
    TITLE_WARN: intl.get(`${PREFIX}.view.message.title.warn`).d('警告'),
    // button

    // message
    CONFIRM_SAVE: intl
      .get(`${PREFIX}.view.message.confirmSave`)
      .d('由于修改了限定分配服务区域，不符合限定的适用范围数据将被清空，是否确认修改？'),

    // model
    GROUP_NAME: intl.get(`${MODELPREFIX}.checklistGroupName`).d('检查组名称'),
    GROUP_CODE: intl.get(`${MODELPREFIX}.checklistGroupCode`).d('检查组代码'),
    GROUP_TYPE_CODE: intl.get(`${MODELPREFIX}.typeCode`).d(' 检查组类型'),
    MAINTSITES: intl.get(`${MODELPREFIX}.maintSites`).d('限定分配服务区域'),
    MAINTSITE: intl.get(`${MODELPREFIX}.maintSite`).d('服务区域'),
    POINT: intl.get(`${MODELPREFIX}.point`).d('点位'),
    ROUTE: intl.get(`${MODELPREFIX}.route`).d('路线'),
    GROUP_DESC: intl.get(`${MODELPREFIX}.description`).d('检查组补充说明'),
    CHECKLIST_NAME: intl.get(`${MODELPREFIX}.checklistName`).d('名称'),
    BUSINESS_SCENARIO: intl.get(`${MODELPREFIX}.businessScenario`).d('检查时点'),
    METHOD_CODE: intl.get(`${MODELPREFIX}.methodCode`).d('检测方式'),
    REFERENCE: intl.get(`${MODELPREFIX}.standardReference`).d('参考标准'),
    IMPORTANCE: intl.get(`${MODELPREFIX}.importance`).d('重要程度'),
    COLUMN_TYPE: intl.get(`${MODELPREFIX}.columnType`).d('记录方式'),
    OBJ_TYPE: intl.get(`${MODELPREFIX}.objType`).d('对象类型'),
    OBJ_NAME: intl.get(`${MODELPREFIX}.objName`).d('名称'),
    OBJ_CODE: intl.get(`${MODELPREFIX}.objCode`).d('编码信息'),
    POINT_TYPE: intl.get(`${MODELPREFIX}.pointType`).d('点位类型'),
    NO_CHECK_LIST: intl
      .get(`${MODELPREFIX}.noCheckList`)
      .d('当前标准检查组已禁用，启用需至少添加一条检查项数据。'),
    // tabs
    TAB_CHECKLIST: intl.get(`${PREFIX}.view.message.tab.checklist`).d('检查项'),
    TAB_WORK_OBJ: intl.get(`${PREFIX}.view.message.tab.workObj`).d('适用范围'),
  };
  return LANGS[key];
};

export default getLangs;
