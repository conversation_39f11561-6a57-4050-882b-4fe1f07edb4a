/**
 * 设备相关逻辑
 * @since 2021/09/14
 * <AUTHOR> <<EMAIL>>
 */
import { isUndefined } from 'lodash';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';

import { HALM_MDM } from 'alm/utils/config';
import { getEvalItem } from './commonApi';
import {
  handleClearFault as onClearFault,
  handlePlannerAndOwner as onPlannerAndOwner,
  handleChangeCostObjOfLocOrAsset as onChangeCostObjOfLocOrAsset,
} from './index';

const organizationId = getCurrentOrganizationId();

/**
 * 资产/设备 值改变(委外)
 * @param {*} value 当前值
 * @param {*} lovRecord lov选中的行
 */
const handleChangeDescAndLabel = (lovRecord, record, moreProps, moreFun) => {
  const { searchItem = {}, handleRecord = {}, serviceType } = moreProps;
  const { onMoreOperations } = moreFun;
  const { sourceParamType } = searchItem;
  const nowAssetLocationId = record.get('assetLocationId');
  const existFlag =
    !isUndefined(nowAssetLocationId) && nowAssetLocationId !== null && nowAssetLocationId !== '';
  const strategy = {
    WO: () => {
      if (Object.keys(lovRecord).length > 0) {
        record.set('descAndLabel', lovRecord.descAndLabel || `${lovRecord.assetDesc}-${lovRecord.assetNum}`);
      } else {
        record.set('descAndLabel', undefined);
      }

      if (!existFlag) {
        // 如果地图来源有值且为 位置： 设置经纬度的值
        const mapSourceCode =record.get('mapSourceCode');
        if (mapSourceCode && mapSourceCode === 'LOCATION') {
          request(`${HALM_MDM}/v1/${organizationId}/asset-locations`, {
            method: 'GET',
          }).then(res => {
            if (res && !res.failed) {
              const { lng, lat } = res;
              record.set('longitude', lng);
              record.set('latitude', lat);
            }
          });
        }
      }
    },
  };

  record.set('assetId', lovRecord?.assetId);
  record.set('assetNum', lovRecord?.assetNum);

  // 当前位置没有值时 取当前选中资产的位置
  if (!existFlag) {
    record.set('assetLocationId', lovRecord?.assetLocationId);
    record.set('assetLocationName', lovRecord?.assetLocationName);
  }

  // 设备变更后若服务区域发生变更修改工作中心相关信息
  if (lovRecord?.assetId && record.get('maintSiteId') !== lovRecord.maintSiteId) {
    record.set('maintSiteId', lovRecord.maintSiteId);
    record.set('maintSiteName', lovRecord.maintSiteName);
  }

  // 设备发生变化,重新查询默认职责
  onPlannerAndOwner({
    queryData: {
      serviceType,
    },
    type: sourceParamType === 'SUB' ? 'planner' : 'both',
    record,
    handleRecord,
  });

  // 设备与成本对象联动
  onChangeCostObjOfLocOrAsset(lovRecord, record);

  switch (sourceParamType) {
    case 'WO':
      strategy.WO();
      break;
    default:
  }

  // 更多操作，处理一些额外的操作
  if (onMoreOperations) {
    onMoreOperations(lovRecord);
  }
};

/**
 * 设备/资产 值改变（通用模块3：服务申请）
 */
const handleChangeAsset = async (lovRecord, record, moreProps = {}, moreFun = {}) => {
  const { searchItem = {}, handleRecord, serviceType } = moreProps;
  const { sourceParamType } = searchItem;
  const { onChangeShowFault, onChangeRcAssesment } = moreFun;
  if (lovRecord) {
    record.set('assetLocationId', lovRecord?.assetLocationId);
    record.set('assetLocationName', lovRecord?.assetLocationName);
  }

  // 服务区域id发生变更时设置工作中心信心
  if (lovRecord?.maintSiteId && record.get('maintSiteId') !== lovRecord?.maintSiteId) {
    // 获取服务区域详情
    record.set('maintSiteId', lovRecord?.maintSiteId);
    record.set('maintSiteName', lovRecord?.maintSiteName);
  }
  // 设备发生变化,重新查询默认职责
  onPlannerAndOwner({
    queryData: {
      serviceType,
    },
    type: 'both',
    record,
    handleRecord,
  });

  if (sourceParamType === 'SR') {
    if (lovRecord?.assetId) {
      const res = await getEvalItem(lovRecord.assetId);
      if (res && res.content && res.content.length > 0) {
        onChangeShowFault(true);
        if (res.content.length === 1) {
          onClearFault(record);
          record.set('evalItemLov', {
            evalItemId: res.content[0].evalItemId,
            evalItemName: res.content[0].evalItemName,
          });
          onChangeRcAssesment(res.content[0].evalItemId);
        } else {
          // 清空“缺陷评估项、故障部位、故障现象”
          onClearFault(record);
          onChangeRcAssesment();
        }
      } else {
        // 清空“缺陷评估项、故障部位、故障现象”
        onClearFault(record);
        onChangeRcAssesment();
      }
    } else {
      // 隐藏并清空“缺陷评估项、故障部位、故障现象”
      onChangeShowFault(false);
      onClearFault(record);
      onChangeRcAssesment();
    }
  }
};

export { handleChangeAsset, handleChangeDescAndLabel };
