/**
 * 资产报表
 * @date 2021-10-13
 * <AUTHOR> <<EMAIL>>
 * @version 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React, { FC, useMemo, useEffect, useState, useCallback, useRef, CSSProperties } from 'react';
import { isNull, isEmpty, isUndefined, debounce } from 'lodash';
import { Select, Table, DataSet, Button, Modal } from 'choerodon-ui/pro';
import { Skeleton, Spin, Icon } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { SelectionMode, TableAutoHeightType } from 'choerodon-ui/pro/lib/table/enum';
import { Header, Content } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import formatterCollections from 'utils/intl/formatterCollections';
import { useDataSet } from 'utils/hooks';
import request from 'utils/request';
import { getCurrentUserId, getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { HZERO_PLATFORM } from 'utils/config';
import { HALM_ORI } from 'alm/utils/config';
import { setDSFields, parseFieldsHasData } from './dynamicFieldRender';
import styles from './index.module.less';
import getLangs from './Langs';
import { tableDS, saveDS } from './Stores';
import QueryBar from './QueryBar';
import Charts from './Charts';
import ReportModal from './ReportModal';
import SaveModal from './SaveModal';
import Color from './Color';

const tenantId = getCurrentOrganizationId();
const userId = getCurrentUserId();
// 指标lovCode
const dimLovCode = 'HALM.REPORT_TARGET';
// 初始化接口URL
const initUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports/init`;
// 资产报表列表URL
const reportListUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports/list`;
// 更多筛选-加载更多URL
// const moreUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports/filter/more`;
// 更多筛选-过滤名称
const findUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports/filter/find`;
// 保存接口URL
const saveUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports`;
// 维度值集URL
const targetUrl = `${HZERO_PLATFORM}/v1/${tenantId}/lovs/data?lovCode=${dimLovCode}`;
// 指标接口URL
const dimUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports/filter/more`;
// 资产列表接口
const assetUrl = `${HALM_ORI}/v1/${tenantId}/asset-reports/detail`;

interface Props {
  [key: string]: any;
}

const AssetReport: FC<Props> = props => {
  const { sourceType } = props;

  const tableDs = useDataSet(() => new DataSet(tableDS()));
  if (tableDs.queryDataSet) {
    tableDs.queryDataSet.addEventListener('update', () => {
      // 当前报表修改参数后就不是当前报表了s
      const { assetReportId, ...newReport } = currentReport;
      setCurrentReport({ ...newReport, noSearchFlag: 0 });
    });
    tableDs.queryDataSet.addEventListener('reset', () => {
      // 当前报表修改参数后就不是当前报表了s
      const { assetReportId, ...newReport } = currentReport;
      setCurrentReport({ ...newReport, noSearchFlag: 1 });
    });
  }
  const saveDs = useDataSet(() => new DataSet(saveDS()));

  const outRef = useRef(null);

  const [initLoading, setInitLoading] = useState<boolean>(false);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [searchMoreFlag, setSearchMoreFlag] = useState<boolean>(false);
  const [reportList, setReportList] = useState<any[]>([]);
  const [currentReport, setCurrentReport] = useState<any>({});
  // const [defaultReport, setDefaultReport] = useState<any>({});
  const [attrFields, setAttrFields] = useState<any>([]);
  const [queryDynamic, setQueryDynamic] = useState<any>({
    formDisplayRender: [],
    outputDisplayRender: [],
    fieldData: [],
    dsFields: [],
    resultData: [],
    tableDisplay: [],
  });
  const [targetValue, setTargetValue] = useState<string>('ORIGINAL_COST');
  const [targetData, setTargetData] = useState<any[]>([]);
  // const [lovCode, setLovCode] = useState<string>('AAFM.ASSET_CLASS');
  const [dimValue, setDimValue] = useState<string>('asset_set_id');
  const [dimData, setDimData] = useState<any[]>([]);
  const [dimWidth, setDimWidth] = useState<number>(80);
  const [columnName, setColumnName] = useState<string>('');
  // const [tableObject, setTableObject] = useState<any>({});
  const [assetData, setAssetData] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any[]>([]);

  useEffect(() => {
    init();
    getDimData();
    getTargetData();
  }, []);

  useEffect(() => {
    if (currentReport.noSearchFlag !== 1) {
      // 当前报表如果被修改会清除主键
      // 如果是手动更新查询条件  需要延迟1秒查询
      if (isUndefined(currentReport.assetReportId) && !isEmpty(currentReport)) {
        handleSearchAssetDelay(true);
      } else if (isEmpty(currentReport)) {
        handleSearchAsset(true);
      } else {
        handleSearchAsset(false);
      }
    }

    if (!isUndefined(currentReport.assetReportId)) {
      const currentRecord = dimData.find(i => i.fieldCode === currentReport.dimensionCode) || {};
      setColumnName(currentRecord.fieldName);
      setDimValue(currentReport.dimensionCode);
      setTargetValue(currentReport.targetCode);
      // eslint-disable-next-line no-unused-expressions
      tableDs.queryDataSet?.loadData([]);
      handleSetQueryParams(currentReport);
    }
  }, [currentReport, attrFields]);

  useEffect(() => {
    handleSetQueryParams(currentReport);
  }, [attrFields]);

  useEffect(() => {
    const dimRecord = dimData.find(i => i.fieldCode === dimValue);
    if (dimRecord && dimRecord.fieldName) {
      const width = dimRecord.fieldName.length * 12 + 40;
      setDimWidth(width);
    }
  }, [dimValue]);

  /**
   * 页面初始化
   */
  const init = async () => {
    setInitLoading(true);
    saveDs.reset();
    const result = await request(initUrl, {
      method: 'GET',
    });
    if (result) {
      if (result.reportObjList) {
        // 设置报表列表
        setReportList(result.reportObjList);
      }
      if (!isNull(result.defaultReport)) {
        // 设置当前报表
        setCurrentReport(result.defaultReport);
      }
      if (result.allField) {
        setAttrFields(result.allField);
        // 设置表格列名
        const field = result.allField.find(i => i.fieldCode === dimValue) || '';
        setColumnName(field.fieldName);
      }
      setInitLoading(false);
    }
  };

  const handleSetQueryParams = current => {
    const defaultFields: any = ['using_org_id', 'owning_org_id', 'asset_set_id'];
    let fields: any = [];
    if (!isEmpty(current) && !isNull(current)) {
      fields = current.preConditionList;
    } else {
      attrFields.forEach(i => {
        const index = defaultFields.findIndex(name => name === i.fieldCode);
        if (index >= 0) {
          fields[index] = i;
        }
      });
    }
    const handleOrgPartnerLovOk = (name, meaning, record) => {
      tableDs.queryDataSet?.current?.set(name, record.value);
      tableDs.queryDataSet?.current?.set(meaning, record.meaning);
    };
    const dynamic = setDSFields(fields, 'fieldName', 'fieldCode', 'typeCode', 'value', '', {
      handleOrgPartnerLovOk,
    });
    const queryData = tableDs.queryDataSet?.toData()[0];
    if (isUndefined(current.assetReportId)) {
      dynamic.resultData = queryData;
    } else {
      dynamic.resultData = { ...dynamic.resultData, ...queryData };
    }
    setQueryDynamic(dynamic);
  };

  const handleSearchAssetBash = useCallback(
    async flag => {
      let result: any = [];
      if (!isEmpty(attrFields)) {
        let queryParams = {};
        if (flag) {
          const currentRecord = attrFields && attrFields.find(i => i.fieldCode === dimValue);
          const list = parseFieldsHasData(
            attrFields,
            tableDs.queryDataSet?.toData()[0],
            'fieldCode'
          );
          console.log(attrFields);
          console.log(tableDs.queryDataSet?.toData()[0]);
          console.log(list);
          queryParams = {
            tenantId,
            lovCode: currentRecord.lovCode,
            preConditionList: list,
            typeCode: currentRecord.typeCode,
            scope: currentRecord.scope,
            sourceCode: currentRecord.sourceCode,
            enabledFlag: currentRecord.enabledFlag,
            sysSourceModule: currentRecord.sysSourceModule,
            valueField: currentRecord.valueField,
            displayField: currentRecord.sysSourceModule,
            processStrategy: currentRecord.processStrategy,
            dimensionCode: currentRecord.fieldCode,
            dimensionSourceCode: currentRecord.sourceCode,
            dimensionName: currentRecord.fieldName,
            targetCode: targetValue,
          };
        } else {
          const currentRecord =
            attrFields && attrFields.find(i => i.fieldCode === currentReport.dimensionCode);
          queryParams = {
            tenantId,
            lovCode: currentRecord.lovCode,
            preConditionList: currentReport.preConditionList || [],
            typeCode: currentRecord.typeCode,
            scope: currentRecord.scope,
            sourceCode: currentRecord.sourceCode,
            enabledFlag: currentRecord.enabledFlag,
            sysSourceModule: currentRecord.sysSourceModule,
            valueField: currentRecord.valueField,
            displayField: currentRecord.sysSourceModule,
            processStrategy: currentRecord.processStrategy,
            dimensionCode: currentRecord.fieldCode,
            dimensionSourceCode: currentRecord.sourceCode,
            dimensionName: currentRecord.fieldName,
            targetCode: currentReport.targetCode,
          };
        }
        result = await getAssetData(queryParams);
      }
      if (result) {
        // setTableObject(result);
        if (result.data) {
          const data = result.data || [];
          setAssetData(data);
          let newData: any = [];
          newData = handleAssetData(data, false);
          setChartData(newData);
          setSearchMoreFlag(false);
          tableDs.loadData(newData);
        }
      }
    },
    [attrFields, currentReport, dimValue, targetValue, searchMoreFlag]
  );

  const handleSearchAsset = debounce(flag => {
    handleSearchAssetBash(flag);
  }, 0);

  const handleSearchAssetDelay = debounce(flag => {
    handleSearchAssetBash(flag);
  }, 1000);

  const handleAssetData = useCallback(
    (data, flag) => {
      let newData: any = [];
      const moreData: any = [];
      let extValue = 0;
      let extPercent = 0;
      data.forEach((i, index) => {
        if (flag) {
          if (index > 9) {
            moreData.push({ ...i, color: index < 10 ? Color[index] : '#CBC4FF' });
            // eslint-disable-next-line operator-assignment
            extValue = extValue + i.targetValue;
            // eslint-disable-next-line operator-assignment
            extPercent = extPercent + Number(i.percent);
          } else {
            newData.push({ ...i, color: Color[index] });
          }
        } else if (index < 10) {
          newData.push({ ...i, color: Color[index] });
        } else {
          // eslint-disable-next-line operator-assignment
          extValue = extValue + i.targetValue;
          // eslint-disable-next-line operator-assignment
          extPercent = extPercent + Number(i.percent);
        }
      });
      if (data.length > 10) {
        newData.push({
          dimName: getLangs('OTHER'),
          targetValue: extValue.toFixed(targetValue === 'ORIGINAL_COST' ? 2 : 0),
          percent: extPercent.toFixed(2),
          color: '#CBC4FF',
        });
        newData = [...newData, ...moreData];
      }

      return newData;
    },
    [searchMoreFlag, targetValue]
  );

  /**
   * 查询报表列表
   */
  const handleSearchReport = () => {
    request(reportListUrl, {
      method: 'GET',
    })
      .then(result => {
        if (result) {
          setReportList(result);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  /**
   * 获取维度数据
   */
  const getDimData = async () => {
    const result = await request(dimUrl, {
      method: 'GET',
    });
    setDimData(result || []);
  };

  /**
   * 获取指标数据
   */
  const getTargetData = async () => {
    const result = await request(targetUrl, {
      method: 'GET',
    });
    setTargetData(result || []);
  };

  /**
   * 获取设备资产信息
   */
  const getAssetData = params => {
    setSearchLoading(true);
    return new Promise(resolve => {
      request(assetUrl, {
        method: 'POST',
        body: params,
      })
        .then(res => {
          resolve(res);
          setSearchLoading(false);
        })
        .catch(err => {
          console.log(err);
          setSearchLoading(false);
        });
    });
  };

  const handleChangeReport = val => {
    const item = reportList.find(i => val === i.assetReportId);
    setCurrentReport(item);
  };

  /**
   * 查看报表列表
   */
  const handleViewMyReport = useCallback(() => {
    const modalProps = {
      reportList,
      currentReport,
      setCurrentReport,
      setReportList,
    };
    Modal.open({
      key: 'myReport',
      closable: true,
      drawer: true,
      title: getLangs('MY_REPORT'),
      children: <ReportModal {...modalProps} />,
      footer: <></>,
    });
  }, [reportList, currentReport, Modal]);

  /**
   * 保存至我的报表
   */
  const handleOpenSaveModal = () => {
    const modalProps = {
      dataSet: saveDs,
    };
    Modal.open({
      key: 'saveReport',
      closable: true,
      title: getLangs('SAVE_MY_REPORT'),
      children: <SaveModal {...modalProps} />,
      onOk: handleSaveMyReport,
    });
  };

  const handleSaveMyReport = async () => {
    let current = {};
    if (saveDs.current) {
      current = saveDs.current.toData();
      const validate = await saveDs.current.validate();
      if (!validate) {
        notification.error({ message: getLangs('REQUIRED_NAME') });
        return false;
      }
    } else {
      notification.error({ message: getLangs('REQUIRED_NAME') });
      return false;
    }
    const {
      dynamicProps: { fieldData },
    } = outRef.current as any;
    const preConditionList = parseFieldsHasData(
      fieldData,
      tableDs.queryDataSet?.toData()[0],
      'fieldCode'
    );
    return new Promise(resolve => {
      request(saveUrl, {
        method: 'POST',
        body: {
          ...current,
          tenantId,
          userId,
          preConditionList,
          targetCode: targetValue,
          dimensionCode: dimValue,
        },
      }).then(res => {
        if (res && !res.failed) {
          setCurrentReport(res);
          handleSearchReport();
          resolve(res);
          saveDs.reset();
          notification.success({});
        } else {
          notification.warning({
            message: res.message,
          });
        }
      });
    });
  };

  const handleChangeDim = value => {
    const currentRecord = dimData.find(i => i.fieldCode === value) || {};
    setDimValue(value);
    setColumnName(currentRecord.fieldName);

    // 当前报表修改参数后就不是当前报表了
    const { assetReportId, ...newReport } = currentReport;
    setCurrentReport({ ...newReport, noSearchFlag: 0 });
  };

  const handleChangeTarget = value => {
    setTargetValue(value);

    // 当前报表修改参数后就不是当前报表了
    const { assetReportId, ...newReport } = currentReport;
    setCurrentReport({ ...newReport, noSearchFlag: 0 });
  };

  /**
   * 字段筛选名称
   */
  const handleSearchFieldsFilter = param => {
    return new Promise(resolve => {
      request(findUrl, {
        method: 'GET',
        query: {
          keyWords: param || '',
        },
      }).then(res => {
        if (res) {
          resolve(res);
        }
      });
    });
  };

  /**
   * 查看更多搜索结果/收起
   * @params flag {boolean}
   */
  const handleViewMoreAsset = useCallback(
    flag => {
      let newData = [];
      newData = handleAssetData(assetData, !flag);
      tableDs.loadData(newData);
      setSearchMoreFlag(!flag);
    },
    [chartData]
  );

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'color',
        width: 44,
        renderer: ({ value }) => (
          <span style={{ backgroundColor: value }} className={styles['color-point']} />
        ),
      },
      {
        name: 'dimName',
        title: columnName,
        renderer: ({ value }) =>
          getLangs('OTHER') === value ? (
            <div>
              <a onClick={() => handleViewMoreAsset(searchMoreFlag)}>{value}</a>
              {searchMoreFlag ? (
                <Icon type="keyboard_arrow_up" />
              ) : (
                <Icon type="keyboard_arrow_down" />
              )}
            </div>
          ) : (
            value
          ),
      },
      {
        name: 'targetValue',
        title: targetValue === 'ORIGINAL_COST' ? getLangs('ASSET_VALUE') : getLangs('ASSET_COUNT'),
      },
      {
        name: 'percent',
        renderer: ({ value }) => `${value}%`,
      },
    ],
    [columnName, targetValue, chartData, searchMoreFlag]
  );

  const searchMatcher = ({ text, record }) => {
    return record.get('meaning').toLowerCase().indexOf(text.toLowerCase()) !== -1;
  };

  const exportParams = {
    tenantId,
  };
  const cardMode = sourceType === '/workplace';
  const dropdownMenuStyle: CSSProperties = {
    // textAlign: 'right',
  };

  return (
    <>
      {!cardMode && (
        <Header title={getLangs('ASSET_REPORT')}>
          <ExcelExport
            method="GET"
            hiddenFillerType
            requestUrl={`${HALM_ORI}/v1/${tenantId}/asset-reports/export`}
            queryParams={exportParams}
          />
          <Button icon="save" onClick={handleOpenSaveModal}>
            {getLangs('SAVE_MY_REPORT')}
          </Button>
          <Button icon="settings-o" onClick={handleViewMyReport}>
            {getLangs('MY_REPORT')}
          </Button>
          <div className={styles['report-header']}>
            <Select
              name="report"
              clearButton={false}
              onChange={value => handleChangeReport(value)}
              value={currentReport.assetReportId || getLangs('SELECT_REQUIRED')}
              style={{ width: 300 }}
            >
              {reportList.map(i => {
                return (
                  <Select.Option value={i.assetReportId} key={`reportId-${i.assetReportId}`}>
                    {i.assetReportName}
                  </Select.Option>
                );
              })}
            </Select>
          </div>
        </Header>
      )}
      <Content>
        <div className={styles['report-body']}>
          {!cardMode && (
            <Skeleton loading={initLoading}>
              <div className={styles['query-bar']}>
                <QueryBar
                  ref={outRef}
                  dataSet={tableDs}
                  queryFields={queryDynamic.formDisplayRender}
                  queryDynamic={queryDynamic}
                  // @ts-ignore
                  queryDataSet={tableDs.queryDataSet}
                  queryFieldsLimit={3}
                  attrField={attrFields}
                  extMethod={{
                    setCurrentReport,
                    setDimValue,
                    setTargetValue,
                  }}
                  onSearch={handleSearchAsset}
                  onSearchFilter={handleSearchFieldsFilter}
                />
              </div>
            </Skeleton>
          )}
          <Spin spinning={searchLoading}>
            <div className={styles['data-display']}>
              <div className={styles.charts}>
                <div className={styles['dimension-code']}>
                  {cardMode ? (
                    <ExcelExport
                      requestUrl={`${HALM_ORI}/v1/${tenantId}/asset-reports/export`}
                      queryParams={exportParams}
                    />
                  ) : (
                    <>
                      <div className={styles['field-label']}>{getLangs('DIMENSION')}</div>
                      <Select
                        searchable
                        searchFieldInPopup
                        clearButton={false}
                        value={dimValue}
                        onChange={handleChangeDim}
                        searchMatcher={searchMatcher}
                        dropdownMatchSelectWidth={false}
                        style={{
                          width: dimWidth,
                        }}
                        dropdownMenuStyle={dropdownMenuStyle}
                      >
                        {dimData
                          .filter(i => i.typeCode !== 'TEXT_FIELD')
                          .map(i => (
                            <Select.Option value={i.fieldCode}>{i.fieldName}</Select.Option>
                          ))}
                      </Select>
                    </>
                  )}
                </div>
                <div className={styles['target-code']}>
                  {cardMode ? (
                    <div className={styles['card-mode']}>
                      <Select
                        clearButton={false}
                        value={currentReport.assetReportId}
                        onChange={handleChangeReport}
                        style={{
                          width: '15vw',
                        }}
                      >
                        {reportList.map(i => (
                          <Select.Option value={i.assetReportId}>{i.assetReportName}</Select.Option>
                        ))}
                      </Select>
                    </div>
                  ) : (
                    <>
                      <div className={styles['field-label']}>{getLangs('TARGET')}</div>
                      <Select clearButton={false} value={targetValue} onChange={handleChangeTarget}>
                        {targetData.map(i => (
                          <Select.Option value={i.value}>{i.meaning}</Select.Option>
                        ))}
                      </Select>
                    </>
                  )}
                </div>
                <Charts data={chartData} targetValue={targetValue} targetData={targetData} />
              </div>
              <div className={styles['asset-table']}>
                <Table
                  key="assetReportList"
                  customizedCode="AORI.ASSET_REPORT.LIST"
                  autoHeight={{ type: TableAutoHeightType.maxHeight, diff: 1 }}
                  selectionMode={SelectionMode.none}
                  queryFieldsLimit={3}
                  columns={columns}
                  dataSet={tableDs}
                  pagination={false}
                />
              </div>
            </div>
          </Spin>
        </div>
      </Content>
    </>
  );
};

export default formatterCollections({
  code: ['alm.common', 'alm.component', 'aori.assetReport'],
})(AssetReport);
