import { DataSetProps, DataToJSON } from 'choerodon-ui/pro/lib/data-set/interface';
import { FieldType, FieldIgnore, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { pick } from 'lodash';
import { HALM_MTC } from 'alm/utils/config';
// @ts-ignore
import { getCurrentOrganizationId } from 'utils/utils';
import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();

const tableDS = (): DataSetProps => ({
  primaryKey: 'alarmRecordId',
  autoQuery: false,
  cacheSelection: true,
  selection: DataSetSelection.multiple,
  queryFields: [
    {
      label: getLang('ALARM_RECORD_CODE'),
      name: 'alarmRecordCode',
      type: FieldType.string,
    },
    {
      label: getLang('ALARM_STATUS'),
      name: 'alarmStatusList',
      type: FieldType.string,
      lookupCode: 'HALM.ALARM_STATUS',
      defaultValue: ['01PENDING', '02PROCESSING'],
      multiple: ',',
    },
    {
      label: getLang('ALARM_SOURCE'),
      name: 'alarmSource',
      type: FieldType.string,
      lookupCode: 'HALM.ALARM_SOURCE',
      multiple: ',',
    },
    {
      label: getLang('BASIC_HANDLE_TYPE'),
      name: 'basicHandleTypeList',
      type: FieldType.string,
      lookupCode: 'HALM.ALARM_BASIC_HANDLE_TYPE',
      multiple: ',',
    },
    {
      label: getLang('HANDLE_TYPE'),
      name: 'handleTypeList',
      type: FieldType.string,
      lookupCode: 'HALM.ALARM_HANDLE_WAY',
      multiple: ',',
    },
    {
      label: getLang('ALARM_EVENT_CODE'),
      name: 'orderCode',
      type: FieldType.string,
    },
    {
      label: getLang('ASSET'),
      name: 'assetLov',
      type: FieldType.object,
      lovCode: 'AAFM.ASSET_RECEIPT',
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'assetIdList',
      type: FieldType.number,
      bind: 'assetLov.assetId',
      multiple: ',',
    },
    {
      label: getLang('LOCATION'),
      name: 'locationLov',
      type: FieldType.object,
      lovCode: 'AMDM.LOCATIONS',
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'locationIdList',
      type: FieldType.number,
      bind: 'locationLov.assetLocationId',
      multiple: ',',
    },
    {
      label: getLang('CREATION_DATE'),
      name: 'createTime',
      type: FieldType.dateTime,
      range: ['createStartTime', 'createEndTime'],
      ignore: FieldIgnore.always,
    },
    {
      name: 'createStartTime',
      type: FieldType.dateTime,
      bind: 'createTime.createStartTime',
    },
    {
      name: 'createEndTime',
      type: FieldType.dateTime,
      bind: 'createTime.createEndTime',
    },
    {
      label: getLang('HANDLE_DATE'),
      name: 'handleTime',
      type: FieldType.dateTime,
      range: ['handleStartTime', 'handleEndTime'],
      ignore: FieldIgnore.always,
    },
    {
      name: 'handleStartTime',
      type: FieldType.dateTime,
      bind: 'handleTime.handleStartTime',
    },
    {
      name: 'handleEndTime',
      type: FieldType.dateTime,
      bind: 'handleTime.handleEndTime',
    },
  ],
  fields: [
    {
      name: 'alarmRecordId',
      type: FieldType.number,
    },
    {
      label: getLang('ALARM_RECORD_CODE'),
      name: 'alarmRecordCode',
      type: FieldType.string,
    },
    {
      label: getLang('ALARM_STATUS'),
      name: 'alarmStatus',
      type: FieldType.string,
      lookupCode: 'HALM.ALARM_STATUS',
    },
    {
      name: 'sourceId',
      type: FieldType.number,
    },
    {
      label: getLang('SOURCE_NAME'),
      name: 'sourceName',
      type: FieldType.string,
    },
    {
      label: getLang('ALARM_SOURCE'),
      name: 'alarmSource',
      type: FieldType.string,
      lookupCode: 'HALM.ALARM_SOURCE',
    },
    {
      label: getLang('READING_VALUE'),
      name: 'readingRealValue',
      type: FieldType.string,
    },
    {
      name: 'uom',
      type: FieldType.string,
    },
    {
      name: 'locationId',
      type: FieldType.number,
    },
    {
      label: getLang('LOCATION'),
      name: 'locationName',
      type: FieldType.string,
    },
    {
      name: 'assetId',
      type: FieldType.number,
    },
    {
      label: getLang('ASSET'),
      name: 'assetName',
      type: FieldType.string,
    },
    {
      name: 'maintSiteId',
      type: FieldType.number,
    },
    {
      name: 'maintSiteName',
      type: FieldType.string,
    },
    {
      label: getLang('BASIC_HANDLE_TYPE'),
      name: 'basicHandleType',
      type: FieldType.string,
      lookupCode: 'HALM.ALARM_BASIC_HANDLE_TYPE',
    },
    {
      label: getLang('HANDLE_TYPE'),
      name: 'handleType',
      type: FieldType.string,
      lookupCode: 'HALM.ALARM_HANDLE_WAY',
    },
    {
      label: getLang('ALARM_EVENT_CODE'),
      name: 'orderCode',
      type: FieldType.string,
    },
    {
      label: getLang('ALARM_EVENT_CODE'),
      name: 'orderId',
      type: FieldType.number,
    },
    {
      label: getLang('HANDLER'),
      name: 'handlerName',
      type: FieldType.string,
    },
    {
      label: getLang('MEMO'),
      name: 'memo',
      type: FieldType.string,
    },
    {
      label: getLang('CREATION_DATE'),
      name: 'creationDate',
      type: FieldType.dateTime,
    },
    {
      label: getLang('HANDLE_DATE'),
      name: 'handleTime',
      type: FieldType.dateTime,
    },
  ],
  transport: {
    read: () => {
      const url = `${HALM_MTC}/v1/${organizationId}/alarm-records/list`;
      return {
        url,
        method: 'GET',
      };
    },
  },
  events: {
    load: ({ dataSet }) => {
      dataSet.forEach(record => {
        if (record.get('alarmStatus') !== '01PENDING') {
          Object.assign(record, { selectable: false });
        }
      });
    },
  },
});

const operationDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  dataToJSON: DataToJSON.all,
  fields: [
    {
      name: 'alarmRecordIdList',
      type: FieldType.number,
      multiple: true,
    },
    {
      name: 'operateCode',
      type: FieldType.string,
    },
    {
      name: 'msgObjList',
      label: getLang('MSG_OBJ'),
      type: FieldType.string,
      lookupCode: 'HALM.ALARM_MSG_SET',
      multiple: true,
      dynamicProps: {
        required: ({ record }) => record.get('operateCode') === 'BATCH_MSG_SEND',
      },
    },
    {
      name: 'msgPositionList',
      label: getLang('POSITION'),
      type: FieldType.object,
      lovCode: 'LOV_POSITION',
      multiple: true,
    },
    {
      name: 'msgEmployeeList',
      label: getLang('EMPLOYEE'),
      type: FieldType.object,
      lovCode: 'HALM.EMPLOYEE',
      lovPara: { tenantId: organizationId },
      multiple: true,
    },
    {
      name: 'messageLov',
      label: getLang('MSG_CONFIG'),
      type: FieldType.object,
      lovCode: 'ALM.MSG_TEMP_SERVER',
      valueField: 'messageCode',
      textField: 'messageName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record }) => record.get('operateCode') === 'BATCH_MSG_SEND',
      },
    },
    {
      name: 'messageName',
      type: FieldType.string,
      bind: 'messageLov.messageName',
    },
    {
      name: 'messageCode',
      type: FieldType.string,
      bind: 'messageLov.messageCode',
    },
    {
      name: 'memo',
      label: getLang('MEMO'),
      type: FieldType.string,
      maxLength: 240,
    },
  ],
  transport: {
    submit: ({ data }) => {
      const url = `${HALM_MTC}/v1/${organizationId}/alarm-records/operate`;
      let newData: object = {};
      const { operateCode, alarmRecordIdList } = data[0];
      newData = {
        ...newData,
        operateCode,
        alarmRecordIdList,
      };
      if (operateCode === 'BATCH_MSG_SEND') {
        const msgSendDTO = {
          msgObjList: data[0].msgObjList,
          msgPositionList: data[0].msgPositionList.map((i: any) =>
            pick(i, ['positionId', 'positionName', 'unitId'])
          ),
          msgEmployeeList: data[0].msgEmployeeList.map((i: any) =>
            pick(i, ['employeeId', 'employeeName'])
          ),
          messageCode: data[0].messageCode,
          messageName: data[0].messageName,
        };
        newData = {
          ...newData,
          msgSendDTO,
        };
      } else {
        newData = {
          ...newData,
          memo: data[0].memo,
        };
      }
      return {
        url,
        data: newData,
        method: 'POST',
      };
    },
  },
});
export { tableDS, operationDS };
