import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  forwardRef,
  useImperativeHandle,
  createRef,
} from 'react';
import { isUndefined } from 'lodash';
import { getActiveTabKey, menuTabEventManager } from 'utils/menuTab';
import { Form, Button, DataSet, Dropdown, Icon } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Action } from 'choerodon-ui/pro/lib/trigger/enum';
import { setSession, getSession, getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { setDSFields, parseFieldsHasData } from 'alm/utils/dynamicFieldRender';
import { HALM_ORI } from 'alm/utils/config';
import { DynamicProps, FormDisplayField } from './store';
import FieldList from './FieldList';
import getLangs from './Langs';

const tenantId = getCurrentOrganizationId();
// 查询动态字段列表URL
const attrUrl = `${HALM_ORI}/v1/${tenantId}/field-defs/query-field`;

interface Props {
  queryFields: any[];
  moduleName: string;
  defaultQueryFields: any[];
  systemFields: string[];
  queryDataSet: DataSet;
  queryFieldsLimit: number;
  dataSet: DataSet;
  attrField: any[];
  queryDynamic: any;
  buttons: object[];
  defaultShowMore: boolean;
  extQueryParams: any;
}

function useClickOut(onClickOut) {
  const ref = useRef<HTMLInputElement>(null);
  const handleClick = useCallback(
    e => {
      if (ref.current && !ref.current.contains(e.target)) {
        onClickOut(e);
      }
    },
    [onClickOut],
  );
  useEffect(() => {
    document.addEventListener('click', handleClick);
    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, [handleClick]);
  return ref;
}

const QueryBar: React.FC<Props> = forwardRef((props, outRef) => {
  const {
    moduleName = 'normal',
    dataSet,
    queryDataSet,
    queryFieldsLimit = 3,
    defaultQueryFields = [],
    extQueryParams,
  } = props;

  // 设置唯一的sessionKey用于前端针对每个菜单生成唯一的缓存，因为可能多菜单对应一个查询字段集模板
  // 同时每个菜单生成一个session存储自己的数据，因为之前的customFilterFields是直接重新变更对象，没保留别的缓存
  const sessionKey = getActiveTabKey();

  const handleClickOut = useCallback(() => {
    setHidden(true);
  }, []);

  const ref = useClickOut(handleClickOut);
  const fieldRef: any = createRef();

  const [hidden, setHidden] = useState(true);
  const [attrField, setAttrField] = useState<any>([]);
  // 接口配置的默认显示的字段
  const [defShowAttrFields, setDefShowAttrFields] = useState<any>([]);
  const [defaultFields, setDefaultFields] = useState<any>([]);
  const [newQueryFields, setNewQueryFields] = useState<any[]>([]);
  const [savedData, setSavedData] = useState(new Map());
  const [dynamicProps, setDynamicProps] = useState<DynamicProps>({
    formDisplayRender: [],
    outputDisplayRender: [],
    fieldData: [],
    dsFields: [],
    resultData: [],
    tableDisplay: [],
  });

  const handleClearSessionOnClose = () => {
    // 添加监听关闭tab菜单时清除筛选条件缓存
    const tabsKey = getActiveTabKey();
    if (tabsKey) {
      const listener = ({ tabKey }) => {
        if (tabKey === tabsKey) {
          setSession(sessionKey, null);
          menuTabEventManager.off('close', listener);
        }
      };
      menuTabEventManager.on('close', listener);
    }
  };

  useEffect(() => {
    const handleInit = async () => {
      const tmp: any = await handleSearchAttrFields('');
      const fields = defaultFlagFields(tmp);
      setDefShowAttrFields(fields);
      setDefaultFields(tmp.filter(i => i.defaultFlag));
    };
    handleInit();
    handleClearSessionOnClose();
  }, []);

  // 确保组件初始化后查询时已取得attrField，避免查询参数无效
  useEffect(() => {
    if (attrField.length !== 0 && !attrField.some((item) => item.requiredFlag === 1)) {
      handleSearch(attrField, true);
    }
  }, [attrField]);

  const handleQuery = useCallback(() => {
    handleSearch(attrField);
    if (fieldRef && fieldRef.current) {
      const { handleSaveFilter } = fieldRef.current;
      handleSaveFilter();
    } else {
      handleSaveFilterSystem();
    }
  }, [fieldRef]);

  const handleReset = useCallback(() => {
    queryDataSet.reset();
    queryDataSet.loadData([]);
    handleSearch([]);
    setSession(sessionKey, {});
  }, [queryDataSet]);

  useImperativeHandle(
    outRef,
    () => {
      return {
        dynamicProps,
      };
    },
    [dynamicProps],
  );

  useEffect(() => {
    const handleOrgPartnerLovOk = (name, meaning, record) => {
      queryDataSet?.current?.set(name, record.value);
      queryDataSet?.current?.set(meaning, record.meaning);
    };
    const dynamic = setDSFields(
      attrField,
      'fieldName',
      'fieldCode',
      'typeCode',
      'value',
      '',
      'search',
      undefined,
      undefined,
      {
        handleOrgPartnerLovOk,
      },
    );
    const { dsFields } = dynamic;
    setDynamicProps(dynamic);
    dsFields.forEach(field => {
      const t = { ...field };
      // 开关字段默认为否
      if (field.type === 'number' && field.trueValue === 1 && field.falseValue === 0) {
        t.defaultValue = 0;
      }
      queryDataSet.addField(field.name, t);
    });
  }, [attrField]);

  useEffect(() => {
    // [TODO]: 等待后端加接口
    const customFilterFields = getSession(sessionKey);
    const newMap = new Map(customFilterFields?.chosenFields || []);
    const systemObject = customFilterFields?.systemObject || {};
    handleClickField({
      chosenFields: newMap,
      checkAll: undefined,
    });
    setSavedData(newMap);
    const fields: any = [];
    newMap.forEach(value => {
      fields.push(value);
    });
    const dynamic = setDSFields(
      fields,
      'fieldName',
      'fieldCode',
      'typeCode',
      'value',
      '',
      'search',
    );
    const { resultData } = dynamic;
    queryDataSet.loadData([{ ...systemObject, ...resultData }]);
  }, [dynamicProps]);

  const handleSearchAttrFields = fieldName => {
    return new Promise(async resolve => {
      const result =
        (await request(attrUrl, {
          method: 'GET',
          query: {
            tenantId,
            fieldName,
            scope: moduleName,
          },
        })) || [];
      const { allField = [] } = result;
      allField.forEach((item) => {
        if(item.fieldCode === 'asset_name'){
          item.defaultFlag = 1
        }
        if(item.fieldCode === 'asset_location_id'){
          item.defaultFlag = 1
          item.requiredFlag = 1
        }
      })
      // console.log('allField', allField);

      setAttrField(allField);
      resolve(allField);
    });
  };

  const handleSearch = async (fields?: any, initFlag?: boolean) => {
    const queryData = (queryDataSet && queryDataSet.toData()[0]) || {};
    const customFilterFields = getSession(sessionKey) || {};
    const { chosenFields = [] } = customFilterFields;
    const newMap = new Map(chosenFields || []);
    let preConditionList: any = [];
    if (fields) {
      const searchFields: any = [];
      const queryFieldsArr: any = [...defaultQueryFields, ...defShowAttrFields, ...newQueryFields];
      queryFieldsArr.forEach(i => searchFields.push(i.key));
      const newQueryData = {};
      // eslint-disable-next-line no-return-assign
      searchFields.forEach(i => {
        newQueryData[i] = queryData[i];
        if (i !== null && i.indexOf('Lov') !== -1) {
          newQueryData[i.split('Lov')[0]] = queryData[i.split('Lov')[0]];
          newQueryData[`${i.split('Lov')[0]}Meaning`] = queryData[`${i.split('Lov')[0]}Meaning`];
        }
      });
      preConditionList = parseFieldsHasData(
        [...defaultFields, ...fields],
        newQueryData,
        'fieldCode',
      );
    }
    if (initFlag) {
      newMap.forEach(value => {
        preConditionList.push(value);
      });
    }
    dataSet.setState('preConditionList', preConditionList);
    if (extQueryParams) {
      if (extQueryParams.basicCode) {
        dataSet.setState('basicCode', extQueryParams.basicCode);
      }
    }
    await dataSet.query();
  };

  const handleClickField = ({ chosenFields, checkAll }) => {
    const { formDisplayRender } = dynamicProps;
    const chosenDisplay: FormDisplayField[] = [];
    if (isUndefined(checkAll)) {
      formDisplayRender.forEach(i => {
        if (
          (chosenFields as Map<string, object>).has(i.key) ||
          (chosenFields as Map<string, object>).has(i.key.split('Lov')[0])
        ) {
          chosenDisplay.push(i);
        }
      });
      const selectedArr: any = [];
      chosenDisplay.forEach(i => {
        const temp = newQueryFields.find(c => i.key === c.key);
        if (temp !== undefined) {
          selectedArr.push(temp);
        } else {
          selectedArr.push(i);
        }
      });
      setNewQueryFields([...selectedArr]);
    } else if (checkAll) {
      setNewQueryFields([...formDisplayRender]);
    } else {
      setNewQueryFields([]);
    }
  };

  const handleSaveFilterSystem = () => {
    const queryData = (queryDataSet && queryDataSet.toData()[0]) || {};
    const systemObject = {};
    const systemFlagFields: any = [];
    attrField.filter(i => i.defaultFlag).forEach(i => systemFlagFields.push(i.fieldCode));
    // eslint-disable-next-line no-return-assign
    systemFlagFields.forEach(i => (systemObject[i] = queryData[i]));
    const customFilterFields = {
      systemObject,
    };
    const oldCustomFilterFields = getSession(sessionKey) || {};
    setSession(sessionKey, { ...oldCustomFilterFields, ...customFilterFields });
  };

  const defaultFlagFields = attrFields => {
    const fields = attrFields.filter(i => i.defaultFlag);
    const dynamic = setDSFields(
      fields,
      'fieldName',
      'fieldCode',
      'typeCode',
      'value',
      '',
      'search',
    );
    return dynamic.formDisplayRender || [];
  };

  if (queryDataSet) {
    const systemFlagFields: any = [];
    attrField.filter(i => i.defaultFlag).forEach(i => systemFlagFields.push(i.fieldCode));

    const fieldListProps = {
      sessionKey,
      moduleName,
      savedData,
      queryDataSet,
      fields: attrField,
      defaultFields,
      setHidden,
      onSearchFilter: handleSearchAttrFields,
      handleClickField,
    };
    return (
      <div className="c7n-pro-table-professional-query-bar">
        <Form columns={queryFieldsLimit} dataSet={queryDataSet}>
          {defaultQueryFields}
          {defShowAttrFields}
          {newQueryFields}
        </Form>
        <span className="c7n-pro-table-professional-query-bar-button">
          <Dropdown
            hidden={hidden}
            trigger={[Action.click]}
            overlay={
              <div
                ref={ref}
                onMouseDown={e => {
                  e.stopPropagation();
                }}
              >
                <FieldList ref={fieldRef} {...fieldListProps} />
              </div>
            }
          >
            <Button
              onClick={e => {
                e.nativeEvent.stopImmediatePropagation();
                setHidden(!hidden);
              }}
            >
              {getLangs('VIEWMORE')}
              <Icon style={{ fontSize: '.12rem' }} type="arrow_drop_down" />
            </Button>
          </Dropdown>
          <Button onClick={handleReset}>{getLangs('RESET')}</Button>
          <Button onClick={() => handleQuery()} color={ButtonColor.primary}>
            {getLangs('SEARCH')}
          </Button>
        </span>
      </div>
    );
  }
  return null;
});

export default QueryBar;
