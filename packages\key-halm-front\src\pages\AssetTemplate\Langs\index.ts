import intl from 'utils/intl';
import getCommonLangs from 'alm/langs';
import getPageConfigLang from 'alm/components/PageConfiguration/Langs';

const getLang = key => {
  const PREFIX = 'aori.assetTemplate';
  const MODEL_PREFIX = 'aori.assetTemplate.model.assetTemplate';
  const LANGS = {
    PREFIX,
    ...getPageConfigLang(),
    ...getCommonLangs(),
    // title
    TITLE: intl.get(`${PREFIX}.title.assetTemplate`).d('资产模板'),
    KEYWORD_PLACEHOLDER: intl.get(`${PREFIX}.title.keyWordPlaceholder`).d('请输入模板编码、名称'),

    SELECTED_AREA: intl.get(`${PREFIX}.title.selectArea`).d('选择区域'),
    SELECTED_FIELD: intl.get(`${PREFIX}.title.selectField`).d('选择字段'),
    SCOPE_KEYWORD_PLACEHOLDER: intl
      .get(`${PREFIX}.title.scopeKeyWordPlaceholder`)
      .d('请输入对象编码、名称'),
    RESTORE_CONFIRM: intl.get(`${PREFIX}.msg.restoreConfirm`).d('恢复后将变为通用模板，确认恢复？'),
    SCOPE: intl.get(`${PREFIX}.collapse.scope`).d('适用范围'),
    RESTORE_DEFAULT: intl.get(`${PREFIX}.btn.restoreDefault`).d('恢复默认'),
    COPY_CREATE: intl.get(`${PREFIX}.btn.copyCreate`).d('复制创建'),
    AREA_ADJUST: intl.get(`${PREFIX}.btn.areaAdjust`).d('区域调整'),
    RENAME: intl.get(`${PREFIX}.btn.rename`).d('重新命名'),
    CREATE_AREA: intl.get(`${PREFIX}.btn.createArea`).d('新建区域'),
    MOVE_FIELD: intl.get(`${PREFIX}.btn.moveField`).d('字段移动'),

    TEMPLATE_CODE: intl.get(`${MODEL_PREFIX}.templateCode`).d('模板编码'),
    TEMPLATE_NAME: intl.get(`${MODEL_PREFIX}.templateName`).d('模板名称'),
    SCOPE_OBJECT: intl.get(`${MODEL_PREFIX}.scopeObject`).d('适用对象'),
    EFFECTIVE_OBJECT: intl.get(`${MODEL_PREFIX}.effectiveObject`).d('生效对象'),
    SCOPE_OBJECT_TYPE: intl.get(`${MODEL_PREFIX}.scopeObjectType`).d('适用对象类型'),
    OBJECT_CODE: intl.get(`${MODEL_PREFIX}.objectCode`).d('对象编码'),
    OBJECT_TYPE: intl.get(`${MODEL_PREFIX}.objectType`).d('对象类型'),
    OBJECT_NAME: intl.get(`${MODEL_PREFIX}.objectName`).d('对象名称'),

    LINE_NUM: intl.get(`${MODEL_PREFIX}.lineNumber`).d('行号'),
  };
  return LANGS[key];
};

export default getLang;
