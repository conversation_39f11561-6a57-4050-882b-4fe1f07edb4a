import React, { useMemo, useEffect, forwardRef, useImperativeHandle } from 'react';
import { omit } from 'lodash';
import { Button, Table, DataSet, Lov, Modal } from 'choerodon-ui/pro';
import { TableColumnTooltip, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { yesOrNoRender } from 'utils/renderer';

import WoopModal from 'alm/pages/Act/Detail/Woop/WoopModal';
import { woopDs } from 'alm/pages/Act/Stores/woopDs';
import notification from 'utils/notification';

import getLang from '../Langs';
import { tableDs as _tableDs, lovBtnDS as _lovBtnDS } from '../Stores/stepDs';
import { createFromAct } from '../api';

interface IModalProps {
  isNew: boolean;
  isEdit: boolean;
  header: object;
  record: object;
  isFirstWoop: boolean;
  headerId: number;
  headerTypeCode: string;
  woopDs: DataSet;
}

function Step(props, ref) {
  const { header } = props;
  const { maintSiteId, assetId, locationId, maintainPlanId } = header;
  const tableDs = useMemo(() => new DataSet(_tableDs()), []);
  const lovBtnDS = useMemo(
    () => new DataSet(_lovBtnDS({ maintSiteId, assetId, assetLocationId: locationId })),
    [maintSiteId, assetId, locationId]
  );

  useImperativeHandle(ref, () => ({
    hasStep: () => tableDs.records.length > 0,
    refresh: () => {
      tableDs.query();
    },
  }));

  useEffect(() => {
    if (maintainPlanId) {
      tableDs.setQueryParameter('headerId', maintainPlanId);
      tableDs.setQueryParameter('headerTypeCode', 'MAINTAIN_PLAN');
      tableDs.query();
    }
  }, [maintainPlanId]);

  const handleModalOpen = (isNew = false, isEdit = false, record) => {
    const woopLen = tableDs.records.length;
    const firstWoop = woopLen > 0 ? tableDs.records[0].toData() : {};

    const modalProps: IModalProps = {
      isNew,
      isEdit,
      record,
      header,
      headerId: maintainPlanId,
      headerTypeCode: 'MAINTAIN_PLAN',
      isFirstWoop: (isNew && woopLen === 0) || (!isNew && record.actOpId === firstWoop.actOpId), // 是否为首道工序：新增时，woopLen为0 或者 编辑时 actOpId === firstWoop.actOpId
      woopDs: new DataSet(woopDs({ headerTypeCode: 'MAINTAIN_PLAN' })),
    };
    handleOpen(modalProps);
  };

  const handleOpen = modalProps => {
    Modal.open({
      key: 'maintain_plan_step',
      destroyOnClose: true,
      closable: true,
      drawer: true,
      style: {
        width: 450,
      },
      title: getLang('TAB_STEP'),
      children: <WoopModal {...modalProps} />,
      onOk: () => handleModalOk(modalProps.woopDs, modalProps.isEdit, modalProps.isFirstWoop),
    });
  };

  const handleModalOk = async (ds, isEdit, isFirstWoop) => {
    if (await ds.current.validate(true)) {
      try {
        const res = await ds.submit();
        if (res && res.success) {
          tableDs.query();
          // 刷新物料 检查项
          if (isEdit) {
            // eslint-disable-next-line
            props.otherRef.materialRef?.refresh();
            // eslint-disable-next-line
            props.otherRef.checkRef?.refresh();
          }
        }
      } catch (error) {
        return false;
      }
      if (props.headEditFlag && isFirstWoop) {
        props?.detailDs?.current.set('enabledFlag', 1);
      }
    } else {
      return false;
    }
  };

  const handleCreate = () => handleModalOpen(true, false, null);

  const handleEdit = record => handleModalOpen(false, true, record);

  const handleCreateFromAct = record => {
    // 三个行表都需要刷新
    createFromAct({
      maintainPlanId,
      actId: record.actId,
    }).then(res => {
      if (res && !res.failed) {
        if (props.headEditFlag && tableDs.records.length === 0) {
          props?.detailDs?.current.set('enabledFlag', 1);
        }
        notification.success({});
        tableDs.query();
        // eslint-disable-next-line
        props.otherRef.materialRef?.refresh();
        // eslint-disable-next-line
        props.otherRef.checkRef?.refresh();
      }
      // 清空当前dataset的值 以便重复选择当前值时可以触发
      lovBtnDS.current?.reset(); // eslint-disable-line
    });
  };

  const handleDelete = record => {
    // 删除的是最后一条行，则先提示“维保计划需至少具备一条维保步骤才可进行后续维保计划预测，确认删除？”
    if (tableDs.totalCount === 1) {
      Modal.confirm({
        key: Modal.key(),
        title: getLang('DEL_CONFIRM'),
        children: getLang('DEL_LAST_STEP'),
        onOk: () => {
          confirmDelete(record, true);
        },
      });
    } else {
      confirmDelete(record, false);
    }
  };

  const confirmDelete = (record, isLastOne) => {
    // 点击后二次确认，“删除维保步骤行时会同步删除所关联物料及检查项信息”，
    // 点击确认，会删除当前任务及相关物料信息及检查项信息，点取消则不删除。
    Modal.confirm({
      key: Modal.key(),
      title: getLang('DEL_CONFIRM'),
      children: getLang('DEL_STEP'),
      onOk: async () => {
        const res = await tableDs.delete(record, false);

        if (res && res.success) {
          // 删除最后一个检查项后，启用设为否
          if (isLastOne) {
            const tempData = props.detailDs.current.toData();
            props.detailDs.query().then(resData => {
              props.detailDs.current.set({
                ...resData,
                ...omit(tempData, ['objectVersionNumber', 'enabledFlag']), // 保证用户的头更改仍在
              });
            });
          }
          // 刷新物料 检查项
          // eslint-disable-next-line
          props.otherRef.materialRef?.refresh();
          // eslint-disable-next-line
          props.otherRef.checkRef?.refresh();
        }
      },
    });
  };

  const columns: ColumnProps[] = useMemo(() => {
    const cols: ColumnProps[] = [
      {
        name: 'activityOpNumber',
        width: 90,
      },
      {
        name: 'actOpName',
        tooltip: TableColumnTooltip.overflow,
        width: 200,
      },
      {
        name: 'description',
        tooltip: TableColumnTooltip.overflow,
        renderer: ({ value }) => value ?? '-',
      },
      {
        name: 'defaultJobCode',
        width: 150,
      },
      {
        name: 'ownerGroupName',
        width: 120,
        renderer: ({ value }) => value ?? '-',
      },
      {
        name: 'ownerName',
        width: 120,
        renderer: ({ value }) => value ?? '-',
      },
      {
        name: 'standardHour',
        tooltip: TableColumnTooltip.overflow,
        width: 120,
        renderer: ({ value }) => value ?? '-',
        hidden: true,
      },
      {
        name: 'durationUomCodeMeaning',
        tooltip: TableColumnTooltip.overflow,
        hidden: true,
      },
      {
        name: 'importance',
        width: 100,
      },
      {
        header: getLang('OPTION'),
        width: 100,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          const data = record?.toData();
          return (
            <span className="action-link">
              <a onClick={() => handleEdit(data)}>{getLang('EDIT')}</a>
              <a onClick={() => handleDelete(record)}>{getLang('DELETE')}</a>
            </span>
          );
        },
      },
    ];
    if (header?.ifShowSignFlag) {
      // 放在描述的前面，hidden隐藏时也会在表格个性化配置中显示
      cols.splice(2, 0, {
        name: 'needSignFlag',
        renderer: ({ value }) => yesOrNoRender(Number(value)),
      });
    }
    return cols;
  }, [header]);

  return (
    <>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Lov
          noCache
          dataSet={lovBtnDS}
          name="actLov"
          mode={ViewMode.button}
          autoSelectSingle={false}
          clearButton={false}
          icon="add"
          onChange={handleCreateFromAct}
        >
          {getLang('CREATE_FROM_ACT')}
        </Lov>
        <Button icon="add" onClick={handleCreate} color={ButtonColor.primary}>
          {getLang('CREATE')}
        </Button>
      </div>
      <Table
        key="maintainPlanStep"
        dataSet={tableDs}
        columns={columns}
        customizable
        customizedCode="AORI.MAINTAINPLAN.STEP"
      />
    </>
  );
}

export default forwardRef(Step);
