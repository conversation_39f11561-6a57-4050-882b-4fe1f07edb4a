.center-loading {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
}

.screen-container {
  width: 100%;
  height: 100%;

  :global {
    .page-content-wrap {
      height: 100%;
      margin: 0;
    }

    .page-content {
      height: 100%;
      margin: 0;
      .c7n-spin-nested-loading,
      .c7n-spin-container {
        height: 100%;
      }
    }
  }
}

.dashboard-container {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
  // padding: 20px 36px;
  overflow: hidden;
  color: #fff;
  background: url('../../assets/equipmentBoard/bg.png') center center no-repeat;
  background-position: top;
  background-size: cover;
}

.dashboard-title {
  display: flex;
  width: 100%;
  height: 80px;
  height: 10vh;
  min-height: 80px;
  .dashboard-title-left {
    display: grid;
    width: 30%;    
    height: 10%;
  }

  .dashboard-title-center {
    display: flex;
    flex-grow: 0;
    align-items: center;
    justify-content: center;
    width: 40%;
    height: 70%;
    color: #65ffff;
    font-weight: bold;
    font-size: 32px;
    letter-spacing: 0.1em;
  }

  .dashboard-title-right {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    align-items: flex-end;
    justify-content: right;
    width: 30%;
    height: 10%;
    // margin: 38px 10px;
    margin: 3% 1%;
    :global {
      .c7n-pro-field-label label {
        color: #fff;
      }
      .c7n-pro-output.c7n-pro-output,
      .c7n-pro-select-wrapper.c7n-pro-select-wrapper label input,
      .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-focused .c7n-pro-select {
        color: #65ffff;
      }
    }
  }
}

.my-chart-form {
  padding: 5px 10px 5px 10px;
  :global {
    .c7n-pro-field-label label {
      color: #fff;
    }
    .c7n-pro-output.c7n-pro-output,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label input,
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-focused .c7n-pro-select {
      color: #65ffff;
    }
    .c7n-pro-field-label.c7n-pro-field-label.c7n-pro-field-label,
    .c7n-pro-field-label.c7n-pro-field-label label > span,
    .c7n-pro-output.c7n-pro-output {
      line-height: 20px;
    }
  }
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-wrap: wrap;
  width: 100%;
  height: calc(100% - 10vh);

  .dashboard-left-side {
    width: 66%;
    height: 100%;
  }

  .dashboard-right-side {
    width: 32%;
    height: 100%;
  }

  .dashboard-item-content {
    padding: 5px 10px 10px 5px;
  }
  .dashboard-item-left-bottom {
    width: 100%;
    height: 60%;
    // margin: 1% 0;
    // background-image: url('./assets/task.png');
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
  }
  .dashboard-item-right-bottom {
    width: 100%;
    height: 49%;
    margin: 2% 0;
    background-image: url('../../assets/equipmentBoard/task.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.dashboard-card {
  width: 100%;
  height: 100%;
  padding: 5px;
  overflow: hidden;
  background-image: url('../../assets/equipmentBoard/left.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  // border: 1px solid red;
  .dashboard-card-title {
    width: 100%;
  }

  .dashboard-card-content {
    width: 100%;
    height: 100%;
  }
}
.my-top-chart{
  width: 100%;
  height: 100%;
  margin-top: 1px;
  display: flex;
  flex-direction: column;
  :global {
    .c7n-pro-pagination {
      display: flex;
      justify-content: end;
    }
    .c7n-pro-pagination.c7n-pro-pagination-wrapper .c7n-pro-pagination-page-info {
      color: #65ffff;
    }
    .c7n-pro-pagination.c7n-pro-pagination-wrapper .c7n-pro-btn.c7n-pro-btn-flat.c7n-pro-btn-wrapper.c7n-pro-pagination-pager.c7n-pro-btn-disabled {
      background: rgba(0, 0, 0, 0.2);
    }
    .c7n-pro-pagination.c7n-pro-pagination-wrapper .c7n-pro-btn.c7n-pro-btn-flat.c7n-pro-btn-wrapper.c7n-pro-pagination-pager {
      background: rgba(245, 245, 245, 0.2);
      color: #fff;
    }
    .c7n-pro-pagination.c7n-pro-pagination-wrapper .c7n-pro-btn.c7n-pro-btn-flat.c7n-pro-btn-wrapper.c7n-pro-pagination-pager i.icon::before {
      color: #fff;
    }
  }
  
}
.my-bottom-chart{
  width: 50%;
  height: 83%;
  background-image: url('../../assets/equipmentBoard/chart.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 1%;
  margin-top: 1%;
  padding-top: 1%;
}
.my-bottom-center-chart{
  width: 50%;
  height: 83%;
  background-image: url('../../assets/equipmentBoard/chart.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-top: 1%;
  padding-top: 1%;
}
.my-bottom-right-chart{
  width: 100%;
  height: 100%;
  margin-top: 1%;
  padding-top: 1%;
}
.my-bottom-chart-title{
  width: 100%;
  height: 7%;
  opacity: 1;
  /** 文本1 */
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 28.96px;
  color: rgba(0, 255, 244, 1);
  text-align: center;
  vertical-align: top;
  margin-bottom: 2%;
  .tab-super-count {
    vertical-align: super;
  }
}

.container-inventory-select {
  display: flex;
  // top: 1.5px;
  // right: 10px;
  cursor: pointer;

  :global {
    .c7n-pro-calendar-picker-wrapper {
      background: transparent !important;
    }
    .c7n-pro-calendar-picker-suffix .icon-date_range:before {
      color: #33c5ff !important;
    }
    .c7n-pro-calendar-picker {
      color: #33c5ff !important;
      width: 100px !important;
      height: 35px !important;
    }
    .c7n-pro-select-wrapper {
      width: 100px !important;
      height: 35px !important;
      background: transparent !important;
      border: none !important;
      cursor: pointer !important;
    }

    .c7n-pro-select {
      height: 35px !important;
      color: #33c5ff !important;
      font-weight: 600 !important;
      // font-size: 18px !important;
      line-height: 35px !important;
      //outline: none !important;
    }

    .c7n-pro-select-suffix .icon-baseline-arrow_drop_down:before {
      color: #33c5ff !important;
    }

    .c7n-pro-select-multiple-block {
      background: #154ea0;
    }

    .c7n-pro-select-placeholder {
      color: #3ac2fc !important;
    }

    input::-webkit-input-placeholder {
      padding-left: 10px !important;
      /* placeholder颜色 */
      color: #33c5ff !important;
      font-weight: 600 !important;
      /* placeholder字体大小 */
      font-size: 18px !important;
      line-height: 35px !important;
    }
  }
}

.top-select {
  width: 300px;
  height: 45px !important;

  :global {
    .c7n-pro-select-wrapper {
      width: 200px;
      height: 40px !important;
      background: transparent !important;
      background: linear-gradient(to right, #01427c, #000e27) !important;
      border: 1px solid #6ab5f8 !important;
    }

    .c7n-pro-select {
      height: 40px !important;
      color: #33c5ff !important;
      font-weight: 600 !important;
      // font-size: 18px !important;
      line-height: 40px !important;
      outline: none !important;
    }

    .c7n-pro-select:focus {
      border: none !important;
      box-shadow: none !important;
    }

    .c7n-pro-select:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .c7n-pro-select-multiple-block {
      background: #154ea0;
    }

    input::-webkit-input-placeholder {
      padding-left: 10px !important;
      /* placeholder颜色 */
      color: #33c5ff !important;
      font-weight: 600 !important;
      /* placeholder字体大小 */
      font-size: 18px !important;
      line-height: 40px !important;
    }
  }
}

.rank-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  padding: 10px 10px 20px;

  .rank-item {
    display: flex;
    align-items: center;
    width: 50%;
    height: 20%;
    padding-right: 25px;

    .rank-item-sort {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 20px;
      color: #fff;
      font-size: 12px;
      background: #4c6277;
      border-radius: 50%;
    }

    .rank-item-name {
      flex-shrink: 0;
      width: 100px;
      padding: 0 4px 0;
      color: #fff;
      font-size: 14px;
    }

    .rank-item-progress {
      padding: 0 4px 0;
    }

    :global {
      .c7n-progress {
        padding: 3px !important;
        background: #264160 !important;
        border-radius: 50px !important;
      }

      .c7n-progress-inner {
        background: #264160 !important;

        .c7n-progress-bg {
          height: 8px !important;
        }
      }

      .c7n-progress-outer {
        display: flex;
        align-items: center;
      }

      .c7n-progress-line {
        font-size: 12px !important;
      }
    }

    .rank-item-quantity {
      flex-shrink: 0;
      width: 50px;
      padding: 0 5px 0;
      text-align: right;
    }

    .rank-item-percent {
      flex-shrink: 0;
      width: 30px;
      padding: 0 5px 0;
      text-align: right;
    }
  }
}

.sumary-list {
  display: flex;
  justify-content: space-around;
  height: 100px;

  .sumary-item-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #a6a6a6;
    font-weight: 600;
    font-size: 18px;
    background: url('../../assets/equipmentBoard/summary1.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }

  .sumary-item-2 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #47a5f2;
    font-weight: 600;
    font-size: 18px;
    background: url('../../assets/equipmentBoard/summary2-new.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }

  .sumary-item-3 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #f3be58;
    font-weight: 600;
    font-size: 18px;
    background: url('../../assets/equipmentBoard/summary3-new.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }

  .sumary-item-4 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    color: #03f8f9;
    font-weight: 600;
    font-size: 18px;
    background: url('../../assets/equipmentBoard/summary4-new.png') center center no-repeat;
    background-position: center;
    background-size: contain;
  }
}

.my-scroll-board-2 {
  :global {
    .dv-scroll-board {
      .header {
        .header-item {
          height: 2.5vw !important;
          font-weight: bold !important;
          font-size: 12px !important;
          line-height: 2.5vw !important;
        }
      }

      .rows {
        .row-item {
          font-size: 12px !important;
          // border: 1px solid #1e3e67;
        }
      }
    }
  }
}
.my-scroll-board-title{
    width: 100%;
    height: 7%;
    opacity: 1;
    /** 文本1 */
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 28.96px;
    color: rgba(0, 255, 244, 1);
    text-align: center;
    margin-bottom: 10px;
    vertical-align: top;
    padding: 0 10px;
    position: relative;
    :global {
      .c7n-pro-btn-raised:not(.c7n-pro-btn-primary):not(.c7n-pro-btn-disabled):not(.c7n-pro-btn-loading) {
        background-color: #ffffff22;
        color: #65ffff;
        position: absolute;
        left: 10px;
        top: 2px;
      }
      .c7n-pro-btn.c7n-pro-btn-default.c7n-pro-btn-raised:not(.c7n-pro-btn-disabled):not(.c7n-pro-btn-loading):hover {
        background-color: #ffffff55;
        color: #65ffff;
      }
    }
    
}

.my-chart-filter{
  width: 100%;
  // height: 7%;
  opacity: 1;
  font-size: 12px;
  letter-spacing: 0px;
  vertical-align: top;
  margin-top: 1%;
  margin-left: 15px;
  :global {
    .c7n-pro-radio-button {
      background-color: #65ffff22;
    }
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper,
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper.c7n-pro-radio-button:not(.c7n-pro-table-customization-select-view-option) .c7n-pro-radio:checked + .c7n-pro-radio-inner::after,
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper.c7n-pro-radio-button:not(.c7n-pro-table-customization-select-view-option) .c7n-pro-radio:checked + .c7n-pro-radio-inner + span {
      color: #65ffff;
    }
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper.c7n-pro-radio-button:not(.c7n-pro-table-customization-select-view-option).c7n-pro-radio-button:hover:not(.c7n-pro-radio-disabled) .c7n-pro-radio-inner {
      border-color: #65ffff;
    }
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper.c7n-pro-radio-button:not(.c7n-pro-table-customization-select-view-option).c7n-pro-radio-button:hover:not(.c7n-pro-radio-disabled) .c7n-pro-radio-label {
      color: #fff;
    }
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper.c7n-pro-radio-button:not(.c7n-pro-table-customization-select-view-option) .c7n-pro-radio:checked + .c7n-pro-radio-inner {
      background-color: #65ffff44;
      color: #65ffff;
      border-color: #65ffff !important;
    }
  }
}

.my-chart{
  width: 100%;
  height: 110%;
}

.my-scroll-board-table {
  :global {
    .dv-scroll-board .header .header-item:nth-child(1) {
      width: 90px !important;
    }
    .dv-scroll-board .rows .ceil:nth-child(1) {
      width: 90px !important;
    }
    .dv-scroll-board {
      .rows {
        .row-item {
          font-size: 14px !important;
          height: 45px !important;
          line-height: 45px !important;
        }
      }
    }

    .dv-scroll-board .rows .ceil {
      padding: 0 5px !important;
    }
  }
}

.checkSupplierText {
  display: block;
  margin-bottom: 20px; /* 留出足够的空间供背景图片显示 */
}
 
.checkSupplierImage {
  display: inline-block;
  width: 100px; /* 或者你想要的任何宽度 */
  height: 100px; /* 或者你想要的任何高度 */
  background-image: url('../../assets/equipmentBoard/1.png');
  background-size: cover; /* 背景图片覆盖整个span区域 */
  background-position: center; /* 图片居中显示 */
  background-repeat: no-repeat; /* 不重复背景图片 */
}

.dashboard-right-chart{  
  margin: 2%;
  border-radius: 50%; /* 圆形结构 */
  background-image: url('../../assets/equipmentBoard/cicle.png');
  background-position: 50% 10%; /* 背景图片居中 */
  background-repeat: no-repeat; /* 不重复背景图片 */
  background-size: 64% 82%;;
}

.dashboard-right-chart-full-screen{
  // margin:2%;
  border-radius: 50%; /* 圆形结构 */
  background-image: url('../../assets/equipmentBoard/cicle.png');
  // background-size: 70% 90%; /* 背景图片覆盖整个元素 */
  // background-repeat: no-repeat; /* 不重复背景图片 */
  // background-position: 50% 100%; /* 背景图片居中 */
  
  background-size: 61% 72%;
  background-repeat: no-repeat;
  background-position: 50% 25%;
}
.workOrder-info {
  height: 90%;
  display: flex;
  flex-grow: 1;
  overflow-y: scroll;
}

.person-status-content {
  border-radius: 6px;
  padding: 5px;
  text-align: center;
  margin-right: 10px;
  margin-bottom: 10px;
}

.person-status-content-normal {
  border: 1px solid #65ffff;
  color: #65ffff;
}

.person-status-content-warning {
  border: 1px solid #f94a53;
  color: #f94a53;
  background: #d94d5422;
}

.workOrder-content {
  flex: 1;
}
.workOrder-content > div {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: 0;
  .workOrder-table {
    flex-grow: 1;
    min-height: 0;
    height: 0;
    overflow: hidden;
    table {
      width: 100%;
      border-collapse: collapse;
      height: 100%;
      display: flex;
      flex-direction: column;
      th {
        padding: 1px 5px;
        background-color: #ffffff22;
        text-align: center;
      }

      tr td {
        text-align: center;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        line-height: 25px;
      }
      
      tbody {
        overflow-y: scroll;
        display: block;
        tr {
          display: table;
          width: 100%;
          table-layout: fixed;
        }
      }
      tbody tr:nth-child(even) {
        background-color: #ffffff22;
      }
      tbody tr:hover {
        background-color: #ffffff33;
      }
    }
    table thead {
      font-weight: 600;
      color: #65ffff;
      display: table;
      width: 100%;
      table-layout: fixed;
    }
  }
  .workOrder_form-item-label {
    color: #65ffffaa;
  }
}
