/**
 * AssetTransactionBasicType - 五种资产事务界面、领用单界面
 * @date: 2020-03-03
 * @author: zong<PERSON> <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component, Fragment } from 'react';
import { Bind } from 'lodash-decorators';
import { DataSet, Table, Button, Modal, TextField, Select } from 'choerodon-ui/pro';
import { isEmpty, split } from 'lodash';
import queryString from 'querystring';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import IconSelect from 'alm/components/IconSelect';
import QueryBar from 'alm/components/QueryBar';
import { tableDS, searchFormDS } from './Stores/ListDS';
import { searchTsFormDS, transactionDS } from './Stores/TransactionDS';
import { getPageConfig } from './assetUtils';
import { getModuleTitle } from './transactionInfoConfig';

const menuIconStyle = {
  width: 36,
  height: 36,
};
@formatterCollections({ code: ['aatn.assetTransactionBasicType', 'alm.common', 'alm.component'] })
class AssetTransactionBasicType extends Component {
  form;

  /**
   * state初始化
   * @param {props} props -参数
   */
  constructor(props) {
    super(props);
    const {
      location: { pathname },
    } = props;
    const tsType = split(split(pathname, '/aatn/asset-transaction-basic-type/')[1], '/')[0];
    const pageConfig = getPageConfig(tsType);
    this.pageConfig = pageConfig;
    this.searchFormDS = new DataSet(searchFormDS(pageConfig.basicCode));
    this.tableDS = new DataSet(tableDS([], this.searchFormDS));
    this.searchTsFormDS = new DataSet(searchTsFormDS());
    this.transactionDS = new DataSet(transactionDS(this.searchTsFormDS));
  }

  /**
   * 页面跳转
   * @param {string} id - 头id
   */
  @Bind()
  handleGotoDetail(record, isEdit) {
    const { history } = this.props;
    if (isEmpty(record)) {
      history.push({
        pathname: `/aatn/asset-transaction-basic-type/${this.pageConfig.routeType}/create`,
        state: {
          isEdit: true,
        },
      });
    } else {
      const tsTypeId = record.toJSONData().transactionTypeId;
      const { changeHeaderId } = record.toJSONData();
      history.push({
        pathname: `/aatn/asset-transaction-basic-type/${this.pageConfig.routeType}/detail/${tsTypeId}/${changeHeaderId}`,
        state: {
          isEdit,
        },
      });
    }
  }

  /**
   * 操作列渲染
   *
   * @returns
   * @memberof AssetTransactionBasicType
   */
  @Bind()
  operaRender({ record }) {
    return (
      <a
        disabled={!['DRAFT', 'REJECTED'].includes(record.get('processStatus'))}
        onClick={() => this.handleGotoDetail(record, true)}
      >
        {intl.get('hzero.common.button.edit').d('编辑')}
      </a>
    );
  }

  /**
   * 跳转详情渲染
   *
   * @returns
   * @memberof AssetTransactionBasicType
   */
  @Bind()
  toDetailRender({ record, text }) {
    return <a onClick={() => this.handleGotoDetail(record, false)}>{text}</a>;
  }

  /**
   * 图标显示
   *
   * @returns
   * @memberof AssetTransactionBasicType
   */
  @Bind()
  iconRender({ record }) {
    const { data } = record;
    return (
      <IconSelect
        border
        title={data.icon}
        iconTypeCode={data.iconTypeCode}
        type={data.icon}
        style={menuIconStyle}
      />
    );
  }

  /**
   *选择事务
   */
  @Bind()
  handleChoseTransaction() {
    this.transactionDS.setQueryParameter('basicTypeCode', this.pageConfig.basicCode);
    this.transactionDS.query();
    this.showTsModal();
  }

  /**
   * 显示事务模态框
   */
  @Bind()
  showTsModal() {
    const modalKey = Modal.key();
    Modal.open({
      key: modalKey,
      width: 800,
      title: intl
        .get(`aatn.assetTransactionBasicType.modal.assetTransactionBasicType`)
        .d('资产事务处理类型'),
      style: {
        width: 700,
      },
      children: (
        <Fragment>
          <Table
            key="assetTransactionBasicType"
            customizedCode="AORI.ASSET_TRANSACTION_BASIC_TYPE.TYPE"
            columns={this.tsColumns}
            dataSet={this.transactionDS}
            queryFieldsLimit={2}
          />
        </Fragment>
      ),
      onOk: this.modelOk,
    });
  }

  /**
   *资产列表 列
   */
  @Bind()
  get tsColumns() {
    return [{ name: 'transactionTypeCode' }, { name: 'transactionTypeName' }];
  }

  /**
   *事务模态框点击确定
   */
  @Bind()
  async modelOk() {
    const { history } = this.props;
    if (this.transactionDS.selected.length > 0) {
      history.push({
        pathname: `/aatn/asset-transaction-basic-type/${this.pageConfig.routeType}/create`,
        state: {
          transaction: this.transactionDS.selected[0].toJSONData(),
          isEdit: true,
        },
        search: queryString.stringify({
          transactionTypeId: this.transactionDS.selected[0].toJSONData().transactionTypeId,
        }),
      });
    } else {
      notification.warning({
        message: intl
          .get('aatn.assetTransactionBasicType.view.message.unchoseTransaction')
          .d('请选择一个资产事务'),
      });
      return false;
    }
  }

  get columns() {
    return [
      {
        name: 'icon',
        renderer: this.iconRender,
        width: 75,
        tooltip: 'overflow',
      },
      {
        name: 'changeNum',
        width: 150,
        renderer: this.toDetailRender,
        tooltip: 'overflow',
      },
      {
        name: 'titleOverview',
        tooltip: 'overflow',
      },
      {
        name: 'transactionTypeIdMeaning',
        width: 200,
        tooltip: 'overflow',
      },
      {
        name: 'processStatusMeaning',
        width: 200,
        tooltip: 'overflow',
      },
      {
        header: intl.get('hzero.common.button.action').d('操作'),
        renderer: this.operaRender,
        width: 100,
        lock: 'right',
      },
    ];
  }

  renderBar = props => {
    const {
      location: { pathname },
    } = this.props;
    const tsType = split(split(pathname, '/aatn/asset-transaction-basic-type/')[1], '/')[0];
    const pageConfig = getPageConfig(tsType);
    const queryProps = {
      ...props,
      moduleName: 'ASSET_TRANSACTION',
      systemFields: ['changeNum', 'titleOverview', 'processStatus'],
      defaultQueryFields: this.defaultQueryFields(),
      extQueryParams: {
        basicCode: pageConfig.basicCode,
      },
    };
    return <QueryBar {...queryProps} />;
  };

  defaultQueryFields = () => [
    <TextField name="changeNum" />,
    <TextField name="titleOverview" />,
    <Select name="processStatus" />,
  ];

  render() {
    return (
      <React.Fragment>
        <Header title={getModuleTitle(this.pageConfig.basicCode)}>
          <Button icon="add" color="primary" onClick={() => this.handleChoseTransaction()}>
            {intl.get('hzero.common.button.create').d('新建')}
          </Button>
        </Header>
        <Content>
          <Table
            key="assetTransactionBasicTypeList"
            customizedCode="AORI.ASSET_TRANSACTION_BASIC_TYPE.LIST"
            dataSet={this.tableDS}
            columns={this.columns}
            queryBar={this.renderBar}
          />
        </Content>
      </React.Fragment>
    );
  }
}

export default AssetTransactionBasicType;
