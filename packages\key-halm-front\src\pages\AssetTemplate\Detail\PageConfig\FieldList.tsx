import React, { useEffect, useMemo, useRef } from 'react';

import { DataSet, Table, Modal, Button, useModal, Lov } from 'choerodon-ui/pro/lib';
import {
  ColumnAlign,
  ColumnLock,
  ColumnProps,
  DragColumnAlign,
  TableProps,
} from 'choerodon-ui/pro/lib/table/interface';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { isNil } from 'lodash';
import { observer } from 'mobx-react';
import { useDataSet } from 'utils/hooks';
import notification from 'utils/notification';
import { yesOrNoRender, dateRender } from 'utils/renderer';
import ExtraFieldDrawer from 'alm/components/PageConfiguration/ExtraFieldDrawer';
import RelateExtraFieldList from 'alm/components/PageConfiguration/RelateExtraFieldList';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { fieldListDS, areaExchangeDS } from '../../Stores/pageConfig';
import { batchCreateFields } from '../../api';
import getLang from '../../Langs';

interface FieldListProps {
  areaConfId?: string;
  templateCode?: string;
}

const FieldList: React.FC<FieldListProps> = ({ areaConfId, templateCode }) => {
  const modal = useModal();
  const fieldListDs = useDataSet(() => new DataSet(fieldListDS()));
  const areaExchangeDs = useDataSet(() => new DataSet(areaExchangeDS()));
  const extraModalRef = useRef<ExtraFieldDrawer>(null);
  const relateModalRef = useRef<RelateExtraFieldList>(null);

  useEffect(() => {
    if (!isNil(areaConfId)) {
      areaExchangeDs.setState('templateCode', templateCode);
      areaExchangeDs.setState('currentAreaCode', areaConfId);

      fieldListDs.setQueryParameter('areaConfId', areaConfId);
      fieldListDs.query();
    }
  }, [areaConfId]);

  const handleOpenModal = (record: any, editFlag: boolean) => {
    const modalProps = {
      editFlag,
      scope: 'ASSET_TEMPLATE',
      currentData: {
        ...record,
        attrName: getLang('DEFAULT_VALUE'),
        attrFieldCode: 'defaultValue',
      },
    };
    modal.open({
      key: Modal.key(),
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      closable: true,
      drawer: true,
      style: {
        width: 450,
      },
      title: getLang('EDIT'),
      cancelText: getLang(editFlag ? 'CANCEL' : 'CLOSE'),
      okText: getLang('SAVE'),
      children: <ExtraFieldDrawer {...modalProps} ref={extraModalRef} />,
      footer: (okBtn, cancelBtn) => (
        <div>
          {cancelBtn}
          {editFlag && okBtn}
        </div>
      ),
      onOk: async () => {
        if (extraModalRef && extraModalRef.current) {
          const result = await extraModalRef.current.extraFormDS.validate();
          if (result) {
            handleUpdateExtraField();
          } else {
            return false;
          }
        }
      },
    });
  };

  const handleUpdateExtraField = async () => {
    await extraModalRef.current.extraFormDS.submit();
    fieldListDs.query();
  };

  const handleShowRelationModal = () => {
    const props = {
      currentTemplateCode: templateCode,
      scope: 'ASSET_SET',
    };

    modal.open({
      title: getLang('SELECTED_FIELD'),
      key: Modal.key(),
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      closable: true,
      okText: getLang('SURE'),
      cancelText: getLang('CANCEL'),
      children: <RelateExtraFieldList {...props} ref={relateModalRef} />,
      onOk: async () => {
        if (relateModalRef.current.relateExtraTableDS.selected.length > 0) {
          await createFields();
        } else {
          notification.error({ message: getLang('NO_SELECT') });
          return false;
        }
      },
    });
  };

  const createFields = async () => {
    const submitData = relateModalRef.current.relateExtraTableDS.selected.map(i => {
      i.set('width', 1);
      i.set('scopeCode', 'ASSET_SET');
      i.set('areaConfId', Number(areaConfId));
      i.set('creationDate', null);
      i.set('createdBy', null);
      i.set('lastUpdateDate', null);
      i.set('lastUpdatedBy', null);
      i.set('objectVersionNumber', null);
      i.set('_token', null);
      return i.toData();
    });
    await batchCreateFields(submitData);
    notification.success({});
    fieldListDs.query();
  };

  const handleChangeFieldArea = async value => {
    const updateData = fieldListDs.selected.map(record => ({
      ...record.toJSONData(),
      areaConfId: value.areaConfId,
      seqNum: (value?.maxSeqNum ?? 0) + 1,
    }));
    areaExchangeDs.reset();
    await batchCreateFields(updateData);
    notification.success({});
    fieldListDs.query();
  };

  const btns = useMemo(() => {
    return [
      <Lov
        noCache
        icon="compare_arrows"
        disabled={!fieldListDs.selected.length}
        dataSet={areaExchangeDs}
        name="area"
        mode={ViewMode.button}
        clearButton={false}
        onChange={handleChangeFieldArea}
        modalProps={{
          style: {
            width: 350,
          },
        }}
      >
        {getLang('MOVE_FIELD')}
      </Lov>,
      <Button icon="add" onClick={handleShowRelationModal}>
        {getLang('ADD')}
      </Button>,
    ];
  }, [areaConfId, fieldListDs.selected.length]);

  const handleDragEnd: TableProps['onDragEnd'] = async dataSet => {
    dataSet.forEach((item, index) => {
      item.set('seqNum', index + 1);
    });
    if (dataSet.updated.length > 0) {
      await fieldListDs.submit();
      fieldListDs.query();
    }
  };

  const handleDelete = async record => {
    const res = await fieldListDs.delete(
      record,
      record.get('fieldUsedFlag') ? getLang('DELETE_PROMPT') : undefined
    );
    if (res?.success) {
      fieldListDs.query();
    }
  };

  const columns = useMemo(
    (): ColumnProps[] => [
      {
        name: 'seqNum',
      },
      {
        name: 'fieldName',
      },
      {
        name: 'fieldCode',
      },
      {
        name: 'sourceCode',
      },
      {
        name: 'typeCode',
      },
      {
        name: 'defaultValue',
        renderer: ({ value, record }) => {
          const typeCode = record?.get('typeCode');
          let defaultValueMeaning = record?.get('defaultValueMeaning');
          if (['MULTI_SELECT', 'MULTI_LOV'].includes(typeCode) && !!defaultValueMeaning) {
            defaultValueMeaning = JSON.parse(defaultValueMeaning || '[]').join(',');
          }
          if (typeCode === 'SWITCH') {
            return yesOrNoRender(Number(value));
          } else if (
            [
              'SELECT',
              'LOV',
              'EMPLOYEE',
              'ORGANIZATION',
              'PARTNER',
              'PLATFORM_ORG',
              'MULTI_SELECT',
              'MULTI_LOV',
            ].includes(typeCode)
          ) {
            return <span>{defaultValueMeaning ?? '-'}</span>;
          } else if (typeCode === 'DATE_PICKER') {
            return dateRender(value);
          } else {
            return <span>{value ?? '-'}</span>;
          }
        },
      },
      {
        name: 'width',
      },
      {
        name: 'requiredFlag',
        width: 100,
        renderer: ({ value }) => yesOrNoRender(value),
      },
      {
        header: getLang('OPTION'),
        width: 120,
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          const editFlag = Boolean(record?.get('editFlag') ?? 1);
          return [
            <Button
              funcType={FuncType.link}
              onClick={() => handleOpenModal(record?.toData(), editFlag)}
            >
              {editFlag ? getLang('EDIT') : getLang('VIEW')}
            </Button>,
            <Button
              funcType={FuncType.link}
              color={ButtonColor.red}
              disabled={!editFlag}
              style={{ margin: '0 0 0 16px' }}
              onClick={() => handleDelete(record)}
            >
              {getLang('DELETE')}
            </Button>,
          ];
        },
      },
    ],
    []
  );
  return (
    <Table
      key="assetTemplate.fieldList"
      customizedCode="AORI.ASSET_TEMPLATE.SCOPE"
      dragColumnAlign={DragColumnAlign.left}
      rowDraggable
      buttons={btns}
      dataSet={fieldListDs}
      columns={columns}
      onDragEnd={handleDragEnd}
    />
  );
};

export default observer(FieldList);
