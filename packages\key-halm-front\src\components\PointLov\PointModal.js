import React from 'react';
import { Table, DataSet, Lov, Form, Button, TextField, Select } from 'choerodon-ui/pro';
import { Bind } from 'lodash-decorators';

import styles from './index.module.less';
import { tableDs, queryDs } from './pointModalDs';
import getLangs from './Langs';

export default class PointModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
    const { multiSelect, queryParams } = props;
    this.queryDs = new DataSet(queryDs(queryParams));
    this.tableDs = new DataSet(tableDs({ multiSelect, queryParams }));
  }

  @Bind
  handleSearch() {
    const queryData = this.queryDs?.current?.toData() || {};

    this.tableDs.setQueryParameter('pointName', queryData?.pointName);
    this.tableDs.setQueryParameter('pointCode', queryData?.pointCode);
    this.tableDs.setQueryParameter('pointType', queryData?.pointType);
    this.tableDs.setQueryParameter('pointObjectId', queryData?.pointObjectId);

    this.tableDs.query();
  }

  @Bind
  handleReset() {
    const queryData = this.queryDs?.current;
    if (queryData) {
      queryData.reset();
      this.handleSearch();
    }
  }

  get columns() {
    return [
      {
        name: 'pointName',
      },
      {
        name: 'pointCode',
      },
      {
        name: 'pointType',
      },
      {
        name: 'pointObjectName',
        tooltip: 'overflow',
      },
      {
        name: 'assetLocationName',
        tooltip: 'overflow',
      },
    ];
  }

  render() {
    return (
      <>
        <div className={styles['query-box']}>
          <div className={styles['query-form']}>
            <Form dataSet={this.queryDs} columns={2}>
              <TextField name="pointName" />
              <TextField name="pointCode" />
              <Select name="pointType" />
              <Lov name="pointObject" />
            </Form>
          </div>
          <div className={styles['query-buttons']}>
            <Button onClick={this.handleReset}>{getLangs('RESET')}</Button>
            <Button color="primary" onClick={this.handleSearch}>
              {getLangs('SEARCH')}
            </Button>
          </div>
        </div>
        <Table dataSet={this.tableDs} columns={this.columns} key="pointLov" />
      </>
    );
  }
}
