import React, { Component } from 'react';
import { withRouter } from 'react-router';
import axios from 'axios';
import classnames from 'classnames';
import { Bind } from 'lodash-decorators';
import { Button, Select, Form, Lov, TextArea } from 'choerodon-ui/pro';
import { Skeleton } from 'choerodon-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { HALM_ATN } from 'alm/utils/config';
import { setDSFields, handleDynamicTranslate } from 'alm/utils/dynamicFieldRender';
import queryString from 'querystring';
import { ASSET_TRANSACTION_BASIC_TYPE } from 'alm/pages/AssetTransactionBasicType/transactionInfoConfig';
import styles from './index.module.less';
import getLang from '../Langs';

const organizationId = getCurrentOrganizationId();
const apiPrefix = `${HALM_ATN}/v1`;

@withRouter
class Modal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      handleType: '', // 处理类型
      routeType: '', // 事务处理路由类型
      basicTypeCode: '', // 事务类型基础code
      transaction: {}, // 事务处理类型对象
      modalLoading: false,
    };
  }

  componentDidMount() {
    const { completeDS, employee, detail } = this.props;
    completeDS.current.set('countingLineId', detail.lineId);
    completeDS.current.set('handlePersonId', employee.employeeId);
    completeDS.current.set('handlePersonName', employee.employeeName);
  }

  componentWillUnmount() {
    const { completeDS } = this.props;
    completeDS.reset();
  }

  /**
   * 组合资产字段
   * @returns <T> assetObj
   */
  async handleComposeAssetObject() {
    const { detail, completeDS, lineDS } = this.props;
    let assetObj = {};
    const lineData = lineDS.toData();
    const currentLine = lineData.length > 1 ? lineData[1] : lineData[0];
    const { attrField = [] } = currentLine;
    const dynamicFields = setDSFields(
      attrField,
      'fieldName',
      'aliasFieldCode',
      'typeCode',
      'value',
      ''
    );
    const { resultData } = dynamicFields;
    assetObj.resultData = resultData;
    // 建立资产和盘点结果关联关系
    assetObj.countingLineId = detail.lineId;
    assetObj.assetSourceTypeCode = 'INVENTORY_PROFIT';
    assetObj.assetSourceDetail = detail.lineNumber;
    assetObj.description = completeDS.current.get('handleDescription');
    assetObj.handleDescription = completeDS.current.get('handleDescription');
    assetObj.countingPersonId = completeDS.current.get('countingPersonId');
    assetObj.countingPersonName = completeDS.current.get('countingPersonName');
    // [TODO] 暂时不传这两个字段
    assetObj.assetStatusId = undefined;
    assetObj.assetStatusIds = undefined;
    // 固定资产还需要传固定资产编号
    assetObj.financialNum = currentLine.financialNum;
    assetObj.assetName = currentLine?.assetName;
    assetObj.assetNum = currentLine?.assetNum;
    const sysObject = await handleDynamicTranslate(attrField, 'Asset', 'ASSET_SET');
    assetObj = { ...sysObject, ...assetObj };

    return assetObj;
  }

  /**
   * 下划线转驼峰
   */
  toCamelCaseVar(variable = '') {
    return variable.replace(/_+[a-zA-Z]/g, (str, index) =>
      index ? str.substr(-1).toUpperCase() : str
    );
  }

  /**
   * 拆分attribute字段的值,可能为xx@!@xxx
   * @param {*} text @!@前半段
   * @returns string
   */
  getIdField(text = '') {
    if (text === null) {
      return '';
    } else {
      return text.split('@!@')[0];
    }
  }

  /**
   * 拆分attribute字段的值,可能为xx@!@xxx
   * @param {*} text @!@后半段
   * @returns T
   */
  getMeaningField(text = '') {
    if (text === null) {
      return '';
    } else {
      return text.split('@!@').length > 1 ? text.split('@!@')[1] : text;
    }
  }

  @Bind()
  handleChangeHandleType(type) {
    this.setState({ handleType: type });
  }

  @Bind()
  async handleComplete() {
    const { completeDS } = this.props;
    const flag = await completeDS.validate();
    if (flag) {
      this.handleDeal(completeDS.toData());
    }
  }

  @Bind()
  async handleNext() {
    const { handleType, routeType, transaction, basicTypeCode } = this.state;
    const { completeDS, modal, detail, drawer } = this.props;
    const flag = await completeDS.validate();
    if (flag) {
      // 新建资产
      if (handleType === 'CREATE_ASSET') {
        const assetData = await this.handleComposeAssetObject();
        if (assetData.assetSetId) {
          try {
            const assetSet = await axios({
              url: `${apiPrefix}/${organizationId}/asset-class/${assetData.assetSetId}`,
              method: 'GET',
            });
            assetData.assetSet = assetSet;
          } catch (e) {
            notification.error({ message: e.message });
          }
        }
        const params = {
          ...assetData,
          otherSourceFlag: true,
        };

        this.props.history.push({
          pathname: `/aafm/equipment-asset/create`,
          search: queryString.stringify({ originData: JSON.stringify(params) }),
        });
        if (drawer) {
          drawer.close();
        }
        modal.close();
      } else {
        // 资产事务处理
        try {
          await axios({
            url: `${apiPrefix}/${organizationId}/counting-line/deal`,
            method: 'POST',
            data: {
              handleType,
              countingLineId: detail.lineId,
            },
          });
        } catch (e) {
          if (e && e.message) {
            notification.warning({ message: e.message, duration: null });
          }
          return;
        }
        const defaultValues = {
          basicTypeCode,
          countingLineId: detail.lineId,
          description: completeDS.current.get('handleDescription'),
          handleDescription: completeDS.current.get('handleDescription'),
          countingPersonId: completeDS.current.get('countingPersonId'),
          countingPersonName: completeDS.current.get('countingPersonName'),
        };
        this.props.history.push({
          pathname: `/aatn/asset-transaction-basic-type/${routeType}/create`,
          state: {
            transaction,
            isEdit: true,
            defaultValues,
            moduleSource: 'countingLine',
            taskId: detail.taskId,
            assets: [{ assetId: detail?.assetId }],
            assetConfig: {
              transactionTypeId: transaction?.transactionTypeId,
              assetId: detail?.assetId,
              lineId: detail?.lineId,
              taskTplId: detail?.taskTplId,
            },
          },
        });
        if (drawer) {
          drawer.close();
        }
        modal.close();
      }
    }
  }

  @Bind()
  handleClose() {
    const { modal } = this.props;
    modal.close();
  }

  async handleDeal(submitData = {}) {
    const { modal, onSearch, drawer } = this.props;
    try {
      await axios({
        url: `${apiPrefix}/${organizationId}/counting-line/deal`,
        method: 'POST',
        data: submitData.length > 0 ? submitData[0] : submitData,
      });
      notification.success();
      modal.close();
      drawer?.close();
      onSearch();
    } catch (e) {
      if (e && e.message) {
        notification.error({ message: e.message });
      }
    }
  }

  @Bind()
  async handleChangeTransType(record) {
    const { detail, completeDS } = this.props;
    completeDS.current.set('basicTypeCode', record?.basicTypeCode);
    const current =
      ASSET_TRANSACTION_BASIC_TYPE.find(i => i.basicCode === record.basicTypeCode) || {};
    this.setState({
      basicTypeCode: record?.basicTypeCode,
      routeType: current.routeType,
      transaction: record,
    });
    try {
      // 校验盘点清单和资产事务处理动态字段是否一致
      const res = await axios({
        url: `${apiPrefix}/${organizationId}/tp-change-headers/check-attribute`,
        method: 'POST',
        data: {
          countingLineId: detail.lineId,
          tenantId: organizationId,
          transactionTypeId: record.transactionTypeId,
        },
      });
      if (res && res.unsavedFieldFlag) {
        const message = `${res.unsavedField} ${getLang('CHECK_ATTRIBUTE')}`;
        notification.warning({ message, duration: null });
      }
    } catch (e) {
      if (e && e.message) {
        notification.error({ message: e.message });
      }
    }
  }

  @Bind()
  optionsFilter(record) {
    const { detail } = this.props;
    const current = record.toData();
    const status = detail.countingStatus;
    let flag = false;
    switch (current.value) {
      // 匹配资产
      case 'MATCH_ASSET':
        if (status === 'OVER_RAGE') {
          flag = true;
        }
        break;
      // 手工处理
      case 'BY_HAND':
        if (['DIFFERENT', 'OVER_RAGE', 'LOSS'].includes(status)) {
          flag = true;
        }
        break;
      // 新建资产
      case 'CREATE_ASSET':
        if (status === 'OVER_RAGE') {
          flag = true;
        }
        break;
      // 资产事务处理
      case 'ASSET_TRANS_PROCESS':
        if (['DIFFERENT', 'LOSS'].includes(status)) {
          flag = true;
        }
        break;

      default:
        break;
    }
    return flag;
  }

  render() {
    const { handleType, modalLoading } = this.state;
    const { completeDS } = this.props;
    const btns = [];
    switch (handleType) {
      // 匹配资产
      case 'MATCH_ASSET':
        btns.push(
          <Button color="primary" onClick={this.handleComplete}>
            {getLang('FINISH')}
          </Button>
        );
        break;
      // 手工处理
      case 'BY_HAND':
        btns.push(
          <Button color="primary" onClick={this.handleComplete}>
            {getLang('FINISH')}
          </Button>
        );
        break;
      // 新建资产
      case 'CREATE_ASSET':
        btns.push(
          <Button color="primary" onClick={this.handleNext}>
            {getLang('NEXT')}
          </Button>
        );
        break;
      // 资产事务处理
      case 'ASSET_TRANS_PROCESS':
        btns.push(
          <Button color="primary" onClick={this.handleNext}>
            {getLang('NEXT')}
          </Button>
        );
        break;
      default:
        break;
    }
    return (
      <Skeleton loading={modalLoading}>
        <Form dataSet={completeDS} labelWidth={140}>
          <Select
            name="handleType"
            onChange={val => this.handleChangeHandleType(val)}
            optionsFilter={this.optionsFilter}
          />
          {handleType === 'MATCH_ASSET' && (
            <Lov
              name="assetLov"
              onChange={record => completeDS.current.set('relateNum', record.assetNum)}
            />
          )}
          {handleType === 'ASSET_TRANS_PROCESS' && (
            <Lov
              name="transactionTypeLov"
              onChange={record => this.handleChangeTransType(record)}
            />
          )}
          <Lov name="employeeLov" />
          <TextArea name="handleDescription" />
        </Form>
        <div className={classnames(styles['modal-footer'])}>{btns}</div>
      </Skeleton>
    );
  }
}

export default Modal;
