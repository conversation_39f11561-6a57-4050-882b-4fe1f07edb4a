/**
 * 通用-多语言
 * <AUTHOR>
 * @date 2021-03-01
 * @version: 0.0.1
 * @copyright: Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';

const getCommonLangs = (key?: string) => {
  const LANGS = {
    // hzero.common
    PREVIEW: intl.get('hzero.common.preview').d('预览'),
    OPTION: intl.get('hzero.common.table.column.option').d('操作'),
    CREATE: intl.get('hzero.common.button.create').d('新建'),
    DELETE: intl.get('hzero.common.button.delete').d('删除'),
    REMOVE: intl.get('hzero.common.button.remove').d('移除'),
    SEARCH: intl.get('hzero.common.button.search').d('查询'),
    RESET: intl.get('hzero.common.button.reset').d('重置'),
    CANCEL: intl.get('hzero.common.button.cancel').d('取消'),
    CANCEL_EDIT: intl.get('hzero.common.button.cancelEdit').d('取消编辑'),
    IMPORT: intl.get('hzero.common.button.import').d('导入'),
    EXPORT: intl.get('hzero.common.button.export').d('导出'),
    CLEAR: intl.get('hzero.common.button.clear').d('清空'),
    CLEAN: intl.get('hzero.common.button.clean').d('清除'),
    EDIT: intl.get('hzero.common.button.edit').d('编辑'),
    SAVE: intl.get('hzero.common.button.save').d('保存'),
    SURE: intl.get('hzero.common.button.sure').d('确定'),
    CLOSE: intl.get('hzero.common.button.close').d('关闭'),
    BACK: intl.get('hzero.common.button.back').d('返回'),
    COLLECTED: intl.get('hzero.common.button.collected').d('收起查询'),
    VIEWMORE: intl.get('hzero.common.button.viewMore').d('更多查询'),
    ENABLE: intl.get('hzero.common.button.enable').d('启用'),
    DISABLE: intl.get('hzero.common.button.disable').d('禁用'),
    ADD: intl.get('hzero.common.button.add').d('新增'),
    DETAIL: intl.get('hzero.common.button.detail').d('详情'),
    FINISH: intl.get('hzero.common.button.finish').d('完成'),
    NEXT: intl.get('hzero.common.button.next').d('下一步'),
    ADD_CHILDREN: intl.get('hzero.common.button.addChildren').d('新增下级'),
    EXPAND: intl.get('hzero.common.button.expand').d('展开'),
    COLLAPSE: intl.get('hzero.common.button.up').d('收起'),
    EXPAND_ALL: intl.get('hzero.common.button.expandAll').d('全部展开'),
    COLLAPSE_ALL: intl.get('hzero.common.button.collapseAll').d('全部收起'),
    REFRESH: intl.get('hzero.common.button.refresh').d('刷新'),
    VIEW: intl.get('hzero.common.button.view').d('查看'),
    SUCCESS: intl.get('hzero.common.notification.success').d('操作成功！'),
    FAILED: intl.get('hzero.common.notification.failed').d('操作失败！'),
    DOWNLOAD: intl.get('hzero.common.button.download').d('下载'),
    ATTACHMENT: intl.get('hzero.common.upload.modal.title').d('附件'),
    NO: intl.get('hzero.common.status.no').d('否'),
    YES: intl.get('hzero.common.status.yes').d('是'),
    SUBMIT: intl.get('hzero.common.button.submit').d('提交'),
    REMARK: intl.get('hzero.common.remark').d('备注'),
    // hzero.c7nUI
    ITEMS_PER_PAGE: intl.get('hzero.c7nUI.Pagination.items_per_page').d('条/页'),
    SELECT_ALL: intl.get('hzero.c7nUI.Select.selectAll').d('全选'),
    FILTER_PLACEHOLDER: intl.get('hzero.c7nUI.Select.filterPlaceholder').d('输入文字以进行过滤'),
    EMPTY_TEXT: intl.get(`hzero.c7nUI.Table.emptyText`).d('暂无数据'),
    // alm.common
    LOADING: intl.get(`alm.common.view.loading`).d('加载中'),
    NO_MORE: intl.get(`alm.common.view.noMore`).d('无更多数据'),
    MUST_LESS: intl.get(`alm.common.view.validation.mustLess`).d('必须小于'),

    // alm.common.button
    APPROVE_HISTORY: intl.get(`alm.common.button.approveHistory`).d('审批历史'),
    END: intl.get(`alm.common.end`).d('结束'),
    COMPCHECK: intl.get(`alm.common.check`).d('验收'),
    UNABLE: intl.get(`alm.common.unable`).d('无法执行'),
    COMPLETE: intl.get(`alm.common.completed`).d('工作完成'),
    ASSIGN: intl.get(`alm.common.assign`).d('派工'),
    REASSIGN: intl.get(`alm.common.reassign`).d('改派'),
    CLOSE_WO: intl.get(`alm.common.closeWo`).d('结束工单'),
    WITHDRAW: intl.get(`alm.common.withdraw`).d('撤回'),
    START: intl.get(`alm.common.start`).d('开工'),
    TBR: intl.get(`alm.common.tbr`).d('需改派'),
    PAUSE: intl.get(`alm.common.pause`).d('暂停'),
    PAUSE_WORK: intl.get(`alm.common.pauseWork`).d('暂停作业'),
    FINISH_CHECK: intl.get(`alm.common.finishCheck`).d('完成检查'),
    START_CHECK: intl.get(`alm.common.startCheck`).d('开始检查'),
    SKIP: intl.get(`alm.common.skip`).d('跳过'),

    ASSIGN_COUNTING_PERSON: intl.get('alm.common.button.assignCountingPerson').d('指定盘点人员'),
    ASSIGN_COUNTING_ORG: intl.get('alm.common.button.assignCountingOrg').d('指定盘点组织'),
    BASIC_Tab: intl.get(`alm.common.view.tab.basicTab`).d('基本'),
    BASIC: intl.get(`alm.common.view.baseInfo`).d('基本信息'),
    UNCHANGED: intl.get(`alm.common.view.message.dataUnchanged`).d('数据未更改！'),
    NEW_TOP_LEVEL: intl.get(`alm.common.view.button.newTopLevel`).d('新建顶层'),
    EXPAND_OR_COLLAPSE: intl.get(`alm.common.view.button.expandOrCollapseAll`).d('全部展开/收起'),
    NO_SELECT: intl.get(`alm.common.view.message.noSelect`).d('请选择一条数据！'),
    DELETE_CONFIRM: intl.get(`alm.common.view.message.deleteConfirm`).d('确定删除吗？'),
    ADDRESS_AND_CONTACT: intl.get('alm.common.view.addressAndContact').d('地址与联系方式'),
    NOTICE: intl.get(`alm.common.view.message.notice`).d('提示'),
    WARN: intl.get(`alm.common.view.message.warn`).d('警告'),
    PLEASE_SELECT_EMP: intl.get(`alm.common.view.message.pleaseSelectEmp`).d('请选择员工'),
    PLEASE_CHECK_DATA: intl.get('alm.common.view.pleaseCheckData').d('请检查数据！'),
    PLEASE_SEARCH_KEYWORDS: intl
      .get('alm.common.view.message.pleaseSearchKeyword')
      .d('请搜索关键字'),
    PLEASE_SEL_DATA: intl.get(`alm.common.view.message.pleaseSelData`).d('请选择数据'),
    SEL_MOST_ONE_DATA: intl.get(`alm.common.view.message.SelMostOneData`).d('最多选择一条数据'),
    CONTINUE: intl.get('alm.common.button.continue').d('继续'),
    MORE_OPTIONS: intl.get('alm.common.button.moreOptions').d('更多操作'),
    QUANTITYGREATERTHAN0: intl
      .get('alm.common.view.message.quantityMustBeGreaterThan0')
      .d('数量必须大于0'),
    TASK_LIST_PLACEHOLDER: intl
      .get(`alm.common.view.message.taskListPlaceholder`)
      .d('输入盘点任务名称、编号'),
    DELETE_ATTACH: intl.get(`alm.common.deleteAttach`).d('删除附件'),
    DELETE_ATTACH_CFM: intl.get(`alm.common.deleteAttachCfm`).d('是否删除附件'),
    ATTACH: intl.get(`alm.common.attachment`).d('附件'),

    // alm.common title
    BASIC_INFO: intl.get(`alm.common.view.title.basicInfo`).d('基础信息'),
    ASSET_INFO: intl.get(`alm.common.view.title.assetInfo`).d('资产信息'),
    DETAILS: intl.get(`alm.common.view.title.details`).d('详细信息'),
    ADDITIONAL_FIELDS: intl.get(`alm.common.view.title.additionalFields`).d('附加字段'),

    // alm.common.model
    ICON: intl.get(`alm.common.model.icon`).d('图标'),
    ENABLED_FLAG: intl.get(`alm.common.model.enabledFlag`).d('是否启用'),
    DESCRIPTION: intl.get(`alm.common.model.description`).d('描述'),
    STATUS: intl.get(`alm.common.model.status`).d('状态'),
    ITEM_CATEGORY: intl.get(`alm.common.model.itemCategory`).d('物料类别'),
    STANDARD_ASSET: intl.get(`alm.common.model.standardAsset`).d('标准资产'),
    QUANTITY: intl.get(`alm.common.model.quantity`).d('数量'),
    LOCATION: intl.get(`alm.common.model.location`).d('位置'),
    STOREROOM: intl.get(`alm.common.model.storeroom`).d('库房'),
    LOCATOR: intl.get(`alm.common.model.locator`).d('货位'),
    SERIAL_NUM: intl.get(`alm.common.model.serialNum`).d('序号'),
    OPTION_TYPE: intl.get(`alm.common.model.optionType`).d('操作类型'),
    OPTION_WAY: intl.get(`alm.common.model.optionWay`).d('操作方式'),
    DOCUMENT_NUM: intl.get(`alm.common.model.documentNum`).d('单据编号'),
    DOCUMENT_TYPE: intl.get(`alm.common.model.documentType`).d('单据类型'),
    DOCUMENT_STATUS: intl.get(`alm.common.model.documentStatus`).d('单据状态'),
    PRINCIPAL: intl.get(`alm.common.model.principal`).d('负责人'),
    DEMAND_DATE: intl.get(`alm.common.model.demandDate`).d('需求日期'),
    ROW_STATUS: intl.get(`alm.common.model.rowStatus`).d('行状态'),
    EQUIPMENT_NUM: intl.get(`alm.common.model.equipmentNum`).d('设备编号'),
    EQUIPMENT_NAME: intl.get(`alm.common.model.equipmentName`).d('设备名称'),
    ASSET_TRANSACTION_TYPE: intl.get(`alm.common.model.assetTransactionType`).d('资产事务类型'),
    RECEIPT_OBJ_TYPE: intl.get(`alm.common.model.receiptObjType`).d('领用对象类型'),
    RECEIPT_OBJ: intl.get(`alm.common.model.receiptObj`).d('领用对象'),
    RECEIPT_NUM: intl.get(`alm.common.model.receiptNum`).d('领用数量'),
    REQUIRED_QTY: intl.get(`alm.common.model.requiredQty`).d('需求数量'),
    QTY_TAKEN: intl.get(`alm.common.model.qtyTaken`).d('已领用数量'),
    EMP_NAME: intl.get(`alm.common.model.empName`).d('员工名称'),
    EMPLOYEE_NUM: intl.get(`alm.common.model.employeeNum`).d('员工编码'),
    ORGANIZATION_NAME: intl.get(`alm.common.model.organizationName`).d('组织名称'),
    DEPARTMENT_NAME: intl.get(`alm.common.model.departmentName`).d('部门名称'),
    POSITION_NAME: intl.get(`alm.common.model.positionName`).d('岗位名称'),
    CODE: intl.get(`alm.common.model.code`).d('编码'),
    NAME: intl.get(`alm.common.model.name`).d('名称'),
    ORG_DEP_POS: intl.get('alm.common.model.unit.name').d('组织/部门/岗位'),
    GENER_OFFLINE_PKG: intl.get('alm.common.button.generOfflinePkg').d('生成离线包'),
    OFFLINE_PKG_CREATOR: intl.get(`alm.common.model.offlinePkgCreator`).d('离线包创建人'),
    OFFLINE_PKG_CREATION_DATE: intl
      .get(`alm.common.model.offlinePkgCreationDate`)
      .d('离线包创建时间'),
    COUNTING_PERSON: intl.get(`alm.common.model.countingPerson`).d('盘点人员'),
    COUNTING_ORG: intl.get(`alm.common.model.countingOrg`).d('盘点组织'),
    ASSET_CLASS: intl.get(`alm.common.model.assetClass`).d('资产分类'),
    ATTACH_UPLOAD: intl.get(`alm.common.model.attachmentUpload`).d('附件上传'),
    NEED_SIGN_FLAG: intl.get(`alm.common.model.needSignFlag`).d('需签到'),

    // alm.component
    FIELD_CONFIG: intl.get('alm.component.view.message.fieldConfig').d('页面配置'),
  };
  return key ? LANGS[key] : LANGS;
};

export default getCommonLangs;
